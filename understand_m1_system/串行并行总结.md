# Magentic-One Architecture Summary (m1_archetecture_sumary.md)

## 1. Core Conclusion / 核心结论
The magentic-one system dispatches sub-agents **serially (串行)** – only one sub-agent is asked to act at any moment.

## 2. Key Scheduling Module / 核心调度模块
* **File / 文件**: `autogen_agentchat/teams/_group_chat/_magentic_one/_magentic_one_orchestrator.py`
* **Class / 类**: `MagenticOneOrchestrator`
* **Responsibility / 作用**: Orchestrates the multi-agent workflow. It picks the next speaker, sends a `GroupChatRequestPublish` message, waits for that agent’s reply, then repeats.

## 3. Execution-Mode Analysis with Code Evidence / 执行模式解析（附代码证据）

**Serial logic** – excerpt showing only one sub-agent selected and triggered per step:
```428:440:autogen_agentchat/teams/_group_chat/_magentic_one/_magentic_one_orchestrator.py
# Request that the step be completed
next_speaker = progress_ledger["next_speaker"]["answer"]
# ... validity check omitted ...
participant_topic_type = self._participant_name_to_topic_type[next_speaker]
await self.publish_message(
    GroupChatRequestPublish(),
    topic_id=DefaultTopicId(type=participant_topic_type),
    cancellation_token=cancellation_token,
)
```
* `next_speaker` is **a single name**, not a list.
* The orchestrator sends **one** `GroupChatRequestPublish` message, waits for that agent’s response before moving on.

Internal parallelism (tool execution inside an agent) does **not** affect inter-agent scheduling. Example:
```1198:1210:autogen_agentchat/agents/_assistant_agent.py
results = await asyncio.gather(*[
    cls._execute_tool_call(...)
    for call in function_calls
])
```
This uses `asyncio.gather` to parallelise **tool calls** inside a single agent, independent of the orchestrator’s serial agent dispatch.

## 4. Workflow Visualisation / 工作流程可视化 (Mermaid)
```mermaid
sequenceDiagram
    participant User
    participant Orchestrator
    participant SubAgent_A
    participant SubAgent_B

    User->>Orchestrator: send task
    Orchestrator->>SubAgent_A: GroupChatRequestPublish
    SubAgent_A-->>Orchestrator: response
    Orchestrator->>SubAgent_B: GroupChatRequestPublish
    SubAgent_B-->>Orchestrator: response
    Orchestrator-->>User: final answer
```

## 5. Summary / 总结
In magentic-one, the `MagenticOneOrchestrator` ensures **strict turn-taking**: it determines one `next_speaker`, triggers that sub-agent, awaits completion, then proceeds. There is **no parallel fan-out** of the same task to multiple sub-agents. Any concurrency you observe comes from *intra-agent* parallel operations (e.g., tool calls).  
因此，在进行二次开发或性能调优时，如需真正的并行子任务，需要修改 orchestrator 逻辑或选择支持并行的 `GraphFlow` 团队实现。