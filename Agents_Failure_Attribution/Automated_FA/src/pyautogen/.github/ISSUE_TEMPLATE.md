### Description
<!-- A clear and concise description of the issue or feature request. -->

### Environment
- AutoGen version: <!-- Specify the AutoGen version (e.g., v0.2.0) -->
- Python version: <!-- Specify the Python version (e.g., 3.8) -->
- Operating System: <!-- Specify the OS (e.g., Windows 10, Ubuntu 20.04) -->

### Steps to Reproduce (for bugs)
<!-- Provide detailed steps to reproduce the issue. Include code snippets, configuration files, or any other relevant information. -->

1. Step 1
2. Step 2
3. ...

### Expected Behavior
<!-- Describe what you expected to happen. -->

### Actual Behavior
<!-- Describe what actually happened. Include any error messages, stack traces, or unexpected behavior. -->

### Screenshots / Logs (if applicable)
<!-- If relevant, include screenshots or logs that help illustrate the issue. -->

### Additional Information
<!-- Include any additional information that might be helpful, such as specific configurations, data samples, or context about the environment. -->

### Possible Solution (if you have one)
<!-- If you have suggestions on how to address the issue, provide them here. -->

### Is this a Bug or Feature Request?
<!-- Choose one: Bug | Feature Request -->

### Priority
<!-- Choose one: High | Medium | Low -->

### Difficulty
<!-- Choose one: Easy | Moderate | Hard -->

### Any related issues?
<!-- If this is related to another issue, reference it here. -->

### Any relevant discussions?
<!-- If there are any discussions or forum threads related to this issue, provide links. -->

### Checklist
<!-- Please check the items that you have completed -->
- [ ] I have searched for similar issues and didn't find any duplicates.
- [ ] I have provided a clear and concise description of the issue.
- [ ] I have included the necessary environment details.
- [ ] I have outlined the steps to reproduce the issue.
- [ ] I have included any relevant logs or screenshots.
- [ ] I have indicated whether this is a bug or a feature request.
- [ ] I have set the priority and difficulty levels.

### Additional Comments
<!-- Any additional comments or context that you think would be helpful. -->
