# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: ContribGraphRagTests

on:
  pull_request:
    branches: ["main"]
    paths:
      - "autogen/agentchat/contrib/graph_rag/**"
      - "test/agentchat/contrib/graph_rag/**"
      - ".github/workflows/contrib-tests.yml"
      - "setup.py"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.head_ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}
permissions:
  {}
  # actions: read
  # checks: read
  # contents: read
  # deployments: read
jobs:
  GraphRagIntegrationTest-FalkorDB-Ubuntu:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11"]
    services:
      falkordb:
        image: falkordb/falkordb:edge
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install packages and dependencies for all tests
        run: |
          python -m pip install --upgrade pip wheel
          pip install pytest
      - name: Install FalkorDB SDK when on linux
        run: |
          pip install -e .[graph-rag-falkor-db]
      - name: Set AUTOGEN_USE_DOCKER based on OS
        shell: bash
        run: |
          echo "AUTOGEN_USE_DOCKER=False" >> $GITHUB_ENV
      - name: Coverage
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
          AZURE_OPENAI_API_BASE: ${{ secrets.AZURE_OPENAI_API_BASE }}
          OAI_CONFIG_LIST: ${{ secrets.OAI_CONFIG_LIST }}
        run: |
          pip install pytest-cov>=5
          pytest test/agentchat/contrib/graph_rag/test_falkor_graph_rag.py --skip-openai
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests

  GraphRagIntegrationTest-Neo4j-Llmaindex-Ubuntu:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11"]
    services:
      neo4j:
        image: neo4j:latest
        ports:
          - 7687:7687
          - 7474:7474
        env:
          NEO4J_AUTH: neo4j/password
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install packages and dependencies for all tests
        run: |
          python -m pip install --upgrade pip wheel
          pip install pytest
      - name: Install Neo4j and Llama-index when on linux
        run: |
          pip install -e .[neo4j_graph_rag]
      - name: Set AUTOGEN_USE_DOCKER based on OS
        shell: bash
        run: |
          echo "AUTOGEN_USE_DOCKER=False" >> $GITHUB_ENV
      - name: Coverage
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
          AZURE_OPENAI_API_BASE: ${{ secrets.AZURE_OPENAI_API_BASE }}
          OAI_CONFIG_LIST: ${{ secrets.OAI_CONFIG_LIST }}
        run: |
          pip install pytest-cov>=5
          pytest test/agentchat/contrib/graph_rag/test_neo4j_graph_rag.py --skip-openai
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
