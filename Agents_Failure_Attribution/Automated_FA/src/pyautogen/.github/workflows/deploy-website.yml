name: docs

on:
  pull_request:
    branches: [main]
    path:
      - "autogen/*"
      - "website/*"
      - ".github/workflows/deploy-website.yml"
  push:
    branches: [main]
    path:
      - "autogen/*"
      - "website/*"
      - ".github/workflows/deploy-website.yml"
  workflow_dispatch:
  merge_group:
    types: [checks_requested]
permissions:
  contents: write
jobs:
  checks:
    if: github.event_name != 'push'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: website
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true
      - uses: actions/setup-node@v4
        with:
          node-version: 18.x
      - name: setup python
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"
      - name: pydoc-markdown install
        run: |
          python -m pip install --upgrade pip
          pip install pydoc-markdown pyyaml termcolor
          # Pin databind packages as version 4.5.0 is not compatible with pydoc-markdown.
          pip install databind.core==4.4.2 databind.json==4.4.2
      - name: pydoc-markdown run
        run: |
          pydoc-markdown
      - name: quarto install
        working-directory: ${{ runner.temp }}
        run: |
          wget -q https://github.com/quarto-dev/quarto-cli/releases/download/v1.5.23/quarto-1.5.23-linux-amd64.tar.gz
          tar -xzf quarto-1.5.23-linux-amd64.tar.gz
          echo "$(pwd)/quarto-1.5.23/bin/" >> $GITHUB_PATH
      - name: Process notebooks
        run: |
          python process_notebooks.py render
      - name: Test Build
        run: |
          if [ -e yarn.lock ]; then
          yarn install --frozen-lockfile --ignore-engines
          yarn build
          elif [ -e package-lock.json ]; then
          npm ci
          npm run build
          else
          npm i --legacy-peer-deps
          npm run build
          fi
  gh-release:
    if: github.event_name != 'pull_request'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: website
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true
      - uses: actions/setup-node@v4
        with:
          node-version: 18.x
      - name: setup python
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"
      - name: pydoc-markdown install
        run: |
          python -m pip install --upgrade pip
          pip install pydoc-markdown pyyaml termcolor
          # Pin databind packages as version 4.5.0 is not compatible with pydoc-markdown.
          pip install databind.core==4.4.2 databind.json==4.4.2
      - name: pydoc-markdown run
        run: |
          pydoc-markdown
      - name: quarto install
        working-directory: ${{ runner.temp }}
        run: |
          wget -q https://github.com/quarto-dev/quarto-cli/releases/download/v1.5.23/quarto-1.5.23-linux-amd64.tar.gz
          tar -xzf quarto-1.5.23-linux-amd64.tar.gz
          echo "$(pwd)/quarto-1.5.23/bin/" >> $GITHUB_PATH
      - name: Process notebooks
        run: |
          python process_notebooks.py render
      - name: Build website
        run: |
          if [ -e yarn.lock ]; then
          yarn install --frozen-lockfile --ignore-engines
          yarn build
          elif [ -e package-lock.json ]; then
          npm ci
          npm run build
          else
          npm i --legacy-peer-deps
          npm run build
          fi
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          # Build output to publish to the `gh-pages` branch:
          publish_dir: ./website/build
