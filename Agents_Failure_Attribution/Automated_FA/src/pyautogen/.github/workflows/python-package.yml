# This workflows will build and upload a Python Package using Twine when a release is published
# Conda-forge bot will pick up new PyPI version and automatically create new version
# For more information see: https://help.github.com/en/actions/language-and-framework-guides/using-python-with-github-actions#publishing-to-package-registries

name: python-package

on:
  release:
    types: [published]
permissions: {}
  # actions: read
  # checks: read
  # contents: read
  # deployments: read
jobs:
  deploy:
    strategy:
      matrix:
        os: ['ubuntu-latest']
        python-version: [3.10]
    runs-on: ${{ matrix.os }}
    environment: package
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # - name: Cache conda
      #   uses: actions/cache@v4
      #   with:
      #     path: ~/conda_pkgs_dir
      #     key: conda-${{ matrix.os }}-python-${{ matrix.python-version }}-${{ hashFiles('environment.yml') }}
      # - name: Setup Miniconda
      #   uses: conda-incubator/setup-miniconda@v2
      #   with:
      #     auto-update-conda: true
      #     auto-activate-base: false
      #     activate-environment: hcrystalball
      #     python-version: ${{ matrix.python-version }}
      #     use-only-tar-bz2: true
      - name: Install from source
        # This is required for the pre-commit tests
        shell: pwsh
        run: pip install .
      # - name: Conda list
      #   shell: pwsh
      #   run: conda list
      - name: Build pyautogen
        shell: pwsh
        run: |
          pip install twine
          python setup.py sdist bdist_wheel
      - name: Publish pyautogen to PyPI
        env:
          TWINE_USERNAME: ${{ secrets.PYAUTOGEN_PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.PYAUTOGEN_PYPI_PASSWORD }}
        shell: pwsh
        run: twine upload dist/*pyautogen*
      - name: Build autogen
        shell: pwsh
        run: |
          python setup_autogen.py sdist bdist_wheel
      - name: Publish autogen to PyPI
        env:
          TWINE_USERNAME: ${{ secrets.AUTOGEN_PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.AUTOGEN_PYPI_PASSWORD }}
        shell: pwsh
        run: twine upload dist/autogen*
      - name: Build ag2
        shell: pwsh
        run: |
          python setup_ag2.py sdist bdist_wheel
      - name: Publish ag2 to PyPI
        env:
          TWINE_USERNAME: ${{ secrets.AUTOGEN_PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.AUTOGEN_PYPI_PASSWORD }}
        shell: pwsh
        run: twine upload dist/ag2*
