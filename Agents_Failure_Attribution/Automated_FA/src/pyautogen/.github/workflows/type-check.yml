name: Type check
# see: https://help.github.com/en/actions/reference/events-that-trigger-workflows
on: # Trigger the workflow on pull request or merge
  pull_request:
  merge_group:
    types: [checks_requested]
defaults:
  run:
    shell: bash
permissions: {}
jobs:
  type-check:
    strategy:
      fail-fast: true
      matrix:
        version: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.version }}
      # All additional modules should be defined in setup.py
      - run: pip install ".[types]"
      # Any additional configuration should be defined in pyproject.toml
      - run: |
          mypy
