# Maintainers

## Here is a list of maintainers for the AutoGen project.

| Name            | GitHub Handle                                              | Organization           | Features                                |
|-----------------|------------------------------------------------------------|------------------------|-----------------------------------------|
| <PERSON><PERSON>      | [qingyun-wu](https://github.com/qingyun-wu)                | Penn State University  | all, alt-models, autobuilder            |
| <PERSON>        | [sonichi](https://github.com/sonichi)                      | -                      | all                                     |
| <PERSON>        | [marklysze](https://github.com/marklysze)                  | -                      | alt-models, group chat                  |
| Hrushikesh Dokala | [Hk669](https://github.com/Hk669)                        | -                      | alt-models, swebench, logging, rag      |
| <PERSON><PERSON>       | [LeoLjl](https://github.com/LeoLjl)                       | Penn State University  | autobuild, group chat                   |
| Shaokun Zhang   | [skzhang1](https://github.com/skzhang1)                    | Penn State University  | AgentOptimizer, Teachability            |
| Yixuan Zhai     | [randombet](https://github.com/randombet)                  | Meta                   | group chat, sequential_chats, rag       |
| Yiran Wu        | [yiranwu0](https://github.com/yiranwu0)                    | Penn State University  | alt-models, group chat, logging, infra  |
| Linxin Song     | [LinxinS97](https://github.com/LinxinS97)                  | University of Southern California | autobuild, group chat       |
| Jieyu Zhang     | [JieyuZ2](https://jieyuz2.github.io/)                  | University of Washington | autobuild, group chat       |
| Justin Trugman     | [jtrugman](https://github.com/jtrugman)                  | BetterFutureLabs | GPTAssistantAgent, Tool overwrite, Instruction Overwrite     |
| Gregory Fanous  | [gregory-fanous](https://github.com/gregory-fanous)         | -                     | teachability, rag, tool calling     |
| Davor Runje     | [davorrunje](https://github.com/davorrunje)                | airt.ai                | Tool calling, IO                        |
| Aaron Ward     | [AaronWard](https://github.com/AaronWard)                | yappstore.ai                | all                      |
| Rudy Wu     | [rudyalways](https://github.com/rudyalways)                | Google                | all, group chats, sequential chats                |
| Haiyang Li  | [ohdearquant](https://github.com/ohdearquant)               | -               | all, sequential chats, structured output, low-level|
| Eric Moore  | [emooreatx](https://github.com/emooreatx)               | IBM               | all|
| Evan David    | [evandavid1](https://github.com/evandavid1)                | -                      | all |
| Tvrtko Sternak  | [sternakt](https://github.com/sternakt)               | airt.ai               | structured output |
| Jiacheng Shang  | [Eric-Shang](https://github.com/Eric-Shang)               | Toast              | RAG |

**Pending Maintainers list (Marked with \*, Waiting for explicit approval from the maintainers)**
| Name            | GitHub Handle                                              | Organization           | Features                                |
|-----------------|------------------------------------------------------------|------------------------|-----------------------------------------|
| Olaoluwa Ademola Salami * | [olaoluwasalami](https://github.com/olaoluwasalami)  | DevOps Engineer        |                                          |
| Rajan Chari *    | [rajan-chari](https://github.com/rajan-chari)                | Microsoft Research                | CAP                        |

## I would like to join this list. How can I help the project?
> We're always looking for new contributors to join our team and help improve the project. For more information, please refer to our [CONTRIBUTING](https://ag2ai.github.io/ag2/docs/contributor-guide/contributing) guide.


## Are you missing from this list?
> Please open a PR to help us fix this.

## Acknowledgements
This template was adapted from [GitHub Template Guide](https://github.com/cezaraugusto/github-template-guidelines/blob/master/.github/CONTRIBUTORS.md) by [cezaraugusto](https://github.com/cezaraugusto).
