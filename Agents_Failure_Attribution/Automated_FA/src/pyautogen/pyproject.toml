[metadata]
license_file = "LICENSE"
description-file = "README.md"


[tool.pytest.ini_options]
addopts = '--cov=. --cov-append --cov-branch --cov-report=xml -m "not conda"'
markers = ["conda: test related to conda forge distribution"]

[tool.black]
# https://github.com/psf/black
line-length = 120
exclude = "(.eggs|.git|.hg|.mypy_cache|.venv|_build|buck-out|build|dist)"


[tool.ruff]
line-length = 120

[tool.ruff.lint]
# Enable Pyflakes `E` and `F` codes by default.
select = [
    "E",
    "W", # see: https://pypi.org/project/pycodestyle
    "F", # see: https://pypi.org/project/pyflakes
    #    "D",  # see: https://pypi.org/project/pydocstyle
    #    "N",  # see: https://pypi.org/project/pep8-naming
    #    "S",  # see: https://pypi.org/project/flake8-bandit
    "I", # see: https://pypi.org/project/isort/
]
ignore = ["E501", "F401", "F403", "C901"]

# Exclude a variety of commonly ignored directories.
exclude = [
    ".eggs",
    ".git",
    ".mypy_cache",
    ".ruff_cache",
    "__pypackages__",
    "_build",
    "build",
    "dist",
    "docs",
    # This file needs to be either upgraded or removed and therefore should be
    # ignore from type checking for now
    "math_utils\\.py$",
    "**/cap/py/autogencap/proto/*",
]
unfixable = ["F401"]

[tool.ruff.lint.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.mypy]
files = [
    "autogen/logger",
    "autogen/exception_utils.py",
    "autogen/coding",
    "autogen/oai/openai_utils.py",
    "autogen/_pydantic.py",
    "autogen/function_utils.py",
    "autogen/io",
    "test/test_pydantic.py",
    "test/test_function_utils.py",
    "test/io",
]
exclude = [
    "autogen/math_utils\\.py",
    "autogen/oai/completion\\.py",
    "autogen/agentchat/contrib/compressible_agent\\.py",
    "autogen/agentchat/contrib/math_user_proxy_agent.py",
    "autogen/oai/openai_utils.py",
]

strict = true
python_version = "3.8"
ignore_missing_imports = true
install_types = true
non_interactive = true
plugins = ["pydantic.mypy"]

# remove after all files in the repo are fixed
follow_imports = "silent"

# from https://blog.wolt.com/engineering/2021/09/30/professional-grade-mypy-configuration/
disallow_untyped_defs = true
no_implicit_optional = true
check_untyped_defs = true
warn_return_any = true
show_error_codes = true
warn_unused_ignores = true

disallow_incomplete_defs = true
disallow_untyped_decorators = true
disallow_any_unimported = true
