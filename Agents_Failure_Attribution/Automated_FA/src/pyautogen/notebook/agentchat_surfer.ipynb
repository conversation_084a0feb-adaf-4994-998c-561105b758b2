{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# WebSurferAgent\n", "\n", "AutoGen provides a proof-of-concept WebSurferAgent that can command a simple text-based browser (similar to [Lynx](https://en.wikipedia.org/wiki/Lynx_(web_browser))) to search the web, visit pages, navigate within pages, download files, etc. The browsing is stateful, meaning that browsing history, viewport state, and other details are maintained throughout the conversation. \n", "\n", "This work was largely inspired by OpenAI's [WebGPT](https://openai.com/research/webgpt) project from December 2021. \n", "\n", "## Requirements\n", "\n", "AutoGen requires `Python>=3.8`. To run this notebook example, please install AutoGen with the optional `websurfer` dependencies:\n", "```bash\n", "pip install \"autogen[websurfer]\"\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%pip install --quiet \"autogen[websurfer]\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Set your API Endpoint\n", "\n", "The [`config_list_from_json`](https://ag2ai.github.io/ag2/docs/reference/oai/openai_utils#config_list_from_json) function loads a list of configurations from an environment variable or a json file.\n", "\n", "It first looks for environment variable \"OAI_CONFIG_LIST\" which needs to be a valid json string. If that variable is not found, it then looks for a json file named \"OAI_CONFIG_LIST\". It filters the configs by models (you can filter by other keys as well).\n", "\n", "The WebSurferAgent uses a combination of models. GPT-4 and GPT-3.5-turbo-16k are recommended.\n", "\n", "Your json config should look something like the following:\n", "```json\n", "[\n", "    {\n", "        \"model\": \"gpt-4\",\n", "        \"api_key\": \"<your OpenAI API key here>\"\n", "    },\n", "    {\n", "        \"model\": \"gpt-3.5-turbo-16k\",\n", "        \"api_key\": \"<your OpenAI API key here>\"\n", "    }\n", "]\n", "```\n", "\n", "If you open this notebook in colab, you can upload your files by clicking the file icon on the left panel and then choose \"upload file\" icon.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import autogen  # noqa: E402\n", "\n", "llm_config = {\n", "    \"timeout\": 600,\n", "    \"cache_seed\": 44,  # change the seed for different trials\n", "    \"config_list\": autogen.config_list_from_json(\n", "        \"OAI_CONFIG_LIST\",\n", "        filter_dict={\"model\": [\"gpt-4\", \"gpt-4-0613\", \"gpt-4-32k\", \"gpt-4-32k-0613\", \"gpt-4-1106-preview\"]},\n", "    ),\n", "    \"temperature\": 0,\n", "}\n", "\n", "summarizer_llm_config = {\n", "    \"timeout\": 600,\n", "    \"cache_seed\": 44,  # change the seed for different trials\n", "    \"config_list\": autogen.config_list_from_json(\n", "        \"OAI_CONFIG_LIST\",\n", "        filter_dict={\"model\": [\"gpt-3.5-turbo-1106\", \"gpt-3.5-turbo-16k-0613\", \"gpt-3.5-turbo-16k\"]},\n", "    ),\n", "    \"temperature\": 0,\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configure Bing\n", "\n", "For WebSurferAgent to be reasonably useful, it needs to be able to search the web -- and that means it needs a Bing API key. \n", "You can read more about how to get an API on the [Bing Web Search API](https://www.microsoft.com/en-us/bing/apis/bing-web-search-api) page.\n", "\n", "Once you have your key, either set it as the `BING_API_KEY` system environment variable, or simply input your key below.\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os  # noqa: E402\n", "\n", "bing_api_key = os.environ[\"BING_API_KEY\"]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Construct Agents\n", "\n", "We now create out WebSurferAgent, and a UserProxyAgent to surf the web. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": true}, "outputs": [], "source": ["from autogen.agentchat.contrib.web_surfer import WebSurferAgent  # noqa: E402\n", "\n", "web_surfer = WebSurferAgent(\n", "    \"web_surfer\",\n", "    llm_config=llm_config,\n", "    summarizer_llm_config=summarizer_llm_config,\n", "    browser_config={\"viewport_size\": 4096, \"bing_api_key\": bing_api_key},\n", ")\n", "\n", "user_proxy = autogen.UserProxyAgent(\n", "    \"user_proxy\",\n", "    human_input_mode=\"NEVER\",\n", "    code_execution_config=False,\n", "    default_auto_reply=\"\",\n", "    is_termination_msg=lambda x: True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Navigational search, scroll, answer questions\n", "- Search for Microsoft's wikipedia page, then naviagate to it\n", "- Scroll down\n", "- Answer questions about the content"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to web_surfer):\n", "\n", "Find Microsoft's Wikipedia page.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION navigational_web_search...\u001b[0m\n", "\u001b[33mweb_surfer\u001b[0m (to user_proxy):\n", "\n", "Address: https://en.wikipedia.org/wiki/Microsoft\n", "Title: Microsoft - Wikipedia\n", "Viewport position: Showing page 1 of 64.\n", "=======================\n", "# Microsoft\n", "\n", "American multinational technology corporation\n", "\n", "Microsoft Corporation| [A square divided into four sub-squares, colored red-orange, green, yellow and blue (clockwise), with the company name appearing to its right](/wiki/File:Microsoft_logo_(2012).svg) |\n", "| Building 92 on the [Microsoft Redmond campus](/wiki/Microsoft_Redmond_campus \"Microsoft Redmond campus\") |\n", "| Type | [Public](/wiki/Public_company \"Public company\") |\n", "| [Traded as](/wiki/Ticker_symbol \"Ticker symbol\") | * [Nasdaq](/wiki/Nasdaq \"Nasdaq\"): [MSFT](https://www.nasdaq.com/market-activity/stocks/msft)\n", "* [Nasdaq-100](/wiki/Nasdaq-100 \"Nasdaq-100\") component\n", "* [DJIA](/wiki/Dow_Jones_Industrial_Average \"Dow Jones Industrial Average\") component\n", "* [S&P 100](/wiki/S%26P_100 \"S&P 100\") component\n", "* [S&P 500](/wiki/S%26P_500 \"S&P 500\") component\n", " |\n", "| [ISIN](/wiki/International_Securities_Identification_Number \"International Securities Identification Number\") | [US5949181045](https://isin.toolforge.org/?language=en&isin=US5949181045) |\n", "| Industry | [Information technology](/wiki/Information_technology \"Information technology\") |\n", "| Founded | April 4, 1975; 48 years ago (1975-04-04) in [Albuquerque, New Mexico](/wiki/Albuquerque,_New_Mexico \"Albuquerque, New Mexico\"), U.S. |\n", "| Founders | * [<PERSON>](/wiki/<PERSON> \"Bill Gates\")\n", "* [<PERSON>](/wiki/<PERSON> \"<PERSON>\")\n", " |\n", "| Headquarters | [One Microsoft Way](/wiki/Microsoft_campus \"Microsoft campus\")[Redmond, Washington](/wiki/Redmond,_Washington \"Redmond, Washington\"), U.S. |\n", "| Area served | Worldwide |\n", "| Key people | * [<PERSON><PERSON><PERSON>](/wiki/<PERSON><PERSON><PERSON>_<PERSON> \"<PERSON><PERSON><PERSON>\")([Chairman](/wiki/Chairman \"Chairman\") & [CEO](/wiki/Chief_executive_officer \"Chief executive officer\"))\n", "* [<PERSON>](/wiki/<PERSON>_<PERSON>_(American_lawyer) \"<PERSON> (American lawyer)\")([Vice Chairman](/wiki/Vice-Chairman \"Vice-Chairman\") & [President](/wiki/President_(corporate_title) \"President (corporate title)\"))\n", "* [<PERSON>](/wiki/<PERSON> \"<PERSON>\")([technical adviser](/wiki/Adviser \"Adviser\"))\n", " |\n", "| Products | * [Software development](/wiki/Software_development \"Software development\")\n", "* [Computer hardware](/wiki/Computer_hardware \"Computer hardware\")\n", "* [Consumer electronics](/wiki/Consumer_electronics \"Consumer electronics\")\n", "* [Social networking service](/wiki/Social_networking_service \"Social networking service\")\n", "* [Cloud computing](/wiki/Cloud_computing \"Cloud computing\")\n", "* [Video games](/wiki/Video_game_industry \"Video game industry\")\n", "* [Internet](/wiki/Internet \"Internet\")\n", "* [Corporate venture capital](/wiki/Corporate_venture_capital \"Corporate venture capital\")\n", " |\n", "| Brands | \n", "* [Windows](/wiki/Microsoft_Windows \"Microsoft Windows\")\n", "* [Microsoft 365](/wiki/Microsoft_365 \"Microsoft 365\")\n", "* [Skype](/wiki/Skype \"Skype\")\n", "* [Visual Studio](/wiki/Visual_Studio \"Visual Studio\")\n", "* [Xbox](/wiki/Xbox \"Xbox\")\n", "* [Dynamics](/wiki/Microsoft_Dynamics_365 \"Microsoft Dynamics 365\")\n", "* [Surface](/wiki/Microsoft_Surface \"Microsoft Surface\")\n", "\n", " |\n", "| Services | \n", "* [Edge](/wiki/Microsoft_Edge \"Microsoft Edge\")\n", "* [Azure](/wiki/Microsoft_Azure \"Microsoft Azure\")\n", "* [Bing](/wiki/Microsoft_Bing \"Microsoft Bing\")\n", "* [LinkedIn](/wiki/LinkedIn \"LinkedIn\")\n", "* [Yammer](/wiki/Yammer \"Yammer\")\n", "* [Microsoft 365](/wiki/Microsoft_365 \"Microsoft 365\")\n", "* [OneDrive](/wiki/OneDrive \"OneDrive\")\n", "* [Outlook](/wiki/Microsoft_Outlook \"Microsoft Outlook\")\n", "* [GitHub](/wiki/GitHub \"GitHub\")\n", "* [Microsoft Store](/wiki/Microsoft_Store_(digital) \"Microsoft Store (digital)\")\n", "* [Windows Update](/wiki/Windows_Update \"Windows Update\")\n", "* [Xbox Game Pass](/wiki/Xbox_Game_Pass \"Xbox Game Pass\")\n", "* [Xbox network](/wiki/Xbox_network \"Xbox network\")\n", "\n", " |\n", "| Revenue | Increase [US$](/wiki/United_States_dollar \"United States dollar\")211.9 billion (2023) |\n", "| [Operating income](/wiki/Earnings_before_interest_and_taxes \"Earnings before interest and taxes\") | Increase US$88.5 billion (2023) |\n", "| [Net income](/wiki/Net_income \"Net income\") | Increase US$73.4 billion (2023) |\n", "| [Total assets](/wiki/Asset \"Asset\") | Increase US$411.9 billion (2023) |\n", "| [Total equity](/wiki/Equity_(finance) \"Equity \n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["task1 = \"\"\"Find Microsoft's Wikipedia page.\"\"\"\n", "user_proxy.initiate_chat(web_surfer, message=task1, clear_history=False)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to web_surfer):\n", "\n", "Scroll down.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION page_down...\u001b[0m\n", "\u001b[33mweb_surfer\u001b[0m (to user_proxy):\n", "\n", "Address: https://en.wikipedia.org/wiki/Microsoft\n", "Title: Microsoft - Wikipedia\n", "Viewport position: Showing page 2 of 64.\n", "=======================\n", "(finance)\") | Increase US$206.2 billion (2023) |\n", "| Number of employees | 238,000 (2023) |\n", "| [Divisions](/wiki/Division_(business) \"Division (business)\") | \n", "* [Microsoft Engineering Groups](/wiki/Microsoft_engineering_groups \"Microsoft engineering groups\")\n", "* [Microsoft Digital Crimes Unit](/wiki/Microsoft_Digital_Crimes_Unit \"Microsoft Digital Crimes Unit\")\n", "* [Microsoft Press](/wiki/Microsoft_Press \"Microsoft Press\")\n", "* [Microsoft Japan](/wiki/Microsoft_Japan \"Microsoft Japan\")\n", "* [Microsoft Gaming](/wiki/Microsoft_Gaming \"Microsoft Gaming\")\n", "\n", " |\n", "| [Subsidiaries](/wiki/Subsidiary \"Subsidiary\") | \n", "* [GitHub](/wiki/GitHub \"GitHub\")\n", "* [LinkedIn](/wiki/LinkedIn \"LinkedIn\")\n", "* [Metaswitch](/wiki/Metaswitch \"Metaswitch\")\n", "* [Nuance Communications](/wiki/Nuance_Communications \"Nuance Communications\")\n", "* [RiskIQ](/wiki/RiskIQ \"RiskIQ\")\n", "* [Skype Technologies](/wiki/Skype_Technologies \"Skype Technologies\")\n", "* [OpenAI](/wiki/OpenAI \"OpenAI\") (49%)[[1]](#cite_note-1)\n", "* [<PERSON><PERSON><PERSON>](/wiki/<PERSON><PERSON><PERSON> \"Xamarin\")\n", "* [<PERSON>andr](/wiki/<PERSON>andr \"Xandr\")\n", "\n", " |\n", "|  |\n", "| [ASN](/wiki/Autonomous_System_Number \"Autonomous System Number\") | * [8075](https://bgp.tools/as/8075)\n", " |\n", "|  |\n", "| Website | [microsoft.com](https://www.microsoft.com/) |\n", "| **Footnotes / references**Financials as of June 30, 2023[[update]](https://en.wikipedia.org/w/index.php?title=Microsoft&action=edit)[[2]](#cite_note-2) |\n", "\n", "|  |  |  |\n", "| --- | --- | --- |\n", "| \n", "\n", "|  |  |\n", "| --- | --- |\n", "| [<PERSON> in 2023](/wiki/File:<PERSON>_<PERSON>_2017_(cropped).jpg) | This article is part of a series about\n", "[<PERSON>](/wiki/<PERSON> \"<PERSON>\") |\n", "\n", " |\n", "| * [Awards and honors](/wiki/<PERSON>#Recognition \"<PERSON>\")\n", "* [Philanthropy](/wiki/<PERSON><PERSON>#Philanthropy \"<PERSON>\")\n", "* [Political positions](/wiki/<PERSON>#Political_positions \"<PERSON>\")\n", "* [Public image](/wiki/<PERSON>_<PERSON>#Public_image \"<PERSON>\")\n", "* [Residence](/wiki/<PERSON>_<PERSON>%27s_house \"<PERSON>'s house\")\n", "\n", "---\n", "\n", "Companies* [Traf-O-Data](/wiki/Traf-O-Data \"Traf-O-Data\")\n", "* Microsoft ([criticism](/wiki/Criticism_of_Microsoft \"Criticism of Microsoft\"))\n", "* [BEN](/wiki/Branded_Entertainment_Network \"Branded Entertainment Network\")\n", "* [Cascade Investment](/wiki/Cascade_Investment \"Cascade Investment\")\n", "* [TerraPower](/wiki/TerraPower \"TerraPower\")\n", "* [Gates Ventures](/wiki/Gates_Ventures \"Gates Ventures\")\n", "\n", "---\n", "\n", "Charitable organizations* [Bill & Melinda Gates Foundation](/wiki/Bill_%26_Melinda_Gates_Foundation \"Bill & Melinda Gates Foundation\")\n", "* [Match for Africa](/wiki/Match_for_Africa \"Match for Africa\")\n", "* [The Giving Pledge](/wiki/The_Giving_Pledge \"The Giving Pledge\")\n", "* [OER Project](/wiki/OER_Project \"OER Project\")\n", "* [Breakthrough Energy](/wiki/Breakthrough_Energy \"Breakthrough Energy\")\n", "* [Mission Innovation](/wiki/Mission_Innovation \"Mission Innovation\")\n", "\n", "---\n", "\n", "Writings* \"[An Open Letter to Hobbyists](/wiki/An_Open_Letter_to_Hobbyists \"An Open Letter to Hobbyists\")\"\n", "* *[The Road Ahead](/wiki/The_Road_Ahead_(Gates_book) \"The Road Ahead (Gates book)\")*\n", "* *[Business @ the Speed of Thought](/wiki/Business_@_the_Speed_of_Thought \"Business @ the Speed of Thought\")*\n", "* *[How to Avoid a Climate Disaster](/wiki/How_to_Avoid_a_Climate_Disaster \"How to Avoid a Climate Disaster\")*\n", "* *[How to Prevent the Next Pandemic](/wiki/How_to_Prevent_the_Next_Pandemic \"How to Prevent the Next Pandemic\")*\n", "\n", "---\n", "\n", "Related* [<PERSON>' flower fly](/wiki/<PERSON>_<PERSON>%27_flower_fly \"<PERSON>' flower fly\")\n", "* [Codex Leicester](/wiki/Codex_Leicester \"Codex Leicester\")\n", "* *[Lost on the Grand Banks](/wiki/Lost_on_the_Grand_Banks \"Lost on the Grand Banks\")*\n", "* [History of Microsoft](/wiki/History_of_Microsoft \"History of Microsoft\")\n", "* [Timeline of Microsoft](/wiki/Timeline_of_Microsoft \"Timeline of Microsoft\")\n", "* [<PERSON>](/wiki/<PERSON> \"<PERSON>\")\n", "\n", "---\n", "\n", " |\n", "| * [v](/wiki/Template:<PERSON>_<PERSON>_series \"Template:<PERSON> series\")\n", "* [t](/wiki/Template_talk:<PERSON><PERSON>_series \"Template talk:<PERSON> series\")\n", "* [e](/wiki/Special:EditPage/Template:<PERSON>_series \"Special:EditPage/Template:<PERSON> series\")\n", " |\n", "\n", "**Microsoft Corporation** is an American multinational [technology corporation](/wiki/Technology_company \n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["task2 = \"\"\"Scroll down.\"\"\"\n", "user_proxy.initiate_chat(web_surfer, message=task2, clear_history=False)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to web_surfer):\n", "\n", "Where was the first office location, and when did they move to Redmond?\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION answer_from_page...\u001b[0m\n", "\u001b[33mweb_surfer\u001b[0m (to user_proxy):\n", "\n", "Microsoft's first office location was in Albuquerque, New Mexico, where it was founded on April 4, 1975. However, Microsoft later moved its headquarters to Redmond, Washington in January 1979. Since then, Redmond has been the main office location for Microsoft.\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["task3 = \"\"\"Where was the first office location, and when did they move to Redmond?\"\"\"\n", "user_proxy.initiate_chat(web_surfer, message=task3, clear_history=False)"]}], "metadata": {"front_matter": {"description": "Browse the web with agents.", "tags": ["web", "nested chat", "tool/function"]}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 4}