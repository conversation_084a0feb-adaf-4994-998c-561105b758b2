﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(PackageTargetFrameworks)</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>AutoGen.DotnetInteractive</RootNamespace>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>AutoGen.DotnetInteractive</Title>
    <Description>
      Dotnet interactive integration for AutoGen agents
    </Description>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.DotNet.Interactive" Version="$(MicrosoftDotnetInteractive)" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="dotnet-tools.json" />
	  <EmbeddedResource Include="RestoreInteractive.config" />
	</ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
    <PackageReference Include="Microsoft.DotNet.Interactive.Jupyter" Version="$(MicrosoftDotnetInteractive)" />
    <PackageReference Include="Microsoft.DotNet.Interactive.PackageManagement" Version="$(MicrosoftDotnetInteractive)" />
  </ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\AutoGen.Core\AutoGen.Core.csproj" />
    <ProjectReference Include="..\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
	</ItemGroup>

</Project>
