﻿# EditorConfig is awesome:http://EditorConfig.org

# top-most EditorConfig file
root = true

# Don't use tabs for indentation.
[*]
indent_style = space
# (Please don't specify an indent_size here; that has too many unintended consequences.)

# Code files
[*.{cs,csx,vb,vbx}]
indent_size = 4
insert_final_newline = true
charset = utf-8-bom

[*.xaml]
indent_size = 4

[*.ps1]
indent_size = 2

# Xml project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2

# Xml config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

[*.groovy]
indent_size = 2

# Dotnet code style settings:
[*.{cs,vb}]
# Sort using and Import directives with System.* appearing first
dotnet_sort_system_directives_first = true
dotnet_style_require_accessibility_modifiers = always:warning

# No blank line between System.* and Microsoft.*
dotnet_separate_import_directive_groups = false

# Suggest more modern language features when available
dotnet_style_object_initializer = true:suggestion
dotnet_style_collection_initializer = true:suggestion
dotnet_style_coalesce_expression = true:error
dotnet_style_null_propagation = true:error
dotnet_style_explicit_tuple_names = true:suggestion
dotnet_style_prefer_inferred_tuple_names = true:suggestion
dotnet_style_prefer_inferred_anonymous_type_member_names = true:suggestion
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:suggestion
dotnet_style_prefer_conditional_expression_over_return = false
dotnet_style_prefer_conditional_expression_over_assignment = false
dotnet_style_prefer_auto_properties = false

# Use language keywords instead of framework type names for type references
dotnet_style_predefined_type_for_locals_parameters_members = true:error
dotnet_style_predefined_type_for_member_access = true:error

# Prefer read-only on fields
dotnet_style_readonly_field = false

# CSharp code style settings:
[*.cs]

# Prefer "var" only when the type is apparent
csharp_style_var_for_built_in_types = false:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion
csharp_style_var_elsewhere = false:suggestion

# Prefer method-like constructs to have a block body
csharp_style_expression_bodied_methods = false:none
csharp_style_expression_bodied_constructors = false:none
csharp_style_expression_bodied_operators = false:none

# Prefer property-like constructs to have an expression-body
csharp_style_expression_bodied_properties = true:none
csharp_style_expression_bodied_indexers = true:none
csharp_style_expression_bodied_accessors = true:none

# Use block body for local functions
csharp_style_expression_bodied_local_functions = when_on_single_line:silent

# Suggest more modern language features when available
csharp_style_pattern_matching_over_is_with_cast_check = true:error
csharp_style_pattern_matching_over_as_with_null_check = true:error
csharp_style_inlined_variable_declaration = true:error
csharp_style_throw_expression = true:suggestion
csharp_style_conditional_delegate_call = true:suggestion
csharp_style_deconstructed_variable_declaration = true:suggestion

# Newline settings
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true

# Identation options
csharp_indent_case_contents = true
csharp_indent_case_contents_when_block = true
csharp_indent_switch_labels = true
csharp_indent_labels = no_change
csharp_indent_block_contents = true
csharp_indent_braces = false

# Spacing options
csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_call_empty_parameter_list_parentheses = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_method_declaration_name_and_open_parenthesis = false
csharp_space_between_parentheses = false
csharp_space_between_square_brackets = false
csharp_space_between_empty_square_brackets = false
csharp_space_before_open_square_brackets = false
csharp_space_around_declaration_statements = false
csharp_space_around_binary_operators = before_and_after
csharp_space_after_cast = false
csharp_space_before_semicolon_in_for_statement = false
csharp_space_before_dot = false
csharp_space_after_dot = false
csharp_space_before_comma = false
csharp_space_after_comma = true
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_after_semicolon_in_for_statement = true

# Wrapping
csharp_preserve_single_line_statements = true
csharp_preserve_single_line_blocks = true

# Code block
csharp_prefer_braces = true:warning

# Using statements
csharp_using_directive_placement = outside_namespace:error

# Modifier settings
csharp_prefer_static_local_function = true:warning
csharp_preferred_modifier_order = public,private,protected,internal,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async:warning

# enable format error
dotnet_diagnostic.IDE0055.severity = error

# IDE0035: Remove unreachable code
dotnet_diagnostic.IDE0035.severity = error

# IDE0005: Remove unncecessary usings
dotnet_diagnostic.CS8019.severity = error
dotnet_diagnostic.IDE0005.severity = error

# IDE0069: Remove unused local variable
dotnet_diagnostic.IDE0069.severity = error

# disable CS1573: Parameter has no matching param tag in the XML comment for
dotnet_diagnostic.CS1573.severity = none

# disable CS1570: XML comment has badly formed XML
dotnet_diagnostic.CS1570.severity = none

dotnet_diagnostic.IDE0035.severity = warning # Remove unreachable code
dotnet_diagnostic.IDE0161.severity = warning # Use file-scoped namespace

csharp_style_var_elsewhere = true:suggestion # Prefer 'var' everywhere

# disable check for generated code
[*.generated.cs]
generated_code = true