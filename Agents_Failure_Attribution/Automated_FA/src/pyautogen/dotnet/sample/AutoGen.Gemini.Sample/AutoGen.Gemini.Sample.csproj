﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IncludeResourceFolder>true</IncludeResourceFolder>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\AutoGen\AutoGen.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.Gemini\AutoGen.Gemini.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <PackageReference Include="FluentAssertions" Version="$(FluentAssertionVersion)" />
  </ItemGroup>

</Project>
