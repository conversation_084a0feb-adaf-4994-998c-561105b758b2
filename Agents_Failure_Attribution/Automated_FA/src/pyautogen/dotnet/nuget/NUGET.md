### About AutoGen for .NET
`AutoGen for .NET` is the official .NET SDK for [AutoGen](https://github.com/ag2ai/ag2). It enables you to create LLM agents and construct multi-agent workflows with ease. It also provides integration with popular platforms like OpenAI, Semantic Kernel, and LM Studio.

### Gettings started
- Find documents and examples on our [document site](https://ag2ai.github.io/autogen-for-net/) 
- Join our [Discord channel](https://discord.gg/pAbnFJrkgZ) to get help and discuss with the community
- Report a bug or request a feature by creating a new issue in our [github repo](https://github.com/ag2ai/ag2)
- Consume the nightly build package from one of the [nightly build feeds](https://ag2ai.github.io/autogen-for-net/articles/Installation.html#nighly-build)