<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <AzureOpenAIVersion>1.0.0-beta.17</AzureOpenAIVersion>
        <SemanticKernelVersion>1.15.1</SemanticKernelVersion>
        <SemanticKernelExperimentalVersion>1.15.1-alpha</SemanticKernelExperimentalVersion>
        <SystemCodeDomVersion>5.0.0</SystemCodeDomVersion>
        <MicrosoftCodeAnalysisVersion>4.3.0</MicrosoftCodeAnalysisVersion>
        <ApprovalTestVersion>6.0.0</ApprovalTestVersion>
        <FluentAssertionVersion>6.8.0</FluentAssertionVersion>
        <XUnitVersion>2.4.2</XUnitVersion>
        <MicrosoftNETTestSdkVersion>17.7.0</MicrosoftNETTestSdkVersion>
        <MicrosoftDotnetInteractive>1.0.0-beta.24229.4</MicrosoftDotnetInteractive>
        <MicrosoftSourceLinkGitHubVersion>8.0.0</MicrosoftSourceLinkGitHubVersion>
        <MicrosoftASPNETCoreVersion>8.0.4</MicrosoftASPNETCoreVersion>
        <GoogleCloudAPIPlatformVersion>3.0.0</GoogleCloudAPIPlatformVersion>
        <JsonSchemaVersion>*******</JsonSchemaVersion>
        <AzureAIInferenceVersion>1.0.0-beta.1</AzureAIInferenceVersion>
        <PowershellSDKVersion>7.4.4</PowershellSDKVersion>
    </PropertyGroup>
</Project>