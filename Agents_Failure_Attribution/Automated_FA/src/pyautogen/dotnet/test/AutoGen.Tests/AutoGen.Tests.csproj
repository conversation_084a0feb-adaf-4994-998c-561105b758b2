﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <IsTestProject>True</IsTestProject>
    <NoWarn>$(NoWarn);xUnit1013;SKEXP0110</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\sample\AutoGen.BasicSamples\AutoGen.BasicSample.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.Anthropic\AutoGen.Anthropic.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\..\src\AutoGen\AutoGen.csproj" />
    <ProjectReference Include="..\AutoGen.Test.Share\AutoGen.Tests.Share.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ApprovalTests\square.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
