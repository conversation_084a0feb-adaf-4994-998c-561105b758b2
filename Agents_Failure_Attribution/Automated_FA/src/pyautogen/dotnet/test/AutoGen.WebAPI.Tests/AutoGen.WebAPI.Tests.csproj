﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.TestHost" Version="$(MicrosoftASPNETCoreVersion)" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\AutoGen.Mistral\AutoGen.Mistral.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.WebAPI\AutoGen.WebAPI.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\..\src\AutoGen\AutoGen.csproj" />
    <ProjectReference Include="..\AutoGen.Tests\AutoGen.Tests.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
