<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
      <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
      <ImplicitUsings>enable</ImplicitUsings>
      <IsPackable>false</IsPackable>
      <GenerateDocumentationFile>True</GenerateDocumentationFile>
      <RootNamespace>AutoGen.Anthropic.Tests</RootNamespace>
      <IsTestProject>True</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\AutoGen.Anthropic\AutoGen.Anthropic.csproj" />
      <ProjectReference Include="..\AutoGen.Tests\AutoGen.Tests.csproj" />
      <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    </ItemGroup>

    <ItemGroup>
      <None Update="images\square.png">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>
</Project>
