﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <IsPackable>false</IsPackable>
    <NoWarn>$(NoWarn);SKEXP0110</NoWarn>
    <IsTestProject>True</IsTestProject>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\AutoGen.SemanticKernel\AutoGen.SemanticKernel.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\..\src\AutoGen\AutoGen.csproj" />
    <ProjectReference Include="..\AutoGen.Tests\AutoGen.Tests.csproj" />
  </ItemGroup>

</Project>
