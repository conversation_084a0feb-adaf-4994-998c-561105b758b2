﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <IsPackable>false</IsPackable>
    <IsTestProject>True</IsTestProject>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\sample\AutoGen.OpenAI.Sample\AutoGen.OpenAI.V1.Sample.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\..\src\AutoGen\AutoGen.csproj" />
    <ProjectReference Include="..\AutoGen.Tests\AutoGen.Tests.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ApprovalTests\OpenAIMessageTests.BasicMessageTest.approved.txt">
      <ParentFile>$([System.String]::Copy('%(FileName)').Split('.')[0])</ParentFile>
      <ParentExtension>$(ProjectExt.Replace('proj', ''))</ParentExtension>
      <DependentUpon>%(ParentFile)%(ParentExtension)</DependentUpon>
    </None>
  </ItemGroup>

</Project>
