default_language_version:
  python: python3
exclude: 'dotnet'
ci:
  autofix_prs: true
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit suggestions'
  autoupdate_schedule: 'monthly'

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
    - id: check-added-large-files
    - id: check-ast
    - id: check-yaml
    - id: check-toml
    - id: check-json
    - id: check-byte-order-marker
      exclude: .gitignore
    - id: check-merge-conflict
    - id: detect-private-key
    - id: trailing-whitespace
    - id: end-of-file-fixer
    - id: no-commit-to-branch
  - repo: https://github.com/psf/black
    rev: 24.4.2
    hooks:
    - id: black
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.4.8
    hooks:
      - id: ruff
        types_or: [ python, pyi, jupyter ]
        args: ["--fix", "--ignore=E402"]
        exclude: notebook/agentchat_databricks_dbrx.ipynb
  - repo: https://github.com/codespell-project/codespell
    rev: v2.3.0
    hooks:
      - id: codespell
        args: ["-L", "ans,linar,nam,tread,ot,assertIn,dependin,socio-economic"]
        exclude: |
            (?x)^(
              pyproject.toml |
              website/static/img/ag.svg |
              website/static/img/ag2.svg |
              website/yarn.lock |
              website/docs/tutorial/code-executors.ipynb |
              website/docs/topics/code-execution/custom-executor.ipynb |
              website/docs/topics/non-openai-models/cloud-gemini.ipynb |
              notebook/.* |
              test/agentchat/contrib/graph_rag/trip_planner_data/.* |
              test/agentchat/contrib/graph_rag/paul_graham_essay.txt

            )$
  # See https://jaredkhan.com/blog/mypy-pre-commit
  - repo: local
    hooks:
    - id: mypy
      name: mypy
      entry: "./scripts/pre-commit-mypy-run.sh"
      language: python
      # use your preferred Python version
      # language_version: python3.8
      additional_dependencies: []
      types: [python]
      # use require_serial so that script
      # is only called once per commit
      require_serial: true
      # Print the number of files as a sanity-check
      verbose: true
    - id: check-license-headers
      name: check license headers
      entry: python ./scripts/pre-commit-license-check.py
      language: python
      types: [python]
      exclude: |
        (?x)^(
          notebook/.* |
          website/.*
        )$
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.8.5
    hooks:
      - id: nbqa-black
