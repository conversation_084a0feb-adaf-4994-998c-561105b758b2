# Promptflow

Promptflow is a comprehensive suite of tools that simplifies the development, testing, evaluation, and deployment of LLM based AI applications. It also supports integration with Azure AI for cloud-based operations and is designed to streamline end-to-end development.

Refer to [Promptflow docs](https://ag2ai.github.io/promptflow/) for more information.

Quick links:

- Why use Promptflow - [Link](https://learn.microsoft.com/en-us/azure/machine-learning/prompt-flow/overview-what-is-prompt-flow)
- Quick start guide - [Link](https://ag2ai.github.io/promptflow/how-to-guides/quick-start.html)
- Sample application for Promptflow + AutoGen integration - [Link](https://github.com/ag2ai/build-with-ag2/tree/main/samples/apps/promptflow-autogen)

## Sample Flow

![Sample Promptflow](./img/ecosystem-promptflow.png)
