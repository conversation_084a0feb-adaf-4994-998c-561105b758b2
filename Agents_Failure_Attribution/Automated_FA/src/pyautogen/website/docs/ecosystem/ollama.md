# Ollama

![Ollama Example](img/ecosystem-ollama.png)

[Ollama](https://ollama.com/) allows the users to run open-source large language models, such as Llama 2, locally. Ollama bundles model weights, configuration, and data into a single package, defined by a Modelfile. It optimizes setup and configuration details, including GPU usage.

- [Ollama + AutoGen instruction](https://ollama.ai/blog/openai-compatibility)
