# Research

For technical details, please check our technical report and research publications.

* [AutoGen: Enabling Next-Gen LLM Applications via Multi-Agent Conversation Framework](https://arxiv.org/abs/2308.08155). <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>. ArXiv 2023.

```bibtex
@inproceedings{wu2023autogen,
      title={AutoGen: Enabling Next-Gen LLM Applications via Multi-Agent Conversation Framework},
      author={<PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON> and <PERSON><PERSON> and <PERSON>},
      year={2023},
      eprint={2308.08155},
      archivePrefix={arXiv},
      primaryClass={cs.AI}
}
```

* [Cost-Effective Hyperparameter Optimization for Large Language Model Generation Inference](https://arxiv.org/abs/2303.04673). <PERSON>, <PERSON>, <PERSON>. AutoML'23.

```bibtex
@inproceedings{wang2023EcoOptiGen,
    title={Cost-Effective Hyperparameter Optimization for Large Language Model Generation Inference},
    author={<PERSON> and <PERSON>e<PERSON> and <PERSON> <PERSON>. Awadallah},
    year={2023},
    booktitle={AutoML'23},
}
```

* [An Empirical Study on Challenging Math Problem Solving with GPT-4](https://arxiv.org/abs/2306.01337). Yiran Wu, Feiran <PERSON>a, Shaokun Zhang, Hangyu Li, Erkang Zhu, Yue Wang, Yin Tat Lee, Richard Peng, Qingyun Wu, Chi Wang. ArXiv preprint arXiv:2306.01337 (2023).

```bibtex
@inproceedings{wu2023empirical,
    title={An Empirical Study on Challenging Math Problem Solving with GPT-4},
    author={Yiran Wu and Feiran Jia and Shaokun Zhang and Hangyu Li and Erkang Zhu and Yue Wang and Yin Tat Lee and Richard Peng and Qingyun Wu and Chi Wang},
    year={2023},
    booktitle={ArXiv preprint arXiv:2306.01337},
}
```

* [EcoAssistant: Using LLM Assistant More Affordably and Accurately](https://arxiv.org/abs/2310.03046). Jieyu Zhang, Ranjay Krishna, Ahmed H. Awadallah, Chi Wang. ArXiv preprint arXiv:2310.03046 (2023).

```bibtex
@inproceedings{zhang2023ecoassistant,
    title={EcoAssistant: Using LLM Assistant More Affordably and Accurately},
    author={Zhang, Jieyu and Krishna, Ranjay and Awadallah, Ahmed H and Wang, Chi},
    year={2023},
    booktitle={ArXiv preprint arXiv:2310.03046},
}
```

* [Towards better Human-Agent Alignment: Assessing Task Utility in LLM-Powered Applications](https://arxiv.org/abs/2402.09015). Negar Arabzadeh, Julia Kiseleva, Qingyun Wu, Chi Wang, Ahmed Awadallah, Victor Dibia, Adam Fourney, Charles Clarke. ArXiv preprint arXiv:2402.09015 (2024).

```bibtex
@misc{Kiseleva2024agenteval,
      title={Towards better Human-Agent Alignment: Assessing Task Utility in LLM-Powered Applications},
      author={Negar Arabzadeh and Julia Kiseleva and Qingyun Wu and Chi Wang and Ahmed Awadallah and Victor Dibia and Adam Fourney and Charles Clarke},
      year={2024},
      eprint={2402.09015},
      archivePrefix={arXiv},
      primaryClass={cs.CL}
}
```

* [Training Language Model Agents without Modifying Language Models](https://arxiv.org/abs/2402.11359). Shaokun Zhang, Jieyu Zhang, Jiale Liu, Linxin Song, Chi Wang, Ranjay Krishna, Qingyun Wu. ICML'24.

```bibtex
@misc{zhang2024agentoptimizer,
      title={Training Language Model Agents without Modifying Language Models},
      author={Shaokun Zhang and Jieyu Zhang and Jiale Liu and Linxin Song and Chi Wang and Ranjay Krishna and Qingyun Wu},
      year={2024},
      booktitle={ICML'24},
}
```

* [AutoDefense: Multi-Agent LLM Defense against Jailbreak Attacks](https://arxiv.org/abs/2403.04783). Yifan Zeng, Yiran Wu, Xiao Zhang, Huazheng Wang, Qingyun Wu. ArXiv preprint arXiv:2403.04783 (2024).

```bibtex
@misc{zeng2024autodefense,
      title={AutoDefense: Multi-Agent LLM Defense against Jailbreak Attacks},
      author={Yifan Zeng and Yiran Wu and Xiao Zhang and Huazheng Wang and Qingyun Wu},
      year={2024},
      eprint={2403.04783},
      archivePrefix={arXiv},
      primaryClass={cs.LG}
}
```

* [StateFlow: Enhancing LLM Task-Solving through State-Driven Workflows](https://arxiv.org/abs/2403.11322). Yiran Wu, Tianwei Yue, Shaokun Zhang, Chi Wang, Qingyun Wu. ArXiv preprint arXiv:2403.11322 (2024).

```bibtex
@misc{wu2024stateflow,
        title={StateFlow: Enhancing LLM Task-Solving through State-Driven Workflows},
        author={Yiran Wu and Tianwei Yue and Shaokun Zhang and Chi Wang and Qingyun Wu},
        year={2024},
        eprint={2403.11322},
        archivePrefix={arXiv},
        primaryClass={cs.CL}
}
```
