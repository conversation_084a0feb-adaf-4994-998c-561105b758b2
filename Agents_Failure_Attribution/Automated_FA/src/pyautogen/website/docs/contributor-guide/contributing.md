# Contributing to AG2

The project welcomes contributions from developers and organizations worldwide. Our goal is to foster a collaborative and inclusive community where diverse perspectives and expertise can drive innovation and enhance the project's capabilities. Whether you are an individual contributor or represent an organization, we invite you to join us in shaping the future of this project. Together, we can build something truly remarkable. Possible contributions include but not limited to:

- Pushing patches.
- Code review of pull requests.
- Documentation, examples and test cases.
- Readability improvement, e.g., improvement on docstr and comments.
- Community participation in [issues](https://github.com/ag2ai/ag2/issues), [discord](https://discord.gg/pAbnFJrkgZ), and [twitter](https://twitter.com/<PERSON>_<PERSON>_).
- Tutorials, blog posts, talks that promote the project.
- Sharing application scenarios and/or related research.

If you are new to GitHub [here](https://help.github.com/categories/collaborating-with-issues-and-pull-requests/) is a detailed help source on getting involved with development on GitHub.

## Roadmaps

To see what we are working on and what we plan to work on, please check our
[Roadmap](https://ag2ai.com/roadmap) and [Roadmap Issues](https://github.com/ag2ai/ag2/issues?q=is%3Aopen+is%3Aissue+label%3Aroadmap).

## Becoming a Reviewer

There is currently no formal reviewer solicitation process. Current reviewers identify reviewers from active contributors. If you are willing to become a reviewer, you are welcome to let us know on discord.

## Contact Maintainers

The project is currently maintained by a [dynamic group of volunteers](https://github.com/ag2ai/ag2/blob/main/MAINTAINERS.md) from several organizations. Contact project administrators Chi Wang and Qingyun <NAME_EMAIL> if you are interested in becoming a maintainer.


## License Headers

To maintain proper licensing and copyright notices, please include the following header at the top of each new source code file you create, regardless of the programming language:

```python
# Copyright (c) 2023 - 2024, Owners of https://github.com/ag2ai
#
# SPDX-License-Identifier: Apache-2.0
```

For files that contain or are derived from the original MIT-licensed code from https://github.com/microsoft/autogen, please use this extended header:

```python
# Copyright (c) 2023 - 2024, Owners of https://github.com/ag2ai
#
# SPDX-License-Identifier: Apache-2.0
#
# Portions derived from https://github.com/microsoft/autogen are under the MIT License.
# SPDX-License-Identifier: MIT
```

Please ensure you update the year range as appropriate. If you're unsure which header to use or have any questions about licensing, please don't hesitate to ask in your pull request or reach out to the maintainers.
