import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";

# Getting Started

AG2 (formerly AutoGen) is an open-source programming framework for building AI agents and facilitating
cooperation among multiple agents to solve tasks. AG2 aims to provide an easy-to-use
and flexible framework for accelerating development and research on agentic AI,
like PyTorch for Deep Learning. It offers features such as agents that can converse
with other agents, LLM and tool use support, autonomous and human-in-the-loop workflows,
and multi-agent conversation patterns.

![AG2 Overview](/img/autogen_agentchat.png)

### Main Features

- AG2 enables building next-gen LLM applications based on [multi-agent
  conversations](/docs/Use-Cases/agent_chat) with minimal effort. It simplifies
  the orchestration, automation, and optimization of a complex LLM workflow. It
  maximizes the performance of LLM models and overcomes their weaknesses.
- It supports [diverse conversation
  patterns](/docs/Use-Cases/agent_chat#supporting-diverse-conversation-patterns)
  for complex workflows. With customizable and conversable agents, developers can
  use AG2 to build a wide range of conversation patterns concerning
  conversation autonomy, the number of agents, and agent conversation topology.
- It provides a collection of working systems with different complexities. These
  systems span a [wide range of
  applications](/docs/Use-Cases/agent_chat#diverse-applications-implemented-with-autogen)
  from various domains and complexities. This demonstrates how AG2 can
  easily support diverse conversation patterns.

AG2 is powered by collaborative [research studies](/docs/Research) from
Microsoft, Penn State University, and University of Washington.

### Quickstart

```sh
pip install autogen
```
:::tip
You can also install with different [optional dependencies](/docs/installation/Optional-Dependencies).
:::

<Tabs>
    <TabItem value="nocode" label="No code execution" default>

```python
import os
from autogen import AssistantAgent, UserProxyAgent

llm_config = {"model": "gpt-4", "api_key": os.environ["OPENAI_API_KEY"]}
assistant = AssistantAgent("assistant", llm_config=llm_config)
user_proxy = UserProxyAgent("user_proxy", code_execution_config=False)

# Start the chat
user_proxy.initiate_chat(
    assistant,
    message="Tell me a joke about NVDA and TESLA stock prices.",
)
```

    </TabItem>
    <TabItem value="local" label="Local execution" default>

:::warning
When asked, be sure to check the generated code before continuing to ensure it is safe to run.
:::

```python
import os
import autogen
from autogen import AssistantAgent, UserProxyAgent

llm_config = {"model": "gpt-4", "api_key": os.environ["OPENAI_API_KEY"]}
assistant = AssistantAgent("assistant", llm_config=llm_config)

user_proxy = UserProxyAgent(
    "user_proxy", code_execution_config={"executor": autogen.coding.LocalCommandLineCodeExecutor(work_dir="coding")}
)

# Start the chat
user_proxy.initiate_chat(
    assistant,
    message="Plot a chart of NVDA and TESLA stock price change YTD.",
)
```

    </TabItem>
    <TabItem value="docker" label="Docker execution" default>

```python
import os
import autogen
from autogen import AssistantAgent, UserProxyAgent

llm_config = {"model": "gpt-4", "api_key": os.environ["OPENAI_API_KEY"]}

with autogen.coding.DockerCommandLineCodeExecutor(work_dir="coding") as code_executor:
    assistant = AssistantAgent("assistant", llm_config=llm_config)
    user_proxy = UserProxyAgent(
        "user_proxy", code_execution_config={"executor": code_executor}
    )

    # Start the chat
    user_proxy.initiate_chat(
        assistant,
        message="Plot a chart of NVDA and TESLA stock price change YTD. Save the plot to a file called plot.png",
    )
```

Open `coding/plot.png` to see the generated plot.

    </TabItem>

</Tabs>

:::tip
Learn more about configuring LLMs for agents [here](/docs/topics/llm_configuration).
:::

#### Multi-Agent Conversation Framework

AG2 enables the next-gen LLM applications with a generic multi-agent conversation framework. It offers customizable and conversable agents which integrate LLMs, tools, and humans.
By automating chat among multiple capable agents, one can easily make them collectively perform tasks autonomously or with human feedback, including tasks that require using tools via code. For [example](https://github.com/ag2ai/ag2/blob/main/test/twoagent.py),

The figure below shows an example conversation flow with AG2.

![Agent Chat Example](/img/chat_example.png)

### Where to Go Next?

- Go through the [tutorial](/docs/tutorial/introduction) to learn more about the core concepts in AG2
- Read the examples and guides in the [notebooks section](/docs/notebooks)
- Understand the use cases for [multi-agent conversation](/docs/Use-Cases/agent_chat) and [enhanced LLM inference](/docs/Use-Cases/enhanced_inference)
- Read the [API](/docs/reference/agentchat/conversable_agent/) docs
- Learn about [research](/docs/Research) around AG2
- Chat on [Discord](https://discord.gg/pAbnFJrkgZ)
- Follow on [Twitter](https://twitter.com/Chi_Wang_)
- See our [roadmaps](https://github.com/ag2ai/ag2/issues?q=is%3Aissue+is%3Aopen+label%3Aroadmap)

If you like our project, please give it a [star](https://github.com/ag2ai/ag2) on GitHub. If you are interested in contributing, please read [Contributor's Guide](/docs/contributor-guide/contributing).

<iframe
  src="https://ghbtns.com/github-btn.html?user=ag2ai&amp;repo=autogen&amp;type=star&amp;count=true&amp;size=large"
  frameborder="0"
  scrolling="0"
  width="170"
  height="30"
  title="GitHub"
></iframe>
