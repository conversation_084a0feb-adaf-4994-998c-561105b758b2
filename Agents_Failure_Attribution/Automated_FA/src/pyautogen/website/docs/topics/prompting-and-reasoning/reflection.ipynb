{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "9a71fa36", "metadata": {}, "source": ["# LLM Reflection\n", "\n", "AutoGen supports different LLM prompting and reasoning strategies, such as ReAct, Reflection/Self-Critique, and more. This notebook demonstrates how to realize general LLM reflection with AutoGen. Reflection is a general prompting strategy which involves having LLMs analyze their own outputs, behaviors, knowledge, or reasoning processes.\n", "\n", "This example leverages a generic interface [nested chats](/docs/tutorial/conversation-patterns#nested-chats) to implement the reflection using multi-agents."]}, {"cell_type": "markdown", "id": "5cff1938", "metadata": {}, "source": ["First make sure the `pyautogen` package is installed."]}, {"cell_type": "code", "execution_count": null, "id": "1d94a96a", "metadata": {}, "outputs": [], "source": ["! pip install \"pyautogen>=0.2.18\""]}, {"cell_type": "markdown", "id": "934a2c2b", "metadata": {}, "source": ["Import the relevant modules and configure the LLM.\n", "See [LLM Configuration](/docs/topics/llm_configuration) for how to configure LLMs."]}, {"cell_type": "code", "execution_count": 1, "id": "dca301a4", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from autogen import AssistantAgent, UserProxyAgent, config_list_from_json\n", "from autogen.cache import Cache\n", "from autogen.coding import DockerCommandLineCodeExecutor, LocalCommandLineCodeExecutor\n", "\n", "config_list = [\n", "    {\"model\": \"gpt-4-1106-preview\", \"api_key\": os.environ[\"OPENAI_API_KEY\"]},\n", "    {\"model\": \"gpt-3.5-turbo\", \"api_key\": os.environ[\"OPENAI_API_KEY\"]},\n", "]\n", "# You can also use the following method to load the config list from a file or environment variable.\n", "# config_list = config_list_from_json(env_or_file=\"OAI_CONFIG_LIST\")"]}, {"cell_type": "markdown", "id": "dea04741", "metadata": {}, "source": ["## Construct Agents\n", "Now we create three agents, including `user_proxy` as a user proxy, `writing_assistant` for generating solutions (based on the initial request or critique), and `reflection_assistant` for reflecting and providing critique."]}, {"cell_type": "code", "execution_count": 2, "id": "f8ad9963", "metadata": {}, "outputs": [], "source": ["os.makedirs(\"coding\", exist_ok=True)\n", "# Use DockerCommandLineCodeExecutor if docker is available to run the generated code.\n", "# Using docker is safer than running the generated code directly.\n", "# code_executor = DockerCommandLineCodeExecutor(work_dir=\"coding\")\n", "code_executor = LocalCommandLineCodeExecutor(work_dir=\"coding\")\n", "\n", "user_proxy = UserProxyAgent(\n", "    name=\"user_proxy\",\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\") and x.get(\"content\", \"\").rstrip().endswith(\"TERMINATE\"),\n", "    human_input_mode=\"TERMINATE\",\n", "    max_consecutive_auto_reply=10,\n", "    code_execution_config={\"executor\": code_executor},\n", ")\n", "\n", "writing_assistant = AssistantAgent(\n", "    name=\"writing_assistant\",\n", "    system_message=\"You are an writing assistant tasked to write engaging blogpost. You try generate the best blogpost possible for the user's request. If the user provides critique, respond with a revised version of your previous attempts.\",\n", "    llm_config={\"config_list\": config_list, \"cache_seed\": None},\n", ")\n", "\n", "reflection_assistant = AssistantAgent(\n", "    name=\"reflection_assistant\",\n", "    system_message=\"Generate critique and recommendations on the writing. Provide detailed recommendations, including requests for length, depth, style, etc..\",\n", "    llm_config={\"config_list\": config_list, \"cache_seed\": None},\n", ")"]}, {"cell_type": "markdown", "id": "8d2c77d7", "metadata": {}, "source": ["## Construct Agent Chats with `reflection_assistant` being a Nested Agent for Reflection"]}, {"cell_type": "code", "execution_count": 3, "id": "80075b71", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to writing_assistant):\n", "\n", "Write an engaging blogpost on the recent updates in AI. The blogpost should be engaging and understandable for general audience. Should have more than 3 paragraphes but no longer than 1000 words.\n", "\n", "--------------------------------------------------------------------------------\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33mwriting_assistant\u001b[0m (to user_proxy):\n", "\n", "### The Fascinating World of AI: What's New on the Horizon of Tomorrow?\n", "\n", "In the exhilarating realm of Artificial Intelligence (AI), the only constant is change. Recently, there have been several groundbreaking updates that are not just transforming the industry but also the way we live. From self-learning algorithms to AI becoming more creative, the future we once dreamed of is gradually materializing before our eyes. Here's a peek into the latest advancements that are reshaping the world of AI.\n", "\n", "#### 1. Chatbots Are Getting Chattier!\n", "\n", "Remember when chatbots could only answer the most basic of queries? Well, kiss those days goodbye! The new generation of chatbots is powered by advanced natural language processing systems that allow them to understand and respond to complex human emotions and subtleties. They're learning from each interaction and are getting better at mimicking human conversation. So, the next time you're chatting with a customer service bot, don't be surprised if it throws a joke or shows empathy – it's the new normal.\n", "\n", "#### 2. AI Meets Creativity\n", "\n", "One of the hottest topics nowadays is AI's foray into the creative world. We've seen AI systems compose music, create art, and even write poetry that's indistinguishable from human creations. The recent updates have seen AIs take a deeper dive – they're not just replicating styles but infusing their works with what seems like genuine creativity. This opens a pandora's box of ethical considerations, of course. How much credit does an AI deserve? Is it the programmer or the program that's the artist? While the jury's still out, the possibilities are as tantalizing as they are perplexing.\n", "\n", "#### 3. The Ethical AI Movement\n", "\n", "With great power comes great responsibility, and the AI community is taking note. There's a growing movement towards 'ethical AI' which insists on transparency, fairness, and accountability in AI systems. As AI intertwines with our daily lives, developers are working hard to ensure that these systems do not propagate bias or inequality. Features like 'explainability' – where an AI can explain the process behind its decision – are making these intelligent systems more accessible and trustworthy to the general public.\n", "\n", "#### 4. Healthcare Gets a Tech Boost\n", "\n", "AI in healthcare is a game-changer, and the recent updates are here to prove it. Thanks to the power of machine learning, AI systems can now analyze complex medical data swiftly and with remarkable accuracy. This means faster diagnoses, personalized treatment plans, and better patient outcomes. Recently, algorithms have been developed to predict patient risks for various diseases, offering the potential for early intervention like never before. Doctors and researchers are just scratching the surface of AI's potential in medicine.\n", "\n", "#### Embracing the Future\n", "\n", "As AI evolves at a breathtaking pace, it's clear that the only limits are those of our imagination. The recent updates are not just about making these systems smarter or more efficient; they're about integrating AI into the fabric of our daily lives in a meaningful, ethical, and human-centered way. Whether it's improving the quality of our conversations with chatbots, pushing the boundaries of creativity, advocating for ethical considerations, or saving lives in healthcare – AI is set on a path that will redefine our reality.\n", "\n", "So, buckle up and prepare for an exciting ride into the future. AI isn't just a tech trend; it's a revolution that's just getting started. And while it might seem daunting to keep up with the rapid developments, remember – as much as AI is about algorithms and computing, at its core, it's about enhancing human potential and creating a smarter, better world for all.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "Reflecting...\n", "\u001b[34m\n", "********************************************************************************\u001b[0m\n", "\u001b[34mStarting a new chat....\n", "\n", "Message:\n", "Reflect and provide critique on the following writing. \n", "\n", " ### The Fascinating World of AI: What's New on the Horizon of Tomorrow?\n", "\n", "In the exhilarating realm of Artificial Intelligence (AI), the only constant is change. Recently, there have been several groundbreaking updates that are not just transforming the industry but also the way we live. From self-learning algorithms to AI becoming more creative, the future we once dreamed of is gradually materializing before our eyes. Here's a peek into the latest advancements that are reshaping the world of AI.\n", "\n", "#### 1. Chatbots Are Getting Chattier!\n", "\n", "Remember when chatbots could only answer the most basic of queries? Well, kiss those days goodbye! The new generation of chatbots is powered by advanced natural language processing systems that allow them to understand and respond to complex human emotions and subtleties. They're learning from each interaction and are getting better at mimicking human conversation. So, the next time you're chatting with a customer service bot, don't be surprised if it throws a joke or shows empathy – it's the new normal.\n", "\n", "#### 2. AI Meets Creativity\n", "\n", "One of the hottest topics nowadays is AI's foray into the creative world. We've seen AI systems compose music, create art, and even write poetry that's indistinguishable from human creations. The recent updates have seen AIs take a deeper dive – they're not just replicating styles but infusing their works with what seems like genuine creativity. This opens a pandora's box of ethical considerations, of course. How much credit does an AI deserve? Is it the programmer or the program that's the artist? While the jury's still out, the possibilities are as tantalizing as they are perplexing.\n", "\n", "#### 3. The Ethical AI Movement\n", "\n", "With great power comes great responsibility, and the AI community is taking note. There's a growing movement towards 'ethical AI' which insists on transparency, fairness, and accountability in AI systems. As AI intertwines with our daily lives, developers are working hard to ensure that these systems do not propagate bias or inequality. Features like 'explainability' – where an AI can explain the process behind its decision – are making these intelligent systems more accessible and trustworthy to the general public.\n", "\n", "#### 4. Healthcare Gets a Tech Boost\n", "\n", "AI in healthcare is a game-changer, and the recent updates are here to prove it. Thanks to the power of machine learning, AI systems can now analyze complex medical data swiftly and with remarkable accuracy. This means faster diagnoses, personalized treatment plans, and better patient outcomes. Recently, algorithms have been developed to predict patient risks for various diseases, offering the potential for early intervention like never before. Doctors and researchers are just scratching the surface of AI's potential in medicine.\n", "\n", "#### Embracing the Future\n", "\n", "As AI evolves at a breathtaking pace, it's clear that the only limits are those of our imagination. The recent updates are not just about making these systems smarter or more efficient; they're about integrating AI into the fabric of our daily lives in a meaningful, ethical, and human-centered way. Whether it's improving the quality of our conversations with chatbots, pushing the boundaries of creativity, advocating for ethical considerations, or saving lives in healthcare – AI is set on a path that will redefine our reality.\n", "\n", "So, buckle up and prepare for an exciting ride into the future. AI isn't just a tech trend; it's a revolution that's just getting started. And while it might seem daunting to keep up with the rapid developments, remember – as much as AI is about algorithms and computing, at its core, it's about enhancing human potential and creating a smarter, better world for all.\n", "\n", "Carryover: \n", "\u001b[0m\n", "\u001b[34m\n", "********************************************************************************\u001b[0m\n", "\u001b[33muser_proxy\u001b[0m (to reflection_assistant):\n", "\n", "Reflect and provide critique on the following writing. \n", "\n", " ### The Fascinating World of AI: What's New on the Horizon of Tomorrow?\n", "\n", "In the exhilarating realm of Artificial Intelligence (AI), the only constant is change. Recently, there have been several groundbreaking updates that are not just transforming the industry but also the way we live. From self-learning algorithms to AI becoming more creative, the future we once dreamed of is gradually materializing before our eyes. Here's a peek into the latest advancements that are reshaping the world of AI.\n", "\n", "#### 1. Chatbots Are Getting Chattier!\n", "\n", "Remember when chatbots could only answer the most basic of queries? Well, kiss those days goodbye! The new generation of chatbots is powered by advanced natural language processing systems that allow them to understand and respond to complex human emotions and subtleties. They're learning from each interaction and are getting better at mimicking human conversation. So, the next time you're chatting with a customer service bot, don't be surprised if it throws a joke or shows empathy – it's the new normal.\n", "\n", "#### 2. AI Meets Creativity\n", "\n", "One of the hottest topics nowadays is AI's foray into the creative world. We've seen AI systems compose music, create art, and even write poetry that's indistinguishable from human creations. The recent updates have seen AIs take a deeper dive – they're not just replicating styles but infusing their works with what seems like genuine creativity. This opens a pandora's box of ethical considerations, of course. How much credit does an AI deserve? Is it the programmer or the program that's the artist? While the jury's still out, the possibilities are as tantalizing as they are perplexing.\n", "\n", "#### 3. The Ethical AI Movement\n", "\n", "With great power comes great responsibility, and the AI community is taking note. There's a growing movement towards 'ethical AI' which insists on transparency, fairness, and accountability in AI systems. As AI intertwines with our daily lives, developers are working hard to ensure that these systems do not propagate bias or inequality. Features like 'explainability' – where an AI can explain the process behind its decision – are making these intelligent systems more accessible and trustworthy to the general public.\n", "\n", "#### 4. Healthcare Gets a Tech Boost\n", "\n", "AI in healthcare is a game-changer, and the recent updates are here to prove it. Thanks to the power of machine learning, AI systems can now analyze complex medical data swiftly and with remarkable accuracy. This means faster diagnoses, personalized treatment plans, and better patient outcomes. Recently, algorithms have been developed to predict patient risks for various diseases, offering the potential for early intervention like never before. Doctors and researchers are just scratching the surface of AI's potential in medicine.\n", "\n", "#### Embracing the Future\n", "\n", "As AI evolves at a breathtaking pace, it's clear that the only limits are those of our imagination. The recent updates are not just about making these systems smarter or more efficient; they're about integrating AI into the fabric of our daily lives in a meaningful, ethical, and human-centered way. Whether it's improving the quality of our conversations with chatbots, pushing the boundaries of creativity, advocating for ethical considerations, or saving lives in healthcare – AI is set on a path that will redefine our reality.\n", "\n", "So, buckle up and prepare for an exciting ride into the future. AI isn't just a tech trend; it's a revolution that's just getting started. And while it might seem daunting to keep up with the rapid developments, remember – as much as AI is about algorithms and computing, at its core, it's about enhancing human potential and creating a smarter, better world for all.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mreflection_assistant\u001b[0m (to user_proxy):\n", "\n", "This piece on AI developments is generally well-written, engaging, and informative. It achieves its purpose of highlighting recent advancements within the sphere of artificial intelligence. However, there's always room for improvement. Below is a critique with recommendations on how to enhance the effectiveness of the writing:\n", "\n", "1. Title and Introduction:\n", "   - The title is certainly catchy and appropriate. It sets the right tone for the article.\n", "   - The introduction is enticing, but it could benefit from a statistic or a specific example to ground the reader in the reality of AI's advancements.\n", "\n", "2. <PERSON><PERSON><PERSON> and <PERSON><PERSON>:\n", "   - Each section introduces a topic but could be further enriched with more specifics. For example, in the section on chatbots, mention a particular chatbot or technology.\n", "   - The AI creativity segment raises intriguing questions about authorship and artistry but stops short of diving into current debates or referencing notable AI-generated works. It could benefit from a few examples and more insights into the ethical implications.\n", "\n", "3. Style and Tone:\n", "   - The tone is accessible and energizing, appropriate for a general audience. Nevertheless, some readers might appreciate fewer colloquialisms (\"Well, kiss those days goodbye!\") to maintain professional credibility.\n", "   - The frequent use of rhetorical questions is engaging, but their overuse can be distracting. Consider replacing one or two with assertive statements.\n", "\n", "4. Structure and Organization:\n", "   - The overall structure is logical and easy to follow. However, consider using subheadings that are more descriptive and reflective of the content that follows.\n", "\n", "5. Recommendations:\n", "   - Expand the sections to include more concrete examples, such as mentioning prominent AI tools, like GPT-3 for chatbots or DALL-E for AI-generated art, to illustrate the advancements.\n", "   - Incorporate more data and references to recent studies to substantiate claims, especially regarding AI's impact on healthcare.\n", "   - Provide links or footnotes for readers who may want to delve deeper into specific topics, such as ethical AI guidelines or creative AI projects.\n", "   - Discuss potential downsides or challenges within each AI advancement to present a balanced view. For instance, in healthcare, address data privacy concerns or the risks of over-reliance on AI diagnostics.\n", "   - Conclude with a call to action or a contemplative question to encourage reader engagement with the topic.\n", "\n", "In terms of length, an expansion to dig deeper into each point would enhance the richness of the content. Aim for about 150-200 more words per section, which should give ample space to provide the desired level of detail without overwhelming the reader. Furthermore, consider introducing a closing section that briefly discusses possible future developments, as this would provide an even stronger end to the piece.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33muser_proxy\u001b[0m (to writing_assistant):\n", "\n", "This piece on AI developments is generally well-written, engaging, and informative. It achieves its purpose of highlighting recent advancements within the sphere of artificial intelligence. However, there's always room for improvement. Below is a critique with recommendations on how to enhance the effectiveness of the writing:\n", "\n", "1. Title and Introduction:\n", "   - The title is certainly catchy and appropriate. It sets the right tone for the article.\n", "   - The introduction is enticing, but it could benefit from a statistic or a specific example to ground the reader in the reality of AI's advancements.\n", "\n", "2. <PERSON><PERSON><PERSON> and <PERSON><PERSON>:\n", "   - Each section introduces a topic but could be further enriched with more specifics. For example, in the section on chatbots, mention a particular chatbot or technology.\n", "   - The AI creativity segment raises intriguing questions about authorship and artistry but stops short of diving into current debates or referencing notable AI-generated works. It could benefit from a few examples and more insights into the ethical implications.\n", "\n", "3. Style and Tone:\n", "   - The tone is accessible and energizing, appropriate for a general audience. Nevertheless, some readers might appreciate fewer colloquialisms (\"Well, kiss those days goodbye!\") to maintain professional credibility.\n", "   - The frequent use of rhetorical questions is engaging, but their overuse can be distracting. Consider replacing one or two with assertive statements.\n", "\n", "4. Structure and Organization:\n", "   - The overall structure is logical and easy to follow. However, consider using subheadings that are more descriptive and reflective of the content that follows.\n", "\n", "5. Recommendations:\n", "   - Expand the sections to include more concrete examples, such as mentioning prominent AI tools, like GPT-3 for chatbots or DALL-E for AI-generated art, to illustrate the advancements.\n", "   - Incorporate more data and references to recent studies to substantiate claims, especially regarding AI's impact on healthcare.\n", "   - Provide links or footnotes for readers who may want to delve deeper into specific topics, such as ethical AI guidelines or creative AI projects.\n", "   - Discuss potential downsides or challenges within each AI advancement to present a balanced view. For instance, in healthcare, address data privacy concerns or the risks of over-reliance on AI diagnostics.\n", "   - Conclude with a call to action or a contemplative question to encourage reader engagement with the topic.\n", "\n", "In terms of length, an expansion to dig deeper into each point would enhance the richness of the content. Aim for about 150-200 more words per section, which should give ample space to provide the desired level of detail without overwhelming the reader. Furthermore, consider introducing a closing section that briefly discusses possible future developments, as this would provide an even stronger end to the piece.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mwriting_assistant\u001b[0m (to user_proxy):\n", "\n", "### AI Unveiled: The New Frontiers Shaping Our Tomorrow\n", "\n", "In the enthralling landscape of Artificial Intelligence (AI), each dawn heralds innovations that stretch the fabric of our daily lives. A fascinating statistic from <PERSON><PERSON><PERSON> suggests that by 2025, AI and deep learning will create more value on average across business processes than any other business-analytics combination. Let's delve into specific breakthroughs making this possible and ground ourselves in the tangible strides being made within AI.\n", "\n", "#### Chatbots: Pioneering a New Wave of Digital Conversation\n", "\n", "Imagine engaging with a chatbot that doesn't just answer your query but understands the subtleties of your tone, the context of your request, and even your emotion. This isn't the future; it's happening right now. Chatbots like GPT-3 are leading the charge, utilizing sophisticated machine learning algorithms that adapt and evolve through every interaction. Their ability to sustain deep, meaningful conversations is revolutionizing customer service, offering a glimpse into a future where digital assistants provide empathy alongside efficiency.\n", "\n", "#### The Creative Spark: AI as the New Artist on the Block\n", "\n", "Take, for instance, the AI program DALL-E, which can generate stunning visual images from textual descriptions, showcasing an 'imagination' that could rival human creatives. This blend of technology and artistry poses provocative questions about the nature of originality and creativity. A certain painting, which an AI generated to resemble the style of classic Dutch masters, recently sold at auction for an impressive sum. Who gets credit for such a piece—the algorithm, its developers, or the data it was trained on? This debate is just beginning, as AI's role in creativity continues to unfold.\n", "\n", "#### The Ethical Compass: Guiding AI Towards a Principled Future\n", "\n", "The ethical AI movement is setting a course for responsible innovation. Initiatives like the AI Ethics Guidelines from the European Union are paving the way to ensure AI systems are developed with fairness, accountability, and transparency at their core. Explainable AI (XAI), where an AI can articulate its decision-making process, is one example of this trend, offering clarity to users and building trust in these systems.\n", "\n", "#### AI's Prescription for Healthcare: A Dose of Revolutionary Change\n", "\n", "AI's application in healthcare is nothing short of transformative. New machine learning algorithms, like DeepMind's AlphaFold which predicts protein structures with unprecedented accuracy, are poised to revolutionize drug discovery and treatment protocols. AI is also being utilized to analyze countless medical images swiftly, improving early cancer detection rates and personalizing patient care like never before.\n", "\n", "#### Let's Talk About Tomorrow\n", "\n", "We stand on the cusp of a future abundantly interwoven with AI. The recent updates explored here are just facets of a larger picture, where AI's integration into society offers profound enhancements to how we communicate, create, work, and heal. AI promises not only technological betterment but socio-economic enrichment, and as we journey through AI's metamorphosis, we must remain vigilant custodians of its ethical evolution.\n", "\n", "As AI continues to redefine our boundaries, remember that at its core lies not a quest for rigid automation, but a passion for amplifying human capability and fostering a smarter, more equitable world. The horizon is laden with promise, and it's our collective responsibility to march towards it with an eye for inclusivity and a heart for progress.\n", "\n", "So, where do we go from here? Are you ready to be a part of this revolution, to embrace the endless possibilities, and to engage in the conversations that will shape our collective future? The canvas of tomorrow awaits your imprint, and AI is the brush with which we can paint a future where technology serves humanity in ways we are only just beginning to understand.\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["def reflection_message(recipient, messages, sender, config):\n", "    print(\"Reflecting...\")\n", "    return f\"Reflect and provide critique on the following writing. \\n\\n {recipient.chat_messages_for_summary(sender)[-1]['content']}\"\n", "\n", "\n", "nested_chat_queue = [\n", "    {\n", "        \"recipient\": reflection_assistant,\n", "        \"message\": reflection_message,\n", "        \"max_turns\": 1,\n", "    },\n", "]\n", "user_proxy.register_nested_chats(\n", "    nested_chat_queue,\n", "    trigger=writing_assistant,\n", "    # position=4,\n", ")\n", "\n", "# Use Cache.disk to cache the generated responses.\n", "# This is useful when the same request to the LLM is made multiple times.\n", "with Cache.disk(cache_seed=42) as cache:\n", "    user_proxy.initiate_chat(\n", "        writing_assistant,\n", "        message=\"Write an engaging blogpost on the recent updates in AI. \"\n", "        \"The blogpost should be engaging and understandable for general audience. \"\n", "        \"Should have more than 3 paragraphes but no longer than 1000 words.\",\n", "        max_turns=2,\n", "        cache=cache,\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "flaml_dev", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}