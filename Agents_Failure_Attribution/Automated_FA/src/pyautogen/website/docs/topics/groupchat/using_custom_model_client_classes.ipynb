{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Using Custom Model Client classes with Auto Speaker Selection\n", "\n", "````{=mdx}\n", ":::tip\n", "This documentation only applies when using the 'auto' speaker selection method for GroupChat **and** your GroupChatManager is using a Custom Model Client class.\n", "\n", "You don't need to change your GroupChat if either of these are the case:\n", "- You are using a different speaker selection method, such as 'manual', 'random', 'round_robin', or a Callable\n", "- Your GroupChatManager doesn't use a Custom Model Client class\n", ":::\n", "````\n", "\n", "During a group chat using the `auto` speaker selection method, an inner conversation between two agents is created to determine the next speaker after each turn. One of the speakers will take the `llm_config` from the `GroupChatManager` (the other inner agent doesn't use an `llm_config`).\n", "\n", "If the configuration for the GroupChatManager is using a Custom Model Client Class this is not propagated through to the inner conversation.\n", "\n", "So, you can control the configuration that the inner conversation agent uses by setting two properties on GroupChat:\n", "\n", "- **select_speaker_auto_llm_config**: Set this to your llm_config with the custom model client\n", "- **select_speaker_auto_model_client_cls**: Set this to the class of your custom model client\n", "\n", "This control enables you to register the custom model client class for, or assign a completely different `llm_config` to, the inner conversation agent.\n", "\n", "See a simple example below.\n", "\n", "### Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from autogen.agentchat import ConversableAgent, GroupChat, GroupChatManager"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sample Custom Model Client class\n", "The class below is an example of a custom model client class that always returns the name `<PERSON>`."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import random\n", "from types import SimpleNamespace\n", "\n", "\n", "class MyCustomModelClient:\n", "    def __init__(self, config, **kwargs):\n", "        print(f\"CustomModelClient config: {config}\")\n", "\n", "    def create(self, params):\n", "        num_of_responses = params.get(\"n\", 1)\n", "\n", "        response = SimpleNamespace()\n", "        response.choices = []\n", "        response.model = \"anything\"\n", "\n", "        # Randomly choose between <PERSON>, <PERSON>, and <PERSON> as next speaker\n", "        agent_names = [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\"]\n", "        random_index = random.randint(0, 2)\n", "\n", "        for _ in range(num_of_responses):\n", "            text = f\"Randomly choosing... {agent_names[random_index]}\"\n", "            choice = SimpleNamespace()\n", "            choice.message = SimpleNamespace()\n", "            choice.message.content = text\n", "            choice.message.function_call = None\n", "            response.choices.append(choice)\n", "        return response\n", "\n", "    def message_retrieval(self, response):\n", "        choices = response.choices\n", "        return [choice.message.content for choice in choices]\n", "\n", "    def cost(self, response) -> float:\n", "        response.cost = 0\n", "        return 0\n", "\n", "    @staticmethod\n", "    def get_usage(response):\n", "        return {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### GroupChat with Custom Model Client class\n", "Here we create `llm_config` that will use an actual LLM, then we create `custom_llm_config` that uses the custom model client class that we specified earlier.\n", "\n", "We add a few agents, all using the LLM-based configuration."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Configuration for agents\n", "llm_config = {\n", "    \"config_list\": [\n", "        {\n", "            \"api_type\": \"ollama\",\n", "            \"model\": \"llama3.1:8b\",\n", "        }\n", "    ]\n", "}\n", "\n", "# Configuration for GroupChatManager\n", "# using a Custom Model Client class (above)\n", "custom_llm_config = {\n", "    \"config_list\": [\n", "        {\n", "            \"model_client_cls\": \"MyCustomModelClient\",\n", "        }\n", "    ]\n", "}\n", "\n", "mark = ConversableAgent(\n", "    name=\"<PERSON>\",\n", "    system_message=\"You are a customer who likes asking questions about accounting.\",\n", "    description=\"Customer who needs accounting help.\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "alexandra = ConversableAgent(\n", "    name=\"<PERSON>\",\n", "    system_message=\"You are an accountant who provides detailed responses about accounting.\",\n", "    description=\"Accountant who loves to talk about accounting!\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "elizabeth = ConversableAgent(\n", "    name=\"<PERSON>\",\n", "    system_message=\"You are a head accountant who checks the answers of other accountants. Finish your response with the word 'BAZINGA'.\",\n", "    description=\"Head accountants, checks answers from accountants for validity.\",\n", "    llm_config=llm_config,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we assign the `custom_llm_config` (which uses the custom model client class) and the custom model client class, `MyCustomModelClient`, to the GroupChat so the inner conversation will use it."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["gc = GroupChat(\n", "    agents=[mark, alexandra, elizabeth],\n", "    speaker_selection_method=\"auto\",\n", "    allow_repeat_speaker=False,\n", "    select_speaker_auto_verbose=True,\n", "    select_speaker_auto_llm_config=custom_llm_config,\n", "    select_speaker_auto_model_client_cls=MyCustomModelClient,\n", "    max_round=5,\n", "    messages=[],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With that setup, we create the GroupChatManager, which will use the LLM-based config. So, the custom model client class will only be used for the inner, select speaker, agent of the GroupChat."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mmoderator\u001b[0m (to <PERSON>):\n", "\n", "<PERSON>, ask us an accounting question. <PERSON> and <PERSON> will help you out.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mMark\u001b[0m (to moderator):\n", "\n", "I've been trying to understand how to properly capitalize versus expense certain costs in our company's general ledger. Let me give you some background.\n", "\n", "We're a small manufacturing business that just purchased new equipment worth $100,000. The seller is financing 50% of the purchase price as a loan, with an interest rate of 6%. We'll be using this equipment for multiple years to produce goods.\n", "\n", "Here's my question: should we capitalize the full $100,000 cost or only the portion that we paid out-of-pocket, which was $50,000?\n", "\n", "--------------------------------------------------------------------------------\n", "[autogen.oai.client: 10-18 00:20:34] {565} INFO - Detected custom model client in config: MyCustomModelClient, model client can not be used until register_model_client is called.\n", "CustomModelClient config: {'model_client_cls': 'MyCustomModelClient'}\n", "\u001b[33mchecking_agent\u001b[0m (to speaker_selection_agent):\n", "\n", "Read the above conversation. Then select the next role from ['<PERSON>', '<PERSON>'] to play. Only return the role.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mspeaker_selection_agent\u001b[0m (to checking_agent):\n", "\n", "Randomly choosing... <PERSON>\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m>>>>>>>> Select speaker attempt 1 of 3 successfully selected: <PERSON>\u001b[0m\n", "\u001b[32m\n", "Next speaker: <PERSON>\n", "\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAlexandra\u001b[0m (to moderator):\n", "\n", "A great question that gets to the heart of accounting principles!\n", "\n", "In your case, you'll want to use the Full Amortization Method (also known as Straight-Line Depreciation) to depreciate the equipment over its useful life. This method assumes that the equipment will be used for a certain number of years, and you'll depreciate it evenly over that period.\n", "\n", "According to GAAP (Generally Accepted Accounting Principles), you should capitalize the full purchase price of the equipment, which is $100,000. This includes both the cash outlay ($50,000) and the financing portion ($50,000).\n", "\n", "Here's why:\n", "\n", "1. **GAAP requires capitalization**: When an asset is purchased or constructed, it must be capitalized in its entirety, regardless of how it was financed.\n", "2. **The equipment has a useful life**: Since you'll be using the equipment for multiple years to produce goods, it will have a useful life beyond the initial year. This means that the entire purchase price should be recognized as an asset on your balance sheet.\n", "3. **Depreciation will be calculated based on the total cost**: You'll depreciate the equipment over its useful life, using the straight-line method. The depreciation expense will be calculated based on the total cost of the equipment, including both the cash outlay and the financed portion.\n", "\n", "To illustrate this:\n", "\n", "Assume the equipment has a useful life of 5 years.\n", "\n", "* Total cost: $100,000\n", "* Annual depreciation expense = ($100,000 ÷ 5) = $20,000 per year\n", "\n", "In each of the 5 years, you'll record an annual depreciation expense of $20,000, which will be calculated based on the total cost of the equipment.\n", "\n", "In summary, capitalize the full $100,000 purchase price of the equipment, and depreciate it over its useful life using the straight-line method. This ensures that your accounting records accurately reflect the economic reality of owning an asset with a finite useful life.\n", "\n", "--------------------------------------------------------------------------------\n", "[autogen.oai.client: 10-18 00:20:40] {565} INFO - Detected custom model client in config: MyCustomModelClient, model client can not be used until register_model_client is called.\n", "CustomModelClient config: {'model_client_cls': 'MyCustomModelClient'}\n", "\u001b[33mchecking_agent\u001b[0m (to speaker_selection_agent):\n", "\n", "Read the above conversation. Then select the next role from ['<PERSON>', '<PERSON>'] to play. Only return the role.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mspeaker_selection_agent\u001b[0m (to checking_agent):\n", "\n", "Randomly choosing... Mark\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m>>>>>>>> Select speaker attempt 1 of 3 successfully selected: <PERSON>\u001b[0m\n", "\u001b[32m\n", "Next speaker: <PERSON>\n", "\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mMark\u001b[0m (to moderator):\n", "\n", "Wow, I'm glad you explained this in such detail! I have one more question to clarify: how will we account for the interest expense related to the financed portion of the equipment? Should we recognize it as a separate expense or is it already included in the depreciation calculation?\n", "\n", "In other words, do we need to make an additional journal entry to record the interest expense, or is it implicit in the depreciation calculation I mentioned earlier?\n", "\n", "--------------------------------------------------------------------------------\n", "[autogen.oai.client: 10-18 00:20:42] {565} INFO - Detected custom model client in config: MyCustomModelClient, model client can not be used until register_model_client is called.\n", "CustomModelClient config: {'model_client_cls': 'MyCustomModelClient'}\n", "\u001b[33mchecking_agent\u001b[0m (to speaker_selection_agent):\n", "\n", "Read the above conversation. Then select the next role from ['<PERSON>', '<PERSON>'] to play. Only return the role.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mspeaker_selection_agent\u001b[0m (to checking_agent):\n", "\n", "Randomly choosing... Elizabeth\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m>>>>>>>> Select speaker attempt 1 of 3 successfully selected: <PERSON>\u001b[0m\n", "\u001b[32m\n", "Next speaker: <PERSON>\n", "\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33m<PERSON><PERSON><PERSON><PERSON>\u001b[0m (to moderator):\n", "\n", "A well-structured question that gets to the heart of accounting principles indeed!\n", "\n", "To clarify, you should recognize both the capitalization of the full purchase price and the separate interest expense related to the financed portion.\n", "\n", "The reason for this is that the financing cost (interest) is a distinct economic transaction from the acquisition of the asset. As such, it's necessary to account for it separately as an expense.\n", "\n", "When you capitalize the equipment using the full Amortization Method, you're essentially recognizing the present value of the future benefits (or depreciation) associated with owning the asset. However, this method doesn't account for the financing cost explicitly.\n", "\n", "To record the interest expense related to the financed portion, you'll need to make an additional journal entry. This can be done by debiting Interest Expense and crediting Interest Payable (if there's a short-term obligation to pay interest) or the long-term loan liability account (if it's a long-term financing arrangement).\n", "\n", "To illustrate this:\n", "\n", "Assume the same equipment with a $100,000 purchase price and a 6% interest rate on the financed portion ($50,000). The interest expense for each year would be calculated as follows:\n", "\n", "Interest Expense = Financed Portion x Interest Rate\n", "= $50,000 x 6%\n", "= $3,000 per year\n", "\n", "You'll record this interest expense separately from the depreciation calculation. For example, in Year 1, you might have a journal entry like this:\n", "\n", "Debit: Interest Expense ($3,000)\n", "Credit: Interest Payable (or Long-Term Loan Liability) ($3,000)\n", "\n", "This way, you're accurately reflecting both the capitalization of the asset and the financing costs associated with its acquisition.\n", "\n", "In summary, capitalize the full $100,000 purchase price using the straight-line method, and recognize a separate interest expense related to the financed portion. This will ensure that your accounting records accurately reflect the economic reality of owning an asset with a finite useful life and associated financing costs.\n", "\n", "BAZINGA!\n", "\n", "--------------------------------------------------------------------------------\n", "[autogen.oai.client: 10-18 00:20:48] {565} INFO - Detected custom model client in config: MyCustomModelClient, model client can not be used until register_model_client is called.\n", "CustomModelClient config: {'model_client_cls': 'MyCustomModelClient'}\n", "\u001b[33mchecking_agent\u001b[0m (to speaker_selection_agent):\n", "\n", "Read the above conversation. Then select the next role from ['<PERSON>', '<PERSON>'] to play. Only return the role.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mspeaker_selection_agent\u001b[0m (to checking_agent):\n", "\n", "Randomly choosing... Elizabeth\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m>>>>>>>> Select speaker attempt #1 failed as it did not include any agent names.\u001b[0m\n", "\u001b[33mchecking_agent\u001b[0m (to speaker_selection_agent):\n", "\n", "You didn't choose a speaker. As a reminder, to determine the speaker use these prioritised rules:\n", "    1. If the context refers to themselves as a speaker e.g. \"As the...\" , choose that speaker's name\n", "    2. If it refers to the \"next\" speaker name, choose that name\n", "    3. Otherwise, choose the first provided speaker's name in the context\n", "    The names are case-sensitive and should not be abbreviated or changed.\n", "    The only names that are accepted are ['<PERSON>', '<PERSON>'].\n", "    Respond with ONLY the name of the speaker and DO NOT provide a reason.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mspeaker_selection_agent\u001b[0m (to checking_agent):\n", "\n", "Randomly choosing... Elizabeth\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m>>>>>>>> Select speaker attempt #2 failed as it did not include any agent names.\u001b[0m\n", "\u001b[33mchecking_agent\u001b[0m (to speaker_selection_agent):\n", "\n", "You didn't choose a speaker. As a reminder, to determine the speaker use these prioritised rules:\n", "    1. If the context refers to themselves as a speaker e.g. \"As the...\" , choose that speaker's name\n", "    2. If it refers to the \"next\" speaker name, choose that name\n", "    3. Otherwise, choose the first provided speaker's name in the context\n", "    The names are case-sensitive and should not be abbreviated or changed.\n", "    The only names that are accepted are ['<PERSON>', '<PERSON>'].\n", "    Respond with ONLY the name of the speaker and DO NOT provide a reason.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mspeaker_selection_agent\u001b[0m (to checking_agent):\n", "\n", "Randomly choosing... <PERSON>\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m>>>>>>>> Select speaker attempt 3 of 3 successfully selected: <PERSON>\u001b[0m\n", "\u001b[32m\n", "Next speaker: <PERSON>\n", "\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAlexandra\u001b[0m (to moderator):\n", "\n", "You're not only clever but also enthusiastic about accounting!\n", "\n", "I'm glad I could help clarify the accounting treatment for capitalizing the equipment and recognizing the interest expense related to the financed portion. You've got it spot on: capitalize the full purchase price using the straight-line method, and record a separate interest expense related to the financed portion.\n", "\n", "The key takeaway is that these are two distinct economic transactions that need to be accounted for separately:\n", "\n", "1. **Capitalization of the asset**: Recognizing the present value of future benefits (or depreciation) associated with owning the asset.\n", "2. **Interest expense on financing**: Accounting for the cost of borrowing funds to acquire the asset.\n", "\n", "By making separate journal entries for these two items, you'll ensure that your accounting records accurately reflect the economic reality of your business.\n", "\n", "Remember, accounting is all about providing a clear and accurate picture of a company's financial position and performance. It's not just about following rules and procedures; it's about telling the story of how a business operates and making informed decisions based on that story.\n", "\n", "Keep asking questions, and I'll be here to help you navigate the world of accounting!\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["gcm = GroupChatManager(\n", "    groupchat=gc,\n", "    name=\"moderator\",\n", "    system_message=\"You are moderating a chat between a customer, an accountant, and a head accountant. The customer asks a question, the accountant answers it, and the head accountant then validates it.\",\n", "    is_termination_msg=lambda msg: \"BAZINGA\" in msg[\"content\"].lower(),\n", "    llm_config=llm_config,\n", ")\n", "\n", "result = gcm.initiate_chat(\n", "    recipient=mark,\n", "    message=\"<PERSON>, ask us an accounting question. <PERSON> and <PERSON> will help you out.\",\n", "    summary_method=\"last_msg\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that the inner `speaker_selection_agent` was returning random names for the next agent, highlighting how we can control the configuration that that inner agent used for `auto` speaker selection in group chats."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 4}