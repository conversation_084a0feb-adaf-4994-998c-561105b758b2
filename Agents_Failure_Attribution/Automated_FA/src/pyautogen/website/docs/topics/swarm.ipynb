{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Swarm Orchestration\n", "\n", "With AG2, you can initiate a Swarm Chat similar to OpenAI's [Swarm](https://github.com/openai/swarm). This orchestration offers two main features:\n", "\n", "- **Headoffs**: Agents can transfer control to another agent via function calls, enabling smooth transitions within workflows.  \n", "- **Context Variables**: Agents can dynamically update shared variables through function calls, maintaining context and adaptability throughout the process.\n", "\n", "Instead of sending a task to a single LLM agent, you can assign it to a swarm of agents. Each agent in the swarm can decide whether to hand off the task to another agent. The chat terminates when the last active agent's response is a plain string (i.e., it doesn't suggest a tool call or handoff).  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Components\n", "We now introduce the main components that need to be used to create a swarm chat. \n", "\n", "### Create a `SwarmAgent`\n", "\n", "All the agents passed to the swarm chat should be instances of `SwarmAgent`. `SwarmAgent` is very similar to `AssistantAgent`, but it has some additional features to support function registration and handoffs. When creating a `SwarmAgent`, you can pass in a list of functions. These functions will be converted to schemas to be passed to the LLMs, and you don't need to worry about registering the functions for execution. You can also pass back a `SwarmResult` class, where you can return a value, the next agent to call, and update context variables at the same time.\n", "\n", "**Notes for creating the function calls**    \n", "- For input arguments, you must define the type of the argument, otherwise, the registration will fail (e.g. `arg_name: str`).      \n", "- If your function requires access or modification of the context variables, you must pass in `context_variables: dict` as one argument. This argument will not be visible to the LLM (removed when registering the function schema). But when called, the global context variables will be passed in by the swarm chat.     \n", "- The docstring of the function will be used as the prompt. So make sure to write a clear description.     \n", "- The function name will be used as the tool name.\n", "\n", "### Registering Handoffs to agents\n", "While you can create a function to decide what next agent to call, we provide a quick way to register the handoff using `ON_CONDITION`. We will craft this transition function and add it to the LLM config directly.\n", "\n", "```python\n", "agent_2 = SwarmAgent(...)\n", "agent_3 = SwarmAgent(...)\n", "\n", "# Register the handoff\n", "agent_1 = SwarmAgent(...)\n", "agent_1.handoff(hand_to=[ON_CONDITION(agent_2, \"condition_1\"), ON_CONDITION(agent_3, \"condition_2\")])\n", "\n", "# This is equivalent to:\n", "def transfer_to_agent_2():\n", "    \"\"\"condition_1\"\"\"\n", "    return agent_2\n", "\n", "def transfer_to_agent_3():\n", "    \"\"\"condition_1\"\"\"\n", "    return agent_3\n", "    \n", "agent_1 = SwarmAgent(..., functions=[transfer_to_agent_2, transfer_to_agent_3])\n", "# You can also use agent_1.add_functions to add more functions after initialization\n", "```\n", "\n", "### Registering Handoffs to a nested chat\n", "In addition to transferring to an agent, you can also trigger a nested chat by doing a handoff and using `ON_CONDITION`. This is a useful way to perform sub-tasks without that work becoming part of the broader swarm's messages.\n", "\n", "Configuring the nested chat is similar to [establishing a nested chat for an agent](https://ag2ai.github.io/ag2/docs/tutorial/conversation-patterns#nested-chats).\n", "\n", "Nested chats are a set of sequential chats and these are defined like so:\n", "```python\n", "nested_chats = [\n", "    {\n", "        \"recipient\": my_first_agent,\n", "        \"summary_method\": \"reflection_with_llm\",\n", "        \"summary_prompt\": \"Summarize the conversation into bullet points.\",\n", "    },\n", "    {\n", "        \"recipient\": poetry_agent,\n", "        \"message\": \"Write a poem about the context.\",\n", "        \"max_turns\": 1,\n", "        \"summary_method\": \"last_msg\",\n", "    },\n", "]\n", "```\n", "\n", "New to nested chats within swarms is the ability to **carryover some context from the swarm chat into the nested chat**. This is done by adding a carryover configuration. If you're not using carryover, then no messages from the swarm chat will be brought into the nested chat.\n", "\n", "The carryover is applicable only to the first chat in the nested chats and works together with that nested chat's \"message\" value, if any.\n", "\n", "```python\n", "my_carryover_config = {\n", "    \"summary_method\": \"reflection_with_llm\",\n", "    \"summary_args\": {\"summary_prompt\": \"Summarise the conversation into bullet points.\"}\n", "    }\n", "```\n", "\n", "The `summary_method` can be (with messages referring to the swarm chat's messages):     \n", "\n", "- `\"all\"` - messages will be converted to a new-line concatenated string, e.g. `[first nested chat message]\\nContext: \\n[swarm message 1]\\n[swarm message 2]\\n...`\n", "- `\"last_msg\"` - the latest message will be added, e.g. `[first nested chat message]\\nContext: \\n[swarm's latest message]`\n", "- `\"reflection_with_llm\"` - utilises an LLM to interpret the messages and its resulting response will be added, e.g. `[first nested chat message]\\nContext: \\n[llm response]`\n", "- `Callable` - a function that returns the full message (this will not concatenate with the first nested chat's message, it will replace it entirely).\n", "\n", "The signature of the `summary_method` callable is:     \n", "`def my_method(agent: ConversableAgent, messages: List[Dict[str, Any]], summary_args: Dict) -> str:`\n", "\n", "Both the \"reflection_with_llm\" and Callable will be able to utilise the `summary_args` if they are included.\n", "\n", "With your configuration available, you can add it to the first chat in the nested chat:\n", "```python\n", "nested_chats = [\n", "    {\n", "        \"recipient\": my_first_agent,\n", "        \"summary_method\": \"reflection_with_llm\",\n", "        \"summary_prompt\": \"Summarize the conversation into bullet points.\",\n", "        \"carryover_config\": my_carryover_config,\n", "    },\n", "    {\n", "        \"recipient\": poetry_agent,\n", "        \"message\": \"Write a poem about the context.\",\n", "        \"max_turns\": 1,\n", "        \"summary_method\": \"last_msg\",\n", "    },\n", "]\n", "```\n", "\n", "Finally, we add the nested chat as a handoff in the same way as we do to an agent:\n", "\n", "```python\n", "agent_1.handoff(\n", "    hand_to=[ON_CONDITION(\n", "        target={\n", "            \"chat_queue\":[nested_chats],\n", "            \"config\": Any,\n", "            \"reply_func_from_nested_chats\": None,\n", "            \"use_async\": <PERSON><PERSON>e\n", "        },\n", "        condition=\"condition_1\")\n", "        ]\n", "    )\n", "```\n", "\n", "See the documentation on [registering a nested chat](https://ag2ai.github.io/ag2/docs/reference/agentchat/conversable_agent#register_nested_chats) for further information on the parameters `reply_func_from_nested_chats`, `use_async`, and `config`.\n", "\n", "Once a nested chat is complete, the resulting output from the last chat in the nested chats will be returned as the agent that triggered the nested chat's response.\n", "\n", "### AFTER_WORK\n", "\n", "When the last active agent's response doesn't suggest a tool call or handoff, the chat will terminate by default. However, you can register an `AFTER_WORK` handoff to define a fallback agent if you don't want the chat to end at this agent. At the swarm chat level, you also pass in an `AFTER_WORK` handoff to define the fallback mechanism for the entire chat.\n", "If this parameter is set for the agent and the chat, we will prioritize the agent's setting. There should only be one `AFTER_WORK`. If multiple `AFTER_WORK` handoffs are passed, only the last one will be used.\n", "\n", "Besides fallback to an agent, we provide 3 `AfterWorkOption`:     \n", "- `TERMINATE`: Terminate the chat    \n", "- `STAY`: Stay at the current agent    \n", "- `REVERT_TO_USER`: Revert to the user agent. Only if a user agent is passed in when initializing. (See below for more details)    \n", "\n", "```python\n", "agent_1 = SwarmAgent(...)\n", "\n", "# Register the handoff\n", "agent_1.handoff(hand_to=[\n", " ON_CONDITION(...), \n", " ON_CONDITION(...),\n", " AFTER_WORK(agent_4) # Fallback to agent_4 if no handoff is suggested\n", "])\n", "\n", "agent_2.handoff(hand_to=[AFTER_WORK(AfterWorkOption.TERMINATE)]) # Terminate the chat if no handoff is suggested\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Update Agent state before replying\n", "\n", "It can be useful to update a swarm agent's state before they reply. For example, using an agent's context variables you could change their system message based on the state of the workflow.\n", "\n", "When initialising a swarm agent use the `update_agent_state_before_reply` parameter to register updates that run after the agent is selected, but before they reply.\n", "\n", "`update_agent_state_before_reply` takes a list of any combination of the following (executing them in the provided order):\n", "\n", "- `UPDATE_SYSTEM_MESSAGE` provides a simple way to update the agent's system message via an f-string that substitutes the values of context variables, or a Callable that returns a string\n", "- Callable with two parameters of type `ConversableAgent` for the agent and `List[Dict[str Any]]` for the messages, and does not return a value\n", "\n", "Below is an example of setting these up when creating a Swarm agent.\n", "\n", "```python\n", "# Creates a system message string\n", "def create_system_prompt_function(my_agent: ConversableAgent, messages: List[Dict[]]) -> str:\n", "    preferred_name = my_agent.get_context(\"preferred_name\", \"(name not provided)\")\n", "\n", "    # Note that the returned string will be treated like an f-string using the context variables\n", "    return \"You are a customer service representative helping a customer named \"\n", "    + preferred_name\n", "    + \" and their passport number is '{passport_number}'.\"\n", "\n", "# Function to update an Agent's state\n", "def my_callable_state_update_function(my_agent: ConversableAgent, messages: List[Dict[]]) -> None:\n", "    agent.set_context(\"context_key\", 43)\n", "    agent.update_system_message(\"You are a customer service representative.\")\n", "\n", "# Create the SwarmAgent and set agent updates\n", "customer_service = SwarmAgent(\n", "    name=\"CustomerServiceRep\",\n", "    system_message=\"You are a customer service representative.\",\n", "    update_agent_state_before_reply=[\n", "        UPDATE_SYSTEM_MESSAGE(\"You are a customer service representative. Quote passport number '{passport_number}'\"),\n", "        UPDATE_SYSTEM_MESSAGE(create_system_prompt_function),\n", "        my_callable_state_update_function]\n", "    ...\n", ")\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initialize SwarmChat with `initiate_swarm_chat`\n", "\n", "After a set of swarm agents are created, you can initiate a swarm chat by calling `initiate_swarm_chat`.\n", "\n", "```python\n", "chat_history, context_variables, last_active_agent = initiate_swarm_chat(\n", "    initial_agent=agent_1, # the first agent to start the chat\n", "    agents=[agent_1, agent_2, agent_3], # a list of agents\n", "    messages=[{\"role\": \"user\", \"content\": \"Hello\"}], # a list of messages to start the chat, you can also pass in one string\n", "    user_agent=user_agent, # optional, if you want to revert to the user agent\n", "    context_variables={\"key\": \"value\"} # optional, initial context variables\n", ")\n", "```\n", "\n", "How we handle messages:     \n", "- Case 1: If you pass in one single message    \n", "    - If there is a name in that message, we will assume this message is from that agent. (It will be error if that name doesn't match any agent you passed in.)    \n", "    - If there is no name, 1. User agent passed in: we assume this message is from the user agent. 2. No user agent passed in: we will create a temporary user agent just to start the chat.    \n", "- Case 2: We will use the [Resume GroupChat](https://ag2ai.github.io/ag2/docs/topics/groupchat/resuming_groupchat) feature to resume the chat. The `name` fields in these messages must be one of the names of the agents you passed in, otherwise, it will be an error.\n", "\n", "## Q&As\n", "\n", "> How are context variables updated?\n", "\n", "In a swarm, the context variables are shared amongst Swarm agents. As context variables are available at the agent level, you can use the context variable getters/setters on the agent to view and change the shared context variables. If you're working with a function that returns a `SwarmResult` you should update the passed in context variables and return it in the `SwarmResult`, this will ensure the shared context is updated.\n", "\n", "> What is the difference between ON_CONDITION and AFTER_WORK?\n", "\n", "When registering an ON_CONDITION handoff, we are creating a function schema to be passed to the LLM. The LLM will decide whether to call this function.\n", "\n", "When registering an AFTER_WORK handoff, we are defining the fallback mechanism when no tool calls are suggested. This is a higher level of control from the swarm chat level.\n", "\n", "> When to pass in a user agent?\n", "\n", "If your application requires interactions with the user, you can pass in a user agent to the groupchat, so that don't need to write an outer loop to accept user inputs and call swarm.\n", "\n", "\n", "## Demonstration\n", "\n", "\n", "### Create Swarm Agents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import autogen\n", "\n", "config_list = autogen.config_list_from_json(...)\n", "llm_config = {\"config_list\": config_list}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Agent 1 function schema:\n", "{'type': 'function', 'function': {'description': '', 'name': 'update_context_1', 'parameters': {'type': 'object', 'properties': {}}}}\n", "{'type': 'function', 'function': {'description': 'Transfer to agent 2', 'name': 'transfer_to_agent_2', 'parameters': {'type': 'object', 'properties': {}}}}\n", "Agent 3 function schema:\n", "{'type': 'function', 'function': {'description': 'Transfer to Agent 4', 'name': 'transfer_to_Agent_4', 'parameters': {'type': 'object', 'properties': {}}}}\n"]}], "source": ["import random\n", "\n", "from autogen import (\n", "    AFTER_WORK,\n", "    ON_CONDITION,\n", "    AfterWorkOption,\n", "    SwarmAgent,\n", "    SwarmResult,\n", "    initiate_swarm_chat,\n", ")\n", "\n", "\n", "# 1. A function that returns a value of \"success\" and updates the context variable \"1\" to True\n", "def update_context_1(context_variables: dict) -> SwarmResult:\n", "    context_variables[\"1\"] = True\n", "    return SwarmResult(value=\"success\", context_variables=context_variables)\n", "\n", "\n", "# 2. A function that returns an SwarmAgent object\n", "def transfer_to_agent_2() -> SwarmAgent:\n", "    \"\"\"Transfer to agent 2\"\"\"\n", "    return agent_2\n", "\n", "\n", "# 3. A function that returns the value of \"success\", updates the context variable and transfers to agent 3\n", "def update_context_2_and_transfer_to_3(context_variables: dict) -> SwarmResult:\n", "    context_variables[\"2\"] = True\n", "    return SwarmResult(value=\"success\", context_variables=context_variables, agent=agent_3)\n", "\n", "\n", "# 4. A function that returns a normal value\n", "def get_random_number() -> str:\n", "    return random.randint(1, 100)\n", "\n", "\n", "def update_context_3_with_random_number(context_variables: dict, random_number: int) -> SwarmResult:\n", "    context_variables[\"3\"] = random_number\n", "    return SwarmResult(value=\"success\", context_variables=context_variables)\n", "\n", "\n", "agent_1 = SwarmAgent(\n", "    name=\"Agent_1\",\n", "    system_message=\"You are Agent 1, first, call the function to update context 1, and transfer to Agent 2\",\n", "    llm_config=llm_config,\n", "    functions=[update_context_1, transfer_to_agent_2],\n", ")\n", "\n", "agent_2 = SwarmAgent(\n", "    name=\"Agent_2\",\n", "    system_message=\"You are Agent 2, call the function that updates context 2 and transfer to Agent 3\",\n", "    llm_config=llm_config,\n", "    functions=[update_context_2_and_transfer_to_3],\n", ")\n", "\n", "agent_3 = SwarmAgent(\n", "    name=\"Agent_3\",\n", "    system_message=\"You are Agent 3, tell a joke\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "agent_4 = SwarmAgent(\n", "    name=\"Agent_4\",\n", "    system_message=\"You are Agent 4, call the function to get a random number\",\n", "    llm_config=llm_config,\n", "    functions=[get_random_number],\n", ")\n", "\n", "agent_5 = SwarmAgent(\n", "    name=\"Agent_5\",\n", "    system_message=\"Update context 3 with the random number.\",\n", "    llm_config=llm_config,\n", "    functions=[update_context_3_with_random_number],\n", ")\n", "\n", "\n", "# This is equivalent to writing a transfer function\n", "agent_3.register_hand_off(ON_CONDITION(agent_4, \"Transfer to Agent 4\"))\n", "\n", "agent_4.register_hand_off([AFTER_WORK(agent_5)])\n", "\n", "print(\"Agent 1 function schema:\")\n", "for func_schema in agent_1.llm_config[\"tools\"]:\n", "    print(func_schema)\n", "\n", "print(\"Agent 3 function schema:\")\n", "for func_schema in agent_3.llm_config[\"tools\"]:\n", "    print(func_schema)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Start Chat"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m_User\u001b[0m (to chat_manager):\n", "\n", "start\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent_1\n", "\u001b[0m\n", "\u001b[33mAgent_1\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Suggested tool call (call_kfcEAY2IeRZww06CQN7lbxOf): update_context_1 *****\u001b[0m\n", "Arguments: \n", "{}\n", "\u001b[32m*********************************************************************************\u001b[0m\n", "\u001b[32m***** Suggested tool call (call_izl5eyV8IQ0Wg6XY2SaR1EJM): transfer_to_agent_2 *****\u001b[0m\n", "Arguments: \n", "{}\n", "\u001b[32m************************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Tool_Execution\n", "\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION update_context_1...\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION transfer_to_agent_2...\u001b[0m\n", "\u001b[33mTool_Execution\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Response from calling tool (call_kfcEAY2IeRZww06CQN7lbxOf) *****\u001b[0m\n", "\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m***** Response from calling tool (call_izl5eyV8IQ0Wg6XY2SaR1EJM) *****\u001b[0m\n", "SwarmAgent --> Agent_2\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent<PERSON>2\n", "\u001b[0m\n", "\u001b[33mAgent_2\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Suggested tool call (call_Yf5DTGaaYkA726ubnfJAvQMq): update_context_2_and_transfer_to_3 *****\u001b[0m\n", "Arguments: \n", "{}\n", "\u001b[32m***************************************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Tool_Execution\n", "\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION update_context_2_and_transfer_to_3...\u001b[0m\n", "\u001b[33mTool_Execution\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Response from calling tool (call_Yf5DTGaaYkA726ubnfJAvQMq) *****\u001b[0m\n", "\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent<PERSON>3\n", "\u001b[0m\n", "\u001b[33mAgent_3\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Suggested tool call (call_jqZNHuMtQYeNh5Mq4pV2uwAj): transfer_to_Agent_4 *****\u001b[0m\n", "Arguments: \n", "{}\n", "\u001b[32m************************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Tool_Execution\n", "\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION transfer_to_Agent_4...\u001b[0m\n", "\u001b[33mTool_Execution\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Response from calling tool (call_jqZNHuMtQYeNh5Mq4pV2uwAj) *****\u001b[0m\n", "SwarmAgent --> Agent_4\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent<PERSON>4\n", "\u001b[0m\n", "\u001b[33mAgent_4\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Suggested tool call (call_KeNGv98klvDZsrAX10Ou3I71): get_random_number *****\u001b[0m\n", "Arguments: \n", "{}\n", "\u001b[32m**********************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Tool_Execution\n", "\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION get_random_number...\u001b[0m\n", "\u001b[33mTool_Execution\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Response from calling tool (call_KeNGv98klvDZsrAX10Ou3I71) *****\u001b[0m\n", "27\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent<PERSON>4\n", "\u001b[0m\n", "\u001b[33mAgent_4\u001b[0m (to chat_manager):\n", "\n", "The random number generated is 27.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent_5\n", "\u001b[0m\n", "\u001b[33mAgent_5\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Suggested tool call (call_MlSGNNktah3m3QGssWBEzxCe): update_context_3_with_random_number *****\u001b[0m\n", "Arguments: \n", "{\"random_number\":27}\n", "\u001b[32m****************************************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Tool_Execution\n", "\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION update_context_3_with_random_number...\u001b[0m\n", "\u001b[33mTool_Execution\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Response from calling tool (call_MlSGNNktah3m3QGssWBEzxCe) *****\u001b[0m\n", "\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent_5\n", "\u001b[0m\n", "\u001b[33mAgent_5\u001b[0m (to chat_manager):\n", "\n", "The random number 27 has been successfully updated in context 3.\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["context_variables = {\"1\": False, \"2\": False, \"3\": False}\n", "chat_result, context_variables, last_agent = initiate_swarm_chat(\n", "    initial_agent=agent_1,\n", "    agents=[agent_1, agent_2, agent_3, agent_4, agent_5],\n", "    messages=\"start\",\n", "    context_variables=context_variables,\n", "    after_work=AFTER_WORK(AfterWorkOption.TERMINATE),  # this is the default\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'1': True, '2': True, '3': 27}\n"]}], "source": ["print(context_variables)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Demo with User Agent\n", "\n", "We pass in a user agent to the swarm chat to accept user inputs. With `agent_6`, we register an `AFTER_WORK` handoff to revert to the user agent when no tool calls are suggested. "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mUser\u001b[0m (to chat_manager):\n", "\n", "start\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent<PERSON>6\n", "\u001b[0m\n", "\u001b[33mAgent_6\u001b[0m (to chat_manager):\n", "\n", "Why did the scarecrow win an award? \n", "\n", "Because he was outstanding in his field! \n", "\n", "Want to hear another one?\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: User\n", "\u001b[0m\n", "\u001b[33mUser\u001b[0m (to chat_manager):\n", "\n", "yes\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent<PERSON>6\n", "\u001b[0m\n", "\u001b[33mAgent_6\u001b[0m (to chat_manager):\n", "\n", "Why don't skeletons fight each other?\n", "\n", "They don't have the guts! \n", "\n", "How about another?\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: User\n", "\u001b[0m\n", "\u001b[33mUser\u001b[0m (to chat_manager):\n", "\n", "transfer\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent<PERSON>6\n", "\u001b[0m\n", "\u001b[33mAgent_6\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Suggested tool call (call_gQ9leFamxgzQp8ZVQB8rUH73): transfer_to_Agent_7 *****\u001b[0m\n", "Arguments: \n", "{}\n", "\u001b[32m************************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Tool_Execution\n", "\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION transfer_to_Agent_7...\u001b[0m\n", "\u001b[33mTool_Execution\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Response from calling tool (call_gQ9leFamxgzQp8ZVQB8rUH73) *****\u001b[0m\n", "SwarmAgent --> Agent_7\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: Agent_7\n", "\u001b[0m\n", "\u001b[33mAgent_7\u001b[0m (to chat_manager):\n", "\n", "The joke about the scarecrow winning an award is a play on words. It utilizes the term \"outstanding,\" which can mean both exceptionally good (in the context of the scarecrow's performance) and literally being \"standing out\" in a field (where scarecrows are placed). So, the double meaning creates a pun that makes the joke humorous. \n", "\n", "The skeleton joke works similarly. When it says skeletons \"don't have the guts,\" it plays on the literal fact that skeletons don't have internal organs (guts), and metaphorically, \"having guts\" means having courage. The humor comes from this clever wordplay.\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["from autogen import UserProxyAgent\n", "\n", "user_agent = UserProxyAgent(name=\"User\", code_execution_config=False)\n", "\n", "agent_6 = SwarmAgent(\n", "    name=\"Agent_6\",\n", "    system_message=\"You are Agent 6. Your job is to tell jokes.\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "agent_7 = SwarmAgent(\n", "    name=\"Agent_7\",\n", "    system_message=\"You are Agent 7, explain the joke.\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "agent_6.register_hand_off(\n", "    [\n", "        ON_CONDITION(\n", "            agent_7, \"Used to transfer to Agent 7. Don't call this function, unless the user explicitly tells you to.\"\n", "        ),\n", "        AFTER_WORK(AfterWorkOption.REVERT_TO_USER),\n", "    ]\n", ")\n", "\n", "chat_result, _, _ = initiate_swarm_chat(\n", "    initial_agent=agent_6,\n", "    agents=[agent_6, agent_7],\n", "    user_agent=user_agent,\n", "    messages=\"start\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "autodev", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}