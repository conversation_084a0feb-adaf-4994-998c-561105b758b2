{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Custom Code Executor\n", "\n", "In this guide we will show you how to create a custom code executor that runs\n", "code inside the same <PERSON><PERSON><PERSON> notebook as this one.\n", "\n", "First, let's install the required dependencies:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip -qqq install pyautogen matplotlib yfinance"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import os\n", "from typing import List\n", "\n", "from IPython import get_ipython\n", "\n", "from autogen import ConversableAgent\n", "from autogen.coding import <PERSON><PERSON><PERSON>, CodeExecutor, CodeExtractor, CodeResult, MarkdownCodeExtractor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can create the custom code executor class by subclassing the\n", "`CodeExecutor` protocol and implementing the `execute_code_blocks` method."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["class NotebookExecutor(CodeExecutor):\n", "\n", "    @property\n", "    def code_extractor(self) -> CodeExtractor:\n", "        # Extact code from markdown blocks.\n", "        return MarkdownCodeExtractor()\n", "\n", "    def __init__(self) -> None:\n", "        # Get the current IPython instance running in this notebook.\n", "        self._ipython = get_ipython()\n", "\n", "    def execute_code_blocks(self, code_blocks: List[CodeBlock]) -> CodeResult:\n", "        log = \"\"\n", "        for code_block in code_blocks:\n", "            result = self._ipython.run_cell(\"%%capture --no-display cap\\n\" + code_block.code)\n", "            log += self._ipython.ev(\"cap.stdout\")\n", "            log += self._ipython.ev(\"cap.stderr\")\n", "            if result.result is not None:\n", "                log += str(result.result)\n", "            exitcode = 0 if result.success else 1\n", "            if result.error_before_exec is not None:\n", "                log += f\"\\n{result.error_before_exec}\"\n", "                exitcode = 1\n", "            if result.error_in_exec is not None:\n", "                log += f\"\\n{result.error_in_exec}\"\n", "                exitcode = 1\n", "            if exitcode != 0:\n", "                break\n", "        return CodeResult(exit_code=exitcode, output=log)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can use the new custom code executor in our agents."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["code_writer_agent = ConversableAgent(\n", "    name=\"CodeWriter\",\n", "    system_message=\"You are a helpful AI assistant.\\n\"\n", "    \"You use your coding skill to solve problems.\\n\"\n", "    \"You have access to a IPython kernel to execute Python code.\\n\"\n", "    \"You can suggest Python code in Markdown blocks, each block is a cell.\\n\"\n", "    \"The code blocks will be executed in the IPython kernel in the order you suggest them.\\n\"\n", "    \"All necessary libraries have already been installed.\\n\"\n", "    \"Once the task is done, returns 'TERMINATE'.\",\n", "    llm_config={\"config_list\": [{\"model\": \"gpt-4\", \"api_key\": os.getenv(\"OPENAI_API_KEY\")}]},\n", ")\n", "\n", "code_executor_agent = ConversableAgent(\n", "    name=\"CodeExecutor\",\n", "    llm_config=False,\n", "    code_execution_config={\"executor\": NotebookExecutor()},\n", "    is_termination_msg=lambda msg: \"TERMINATE\" in msg.get(\"content\", \"\").strip().upper(),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's use the agents to complete a simple task of drawing a plot showing\n", "the market caps of the top 7 publicly listed companies."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mCodeExecutor\u001b[0m (to CodeWriter):\n", "\n", "Create a plot showing the market caps of the top 7 publicly listed companies using data from Yahoo Finance.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeWriter\u001b[0m (to CodeExecutor):\n", "\n", "To accomplish this task, we would use the `yfinance` library to fetch data from Yahoo Finance, `pandas` library for data manipulation, and `matplotlib` for data visualization.\n", "\n", "Steps:\n", "1. Identify the tickers for the top 7 largest publicly listed companies. Currently, these companies are: Apple (AAPL), Microsoft (MSFT), Google (GOOGL), Amazon (AMZN), Facebook (FB), Tesla (TSLA), and Berkshire Hathaway (BRK-A).\n", "2. Fetch market cap information for these companies using yfinance.\n", "3. Clean and process the fetched data into a usable format (a pandas DataFrame).\n", "4. Plot the market caps for these companies.\n", "\n", "Let's start by fetching data for these companies.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeExecutor\u001b[0m (to CodeWriter):\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeWriter\u001b[0m (to CodeExecutor):\n", "\n", "Great! Before I continue, I need to know if the necessary libraries are installed.\n", "\n", "The libraries needed for this task are:\n", "1. `yfinance`\n", "2. `pandas`\n", "3. `<PERSON><PERSON><PERSON><PERSON><PERSON>`\n", "\n", "If these libraries are not installed, you can install them using pip:\n", "\n", "```python\n", "!pip install yfinance pandas matplotlib\n", "```\n", "\n", "Assuming these libraries are installed, we would import them and proceed with fetching the market cap data:\n", "\n", "```python\n", "import yfinance as yf\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Define tickers \n", "tickers = [\"AAPL\", \"MSFT\", \"GOOGL\", \"AMZN\", \"FB\", \"TSLA\", \"BRK-A\"]\n", "\n", "# Fetch data\n", "data = yf.download(tickers, start=\"2022-02-01\", end=\"2022-02-28\")\n", "\n", "# Extract only 'Close' values for each ticker\n", "data = data['Close']\n", "\n", "# Create empty dictionary to hold market cap data\n", "market_caps = {}\n", "\n", "# Calculate market caps\n", "for ticker in tickers:\n", "    info = yf.Ticker(ticker).info\n", "    market_caps[ticker] = info[\"marketCap\"]\n", "\n", "# Convert market_caps dictionary to pandas DataFrame\n", "df = pd.DataFrame(list(market_caps.items()), columns=['Company', 'Market_Cap'])\n", "\n", "# Sort DataFrame by Market_Cap in descending order\n", "df = df.sort_values('Market_Cap', ascending=False)\n", "\n", "# Plot data\n", "plt.figure(figsize=(10,6))\n", "plt.barh(df['Company'], df['Market_Cap'], color='skyblue')\n", "plt.xlabel('Market Cap (in trillions)')\n", "plt.title('Market Caps of Top 7 Publicly Listed Companies')\n", "plt.gca().invert_yaxis()\n", "plt.show()\n", "```\n", "\n", "Please note that the start and end dates used while fetching data specifies the time period we are interested in. Feel free to modify these as needed. The 'marketCap' obtained from the 'info' property of the Ticker object represents the market cap as of the end date.\n", "\n", "Also note that the final plot shows the companies in descending order of market cap, with the company with the highest market cap at the top of the chart.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> EXECUTING 2 CODE BLOCKS (inferred languages are [python, python])...\u001b[0m\n"]}, {"ename": "KeyError", "evalue": "'marketCap'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[24], line 20\u001b[0m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m ticker \u001b[38;5;129;01min\u001b[39;00m tickers:\n\u001b[1;32m     19\u001b[0m     info \u001b[38;5;241m=\u001b[39m yf\u001b[38;5;241m.\u001b[39mTicker(ticker)\u001b[38;5;241m.\u001b[39minfo\n\u001b[0;32m---> 20\u001b[0m     market_caps[ticker] \u001b[38;5;241m=\u001b[39m \u001b[43minfo\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmarketCap\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[1;32m     22\u001b[0m \u001b[38;5;66;03m# Convert market_caps dictionary to pandas DataFrame\u001b[39;00m\n\u001b[1;32m     23\u001b[0m df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame(\u001b[38;5;28mlist\u001b[39m(market_caps\u001b[38;5;241m.\u001b[39mitems()), columns\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCompany\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMarket_Cap\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: 'marketCap'"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33mCodeExecutor\u001b[0m (to CodeWriter):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: Requirement already satisfied: yfinance in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (0.2.36)\n", "Requirement already satisfied: pandas in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (2.2.1)\n", "Requirement already satisfied: matplotlib in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (3.8.3)\n", "Requirement already satisfied: numpy>=1.16.5 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (1.26.4)\n", "Requirement already satisfied: requests>=2.31 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (2.31.0)\n", "Requirement already satisfied: multitasking>=0.0.7 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (0.0.11)\n", "Requirement already satisfied: lxml>=4.9.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (5.0.1)\n", "Requirement already satisfied: appdirs>=1.4.4 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (1.4.4)\n", "Requirement already satisfied: pytz>=2022.5 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (2023.3.post1)\n", "Requirement already satisfied: frozendict>=2.3.4 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (2.4.0)\n", "Requirement already satisfied: peewee>=3.16.2 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (3.17.0)\n", "Requirement already satisfied: beautifulsoup4>=4.11.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (4.12.2)\n", "Requirement already satisfied: html5lib>=1.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (1.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from pandas) (2.8.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from pandas) (2023.4)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (1.2.0)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (4.47.2)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (1.4.5)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (23.2)\n", "Requirement already satisfied: pillow>=8 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (10.2.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (3.1.1)\n", "Requirement already satisfied: soupsieve>1.2 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from beautifulsoup4>=4.11.1->yfinance) (2.5)\n", "Requirement already satisfied: six>=1.9 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from html5lib>=1.1->yfinance) (1.16.0)\n", "Requirement already satisfied: webencodings in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from html5lib>=1.1->yfinance) (0.5.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from requests>=2.31->yfinance) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from requests>=2.31->yfinance) (3.6)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from requests>=2.31->yfinance) (1.26.18)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from requests>=2.31->yfinance) (2024.2.2)\n", "/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[**************        29%%                      ]  2 of 7 completed/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[********************* 43%%                      ]  3 of 7 completed/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[**********************57%%*                     ]  4 of 7 completed/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[**********************71%%********              ]  5 of 7 completed/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[*********************100%%**********************]  7 of 7 completed\n", "\n", "1 Failed download:\n", "['FB']: Exception('%ticker%: No timezone found, symbol may be delisted')\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeWriter\u001b[0m (to CodeExecutor):\n", "\n", "From the error message, it seems that the 'FB' ticker (Facebook) failed to download because it might have been delisted. This is likely due to Facebook's corporate rebranding to Meta Platforms Inc. in late 2021, which resulted in a ticker change from 'FB' to 'META'.\n", "\n", "To resolve this, we'll replace 'FB' in our tickers list with 'META' and then retrieve the data again. Here is the modified code:\n", "\n", "```python\n", "# Define tickers \n", "tickers = [\"AAPL\", \"MSFT\", \"GOOGL\", \"AMZN\", \"META\", \"TSLA\", \"BRK-A\"]\n", "\n", "# Fetch data\n", "data = yf.download(tickers, start=\"2022-02-01\", end=\"2022-02-28\")\n", "\n", "# Extract only 'Close' values for each ticker\n", "data = data['Close']\n", "\n", "# Create empty dictionary to hold market cap data\n", "market_caps = {}\n", "\n", "# Calculate market caps\n", "for ticker in tickers:\n", "    info = yf.Ticker(ticker).info\n", "    market_caps[ticker] = info[\"marketCap\"]\n", "\n", "# Convert market_caps dictionary to pandas DataFrame\n", "df = pd.DataFrame(list(market_caps.items()), columns=['Company', 'Market_Cap'])\n", "\n", "# Sort DataFrame by Market_Cap in descending order\n", "df = df.sort_values('Market_Cap', ascending=False)\n", "\n", "# Plot data\n", "plt.figure(figsize=(10,6))\n", "plt.barh(df['Company'], df['Market_Cap'], color='skyblue')\n", "plt.xlabel('Market Cap (in trillions)')\n", "plt.title('Market Caps of Top 7 Publicly Listed Companies')\n", "plt.gca().invert_yaxis()\n", "plt.show()\n", "```\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK (inferred language is python)...\u001b[0m\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33mCodeExecutor\u001b[0m (to CodeWriter):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[                       0%%                      ]/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[********************* 43%%                      ]  3 of 7 completed/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[**********************57%%*                     ]  4 of 7 completed/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[**********************71%%********              ]  5 of 7 completed/Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages/yfinance/utils.py:775: FutureWarning: The 'unit' keyword in TimedeltaIndex construction is deprecated and will be removed in a future version. Use pd.to_timedelta instead.\n", "  df.index += _pd.TimedeltaIndex(dst_error_hours, 'h')\n", "[*********************100%%**********************]  7 of 7 completed\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeWriter\u001b[0m (to CodeExecutor):\n", "\n", "I see that the fetched data was successfully retrieved and processed. However, it looks like the result of the plot isn't visible, so we don't know whether the plot was generated successfully. Please run the code again and provide the output of the plot.\n", "\n", "If there are any issues or any other points you would like me to help with, let me know!\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeExecutor\u001b[0m (to CodeWriter):\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeWriter\u001b[0m (to CodeExecutor):\n", "\n", "I'm glad we were able to retrieve and process the data successfully. Please try running the last part of the code again to generate and display the plot:\n", "\n", "```python\n", "# Plot data\n", "plt.figure(figsize=(10,6))\n", "plt.barh(df['Company'], df['Market_Cap'], color='skyblue')\n", "plt.xlabel('Market Cap (in trillions)')\n", "plt.title('Market Caps of Top 7 Publicly Listed Companies')\n", "plt.gca().invert_yaxis()\n", "plt.show()\n", "```\n", "\n", "This section of the code creates a horizontal bar plot of the market capitalizations of the companies. The `plt.gca().invert_yaxis()` line is included to invert the y-axis, so the company with the highest market cap is at the top of the chart.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK (inferred language is python)...\u001b[0m\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33mCodeExecutor\u001b[0m (to CodeWriter):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: \n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeWriter\u001b[0m (to CodeExecutor):\n", "\n", "I see that the code has executed successfully, but unfortunately, the generated plot is not visible here. However, given that there are no errors, it's likely that the plot has been created as expected when you executed the code on your end.\n", "\n", "If you have any other questions related to this code or need further assistance with Python coding or data visualization, please let me know! I'm here to help.\n", "\n", "Otherwise, if this completes your initial request, I will end this task. Just let me know your decision.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeExecutor\u001b[0m (to CodeWriter):\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mCodeWriter\u001b[0m (to CodeExecutor):\n", "\n", "Alright. If you have any more questions regarding this task, or if you need help with other tasks in the future, don't hesitate to ask. Have a great day!\n", "\n", "'---TERMINATE---'\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["chat_result = code_executor_agent.initiate_chat(\n", "    code_writer_agent,\n", "    message=\"Create a plot showing the market caps of the top 7 publicly listed companies using data from Yahoo Finance.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can see the plots are now displayed in the current notebook."]}], "metadata": {"kernelspec": {"display_name": "autogen", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}