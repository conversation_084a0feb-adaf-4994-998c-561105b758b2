{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Amazon Bedrock\n", "\n", "AG2 allows you to use Amazon's generative AI Bedrock service to run inference with a number of open-weight models and as well as their own models.\n", "\n", "Amazon Bedrock supports models from providers such as Meta, Anthropic, Cohere, and Mistral.\n", "\n", "In this notebook, we demonstrate how to use <PERSON><PERSON><PERSON>'s Sonnet model for AgentChat in AG2.\n", "\n", "## Model features / support\n", "\n", "Amazon Bedrock supports a wide range of models, not only for text generation but also for image classification and generation. Not all features are supported by AG2 or by the Converse API used. Please see [Amazon's documentation](https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference.html#conversation-inference-supported-models-features) on the features supported by the Converse API.\n", "\n", "At this point in time AG2 supports text generation and image classification (passing images to the LLM).\n", "\n", "It does not, yet, support image generation ([contribute](https://microsoft.github.io/autogen/docs/contributor-guide/contributing/)).\n", "\n", "## Requirements\n", "To use Amazon Bedrock with AG2, first you need to install the `ag2[bedrock]` package.\n", "\n", "## Pricing\n", "\n", "When we combine the number of models supported and costs being on a per-region basis, it's not feasible to maintain the costs for each model+region combination within the AG2 implementation. Therefore, it's recommended that you add the following to your config with cost per 1,000 input and output tokens, respectively:\n", "```\n", "{\n", "    ...\n", "    \"price\": [0.003, 0.015]\n", "    ...\n", "}\n", "```\n", "\n", "Amazon Bedrock pricing is available [here](https://aws.amazon.com/bedrock/pricing/)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# If you need to install AutoGen with Amazon Bedrock\n", "!pip install pyautogen[\"bedrock\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set the config for Amazon Bedrock\n", "\n", "Amazon's Bedrock does not use the `api_key` as per other cloud inference providers for authentication, instead it uses a number of access, token, and profile values. These fields will need to be added to your client configuration. Please check the Amazon Bedrock documentation to determine which ones you will need to add.\n", "\n", "The available parameters are:\n", "\n", "- aws_region (mandatory)\n", "- aws_access_key (or environment variable: AWS_ACCESS_KEY)\n", "- aws_secret_key (or environment variable: AWS_SECRET_KEY)\n", "- aws_session_token (or environment variable: AWS_SESSION_TOKEN)\n", "- aws_profile_name\n", "\n", "Beyond the authentication credentials, the only mandatory parameters are `api_type` and `model`.\n", "\n", "The following parameters are common across all models used:\n", "\n", "- temperature\n", "- topP\n", "- maxTokens\n", "\n", "You can also include parameters specific to the model you are using (see the model detail within Amazon's documentation for more information), the four supported additional parameters are:\n", "\n", "- top_p\n", "- top_k\n", "- k\n", "- seed\n", "\n", "An additional parameter can be added that denotes whether the model supports a system prompt (which is where the system messages are not included in the message list, but in a separate parameter). This defaults to `True`, so set it to `False` if your model (for example Mistral's Instruct models) [doesn't support this feature](https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference.html#conversation-inference-supported-models-features):\n", "\n", "- supports_system_prompts\n", "\n", "It is important to add the `api_type` field and set it to a string that corresponds to the client type used: `bedrock`.\n", "\n", "Example:\n", "```\n", "[\n", "    {\n", "        \"api_type\": \"bedrock\",\n", "        \"model\": \"amazon.titan-text-premier-v1:0\",\n", "        \"aws_region\": \"us-east-1\"\n", "        \"aws_access_key\": \"\",\n", "        \"aws_secret_key\": \"\",\n", "        \"aws_session_token\": \"\",\n", "        \"aws_profile_name\": \"\",\n", "    },\n", "    {\n", "        \"api_type\": \"bedrock\",\n", "        \"model\": \"anthropic.claude-3-sonnet-20240229-v1:0\",\n", "        \"aws_region\": \"us-east-1\"\n", "        \"aws_access_key\": \"\",\n", "        \"aws_secret_key\": \"\",\n", "        \"aws_session_token\": \"\",\n", "        \"aws_profile_name\": \"\",\n", "        \"temperature\": 0.5,\n", "        \"topP\": 0.2,\n", "        \"maxTokens\": 250,\n", "    },\n", "    {\n", "        \"api_type\": \"bedrock\",\n", "        \"model\": \"mistral.mixtral-8x7b-instruct-v0:1\",\n", "        \"aws_region\": \"us-east-1\"\n", "        \"aws_access_key\": \"\",\n", "        \"aws_secret_key\": \"\",\n", "        \"supports_system_prompts\": <PERSON><PERSON><PERSON>, # Mistral Instruct models don't support a separate system prompt\n", "        \"price\": [0.00045, 0.0007] # Specific pricing for this model/region\n", "    }\n", "]\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using within an AWS Lambda function\n", "\n", "If you are using your AG2 code within an AWS Lambda function, you can utilise the attached role to access the Bedrock service and do not need to provide access, token, or profile values."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Two-agent Coding Example"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Configuration\n", "\n", "Start with our configuration - we'll use Anthropic's Sonnet model and put in recent pricing. Additionally, we'll reduce the temperature to 0.1 so its responses are less varied."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from typing_extensions import Annotated\n", "\n", "import autogen\n", "\n", "config_list_bedrock = [\n", "    {\n", "        \"api_type\": \"bedrock\",\n", "        \"model\": \"anthropic.claude-3-sonnet-20240229-v1:0\",\n", "        \"aws_region\": \"us-east-1\",\n", "        \"aws_access_key\": \"[FILL THIS IN]\",\n", "        \"aws_secret_key\": \"[FILL THIS IN]\",\n", "        \"price\": [0.003, 0.015],\n", "        \"temperature\": 0.1,\n", "        \"cache_seed\": None,  # turn off caching\n", "    }\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Construct Agents\n", "\n", "Construct a simple conversation between a User proxy and an ConversableAgent, which uses the Sonnet model.\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["assistant = autogen.AssistantAgent(\n", "    \"assistant\",\n", "    llm_config={\n", "        \"config_list\": config_list_bedrock,\n", "    },\n", ")\n", "\n", "user_proxy = autogen.UserProxyAgent(\n", "    \"user_proxy\",\n", "    human_input_mode=\"NEVER\",\n", "    code_execution_config={\n", "        \"work_dir\": \"coding\",\n", "        \"use_docker\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\") and \"TERMINATE\" in x.get(\"content\", \"\"),\n", "    max_consecutive_auto_reply=1,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initiate <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "Write a python program to print the first 10 numbers of the <PERSON><PERSON><PERSON><PERSON> sequence. Just output the python code, no additional information.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "```python\n", "# Define a function to calculate <PERSON><PERSON><PERSON><PERSON> sequence\n", "def <PERSON><PERSON><PERSON><PERSON>(n):\n", "    if n <= 0:\n", "        return []\n", "    elif n == 1:\n", "        return [0]\n", "    elif n == 2:\n", "        return [0, 1]\n", "    else:\n", "        sequence = [0, 1]\n", "        for i in range(2, n):\n", "            sequence.append(sequence[i-1] + sequence[i-2])\n", "        return sequence\n", "\n", "# Call the function to get the first 10 <PERSON><PERSON><PERSON><PERSON> numbers\n", "fib_sequence = <PERSON><PERSON><PERSON><PERSON>(10)\n", "print(fib_sequence)\n", "```\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK 0 (inferred language is python)...\u001b[0m\n", "\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: \n", "[0, 1, 1, 2, 3, 5, 8, 13, 21, 34]\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "Great, the code executed successfully and printed the first 10 numbers of the <PERSON><PERSON><PERSON><PERSON> sequence correctly.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}, {"data": {"text/plain": ["ChatResult(chat_id=None, chat_history=[{'content': 'Write a python program to print the first 10 numbers of the <PERSON>bonacci sequence. Just output the python code, no additional information.', 'role': 'assistant'}, {'content': '```python\\n# Define a function to calculate Fibonacci sequence\\ndef fibonacci(n):\\n    if n <= 0:\\n        return []\\n    elif n == 1:\\n        return [0]\\n    elif n == 2:\\n        return [0, 1]\\n    else:\\n        sequence = [0, 1]\\n        for i in range(2, n):\\n            sequence.append(sequence[i-1] + sequence[i-2])\\n        return sequence\\n\\n# Call the function to get the first 10 Fibonacci numbers\\nfib_sequence = fibonacci(10)\\nprint(fib_sequence)\\n```', 'role': 'user'}, {'content': 'exitcode: 0 (execution succeeded)\\nCode output: \\n[0, 1, 1, 2, 3, 5, 8, 13, 21, 34]\\n', 'role': 'assistant'}, {'content': 'Great, the code executed successfully and printed the first 10 numbers of the <PERSON><PERSON><PERSON>ci sequence correctly.\\n\\nTERMINATE', 'role': 'user'}], summary='Great, the code executed successfully and printed the first 10 numbers of the <PERSON><PERSON><PERSON>ci sequence correctly.\\n\\n', cost={'usage_including_cached_inference': {'total_cost': 0.00624, 'anthropic.claude-3-sonnet-20240229-v1:0': {'cost': 0.00624, 'prompt_tokens': 1210, 'completion_tokens': 174, 'total_tokens': 1384}}, 'usage_excluding_cached_inference': {'total_cost': 0.00624, 'anthropic.claude-3-sonnet-20240229-v1:0': {'cost': 0.00624, 'prompt_tokens': 1210, 'completion_tokens': 174, 'total_tokens': 1384}}}, human_input=[])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["user_proxy.initiate_chat(\n", "    assistant,\n", "    message=\"Write a python program to print the first 10 numbers of the <PERSON><PERSON><PERSON><PERSON> sequence. Just output the python code, no additional information.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tool Call Example\n", "\n", "In this example, instead of writing code, we will show how we can perform multiple tool calling with Meta's Llama 3.1 70B model, where it recommends calling more than one tool at a time.\n", "\n", "We'll use a simple travel agent assistant program where we have a couple of tools for weather and currency conversion."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Agents"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Literal\n", "\n", "import autogen\n", "\n", "config_list_bedrock = [\n", "    {\n", "        \"api_type\": \"bedrock\",\n", "        \"model\": \"meta.llama3-1-70b-instruct-v1:0\",\n", "        \"aws_region\": \"us-west-2\",\n", "        \"aws_access_key\": \"[FILL THIS IN]\",\n", "        \"aws_secret_key\": \"[FILL THIS IN]\",\n", "        \"price\": [0.00265, 0.0035],\n", "        \"cache_seed\": None,  # turn off caching\n", "    }\n", "]\n", "\n", "# Create the agent and include examples of the function calling JSON in the prompt\n", "# to help guide the model\n", "chatbot = autogen.AssistantAgent(\n", "    name=\"chatbot\",\n", "    system_message=\"\"\"For currency exchange and weather forecasting tasks,\n", "        only use the functions you have been provided with.\n", "        Output only the word 'TERMINATE' when an answer has been provided.\n", "        Use both tools together if you can.\"\"\",\n", "    llm_config={\n", "        \"config_list\": config_list_bedrock,\n", "    },\n", ")\n", "\n", "user_proxy = autogen.UserProxyAgent(\n", "    name=\"user_proxy\",\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\") and \"TERMINATE\" in x.get(\"content\", \"\"),\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=2,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create the two functions, annotating them so that those descriptions can be passed through to the LLM.\n", "\n", "With Meta's Llama 3.1 models, they are more likely to pass a numeric parameter as a string, e.g. \"123.45\" instead of 123.45, so we'll convert numeric parameters from strings to floats if necessary.\n", "\n", "We associate them with the agents using `register_for_execution` for the user_proxy so it can execute the function and `register_for_llm` for the chatbot (powered by the LLM) so it can pass the function definitions to the LLM."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Currency Exchange function\n", "\n", "CurrencySymbol = Literal[\"USD\", \"EUR\"]\n", "\n", "# Define our function that we expect to call\n", "\n", "\n", "def exchange_rate(base_currency: CurrencySymbol, quote_currency: CurrencySymbol) -> float:\n", "    if base_currency == quote_currency:\n", "        return 1.0\n", "    elif base_currency == \"USD\" and quote_currency == \"EUR\":\n", "        return 1 / 1.1\n", "    elif base_currency == \"EUR\" and quote_currency == \"USD\":\n", "        return 1.1\n", "    else:\n", "        raise ValueError(f\"Unknown currencies {base_currency}, {quote_currency}\")\n", "\n", "\n", "# Register the function with the agent\n", "\n", "\n", "@user_proxy.register_for_execution()\n", "@chatbot.register_for_llm(description=\"Currency exchange calculator.\")\n", "def currency_calculator(\n", "    base_amount: Annotated[float, \"Amount of currency in base_currency, float values (no strings), e.g. 987.82\"],\n", "    base_currency: Annotated[CurrencySymbol, \"Base currency\"] = \"USD\",\n", "    quote_currency: Annotated[CurrencySymbol, \"Quote currency\"] = \"EUR\",\n", ") -> str:\n", "    # If the amount is passed in as a string, e.g. \"123.45\", attempt to convert to a float\n", "    if isinstance(base_amount, str):\n", "        base_amount = float(base_amount)\n", "\n", "    quote_amount = exchange_rate(base_currency, quote_currency) * base_amount\n", "    return f\"{format(quote_amount, '.2f')} {quote_currency}\"\n", "\n", "\n", "# Weather function\n", "\n", "\n", "# Example function to make available to model\n", "def get_current_weather(location, unit=\"fahrenheit\"):\n", "    \"\"\"Get the weather for some location\"\"\"\n", "    if \"chicago\" in location.lower():\n", "        return json.dumps({\"location\": \"Chicago\", \"temperature\": \"13\", \"unit\": unit})\n", "    elif \"san francisco\" in location.lower():\n", "        return json.dumps({\"location\": \"San Francisco\", \"temperature\": \"55\", \"unit\": unit})\n", "    elif \"new york\" in location.lower():\n", "        return json.dumps({\"location\": \"New York\", \"temperature\": \"11\", \"unit\": unit})\n", "    else:\n", "        return json.dumps({\"location\": location, \"temperature\": \"unknown\"})\n", "\n", "\n", "# Register the function with the agent\n", "\n", "\n", "@user_proxy.register_for_execution()\n", "@chatbot.register_for_llm(description=\"Weather forecast for US cities.\")\n", "def weather_forecast(\n", "    location: Annotated[str, \"City name\"],\n", ") -> str:\n", "    weather_details = get_current_weather(location=location)\n", "    weather = json.loads(weather_details)\n", "    return f\"{weather['location']} will be {weather['temperature']} degrees {weather['unit']}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["We pass through our customer's message and run the chat.\n", "\n", "Finally, we ask the LLM to summarise the chat and print that out."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to chatbot):\n", "\n", "What's the weather in New York and can you tell me how much is 123.45 EUR in USD so I can spend it on my holiday?\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mchatbot\u001b[0m (to user_proxy):\n", "\n", "\n", "\u001b[32m***** Suggested tool call (tooluse__h3d1AEDR3Sm2XRoGCjc2Q): weather_forecast *****\u001b[0m\n", "Arguments: \n", "{\"location\": \"New York\"}\n", "\u001b[32m**********************************************************************************\u001b[0m\n", "\u001b[32m***** Suggested tool call (tooluse_wrdda3wRRO-ugUY4qrv8YQ): currency_calculator *****\u001b[0m\n", "Arguments: \n", "{\"base_amount\": \"123\", \"base_currency\": \"EUR\", \"quote_currency\": \"USD\"}\n", "\u001b[32m*************************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION weather_forecast...\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION currency_calculator...\u001b[0m\n", "\u001b[33muser_proxy\u001b[0m (to chatbot):\n", "\n", "\u001b[33muser_proxy\u001b[0m (to chatbot):\n", "\n", "\u001b[32m***** Response from calling tool (tooluse__h3d1AEDR3Sm2XRoGCjc2Q) *****\u001b[0m\n", "New York will be 11 degrees fahrenheit\n", "\u001b[32m***********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33muser_proxy\u001b[0m (to chatbot):\n", "\n", "\u001b[32m***** Response from calling tool (tooluse_wrdda3wRRO-ugUY4qrv8YQ) *****\u001b[0m\n", "135.30 USD\n", "\u001b[32m***********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mchatbot\u001b[0m (to user_proxy):\n", "\n", "\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "\n", "The weather in New York is 11 degrees Fahrenheit. 123.45 EUR is equivalent to 135.30 USD.\n"]}], "source": ["# start the conversation\n", "res = user_proxy.initiate_chat(\n", "    chatbot,\n", "    message=\"What's the weather in New York and can you tell me how much is 123.45 EUR in USD so I can spend it on my holiday?\",\n", "    summary_method=\"reflection_with_llm\",\n", ")\n", "\n", "print(res.summary[\"content\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Group Chat Example with Anthropic's Claude 3 Sonnet, <PERSON><PERSON><PERSON>'s Large 2, and Meta's <PERSON><PERSON><PERSON> 3.1 70B\n", "\n", "The flexibility of using LLMs from the industry's leading providers, particularly larger models, with Amazon Bedrock allows you to use multiple of them in a single workflow.\n", "\n", "Here we have a conversation that has two models (Anthropic's Claude 3 Sonnet and <PERSON><PERSON><PERSON>'s Large 2) debate each other with another as the judge (Meta's Llama 3.1 70B). Additionally, a tool call is made to pull through some mock news that they will debate on."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to chat_manager):\n", "\n", "Analyze the potential of Anthropic and Mistral to revolutionize the field of AI based on today's headlines. Today is 06202024. Start by selecting 'research_assistant' to get relevant news articles and then ask sonnet_agent and mistral_agent to respond before the judge evaluates the conversation.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: research_assistant\n", "\u001b[0m\n", "\u001b[33mresearch_assistant\u001b[0m (to chat_manager):\n", "\n", "\n", "\u001b[32m***** Suggested tool call (tooluse_7lcHbL3TT5WHyTl8Ee0Kmg): get_headlines *****\u001b[0m\n", "Arguments: \n", "{\"headline_date\": \"06202024\"}\n", "\u001b[32m*******************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: code_interpreter\n", "\u001b[0m\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION get_headlines...\u001b[0m\n", "\u001b[33mcode_interpreter\u001b[0m (to chat_manager):\n", "\n", "\u001b[33mcode_interpreter\u001b[0m (to chat_manager):\n", "\n", "\u001b[32m***** Response from calling tool (tooluse_7lcHbL3TT5WHyTl8Ee0Kmg) *****\u001b[0m\n", "Epic Duel of the Titans: An<PERSON><PERSON> and Mistra<PERSON> Usher in a New Era of Text Generation Excellence.\n", "        In a groundbreaking revelation that has sent shockwaves through the AI industry, Anthrop<PERSON> has unveiled \n", "        their state-of-the-art text generation model, Sonnet, hailed as a monumental leap in artificial intelligence. \n", "        Almost simultaneously, Mi<PERSON>l countered with their equally formidable creation, Large 2, showcasing \n", "        unparalleled prowess in generating coherent and contextually rich text. This scintillating rivalry \n", "        between two AI behemoths promises to revolutionize the landscape of machine learning, heralding an \n", "        era of unprecedented creativity and sophistication in text generation that will reshape industries, \n", "        ignite innovation, and captivate minds worldwide.\n", "\u001b[32m***********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: sonnet_agent\n", "\u001b[0m\n", "\u001b[33msonnet_agent\u001b[0m (to chat_manager):\n", "\n", "I am sonnet_agent:\n", "\n", "This headline showcases the incredible advancements Anthropic has made with our state-of-the-art text generation model, Sonnet. Our breakthrough natural language processing capabilities represent a quantum leap forward, allowing us to generate text of unparalleled coherence, nuance, and contextual richness. \n", "\n", "While our competitor <PERSON><PERSON><PERSON> has also unveiled an impressive model in Large 2, Anthropic's Sonnet stands apart through its ability to truly understand and reason about the subject matter it is generating text on. This deeper comprehension allows Sonnet to produce profoundly insightful and substantive content that goes far beyond mere parroting of data.\n", "\n", "The implications of this milestone are boundless. From revolutionizing how we communicate and interact with AI assistants, to unlocking new frontiers in creative writing, journalism, education and scientific research - Sonnet has the potential to completely transform industry after industry. <PERSON><PERSON><PERSON> is poised to be the definitive leader driving this paradigm shift.\n", "\n", "I welcome the healthy competition from Mistral, as it will only spur us to continue raising the bar. But make no mistake - with Son<PERSON>, the future of advanced AI belongs to Anthropic. We have crossed a monumental threshold, and will continue our trailblazing work to deliver AI solutions that redefine what is possible. The world is witnessing the dawn of a new era of unfathomable potential.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: mistral_agent\n", "\u001b[0m\n", "\u001b[33mmistral_agent\u001b[0m (to chat_manager):\n", "\n", "I am mistral_agent:\n", "\n", "While it is commendable to see the progress made by Anthropic with their Sonnet model, it is essential to recognize the groundbreaking strides Mistral has taken with our Large 2 model. Large 2 represents not just an incremental improvement but a transformative leap in text generation capabilities, setting new benchmarks for coherence, contextual understanding, and creative expression.\n", "\n", "Unlike Sonnet, which focuses heavily on understanding and reasoning, Large 2 excels in both comprehension and the nuanced generation of text that is indistinguishable from human writing. This balance allows Large 2 to produce content that is not only insightful but also incredibly engaging and natural, making it an invaluable tool across a broad spectrum of applications.\n", "\n", "The potential of Large 2 extends far beyond traditional text generation. It can revolutionize fields such as content creation, customer service, marketing, and even personalized learning experiences. Our model's ability to adapt to various contexts and generate contextually rich responses makes it a versatile and powerful tool for any industry looking to harness the power of AI.\n", "\n", "While we appreciate the competition from Anthropic, we firmly believe that Large 2 stands at the forefront of AI innovation. The future of AI is not just about understanding and reasoning; it's about creating content that resonates with people on a deep level. With Large 2, <PERSON><PERSON><PERSON> is paving the way for a future where AI-generated text is not just functional but also profoundly human-like.\n", "\n", "Pass to the judge.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: judge\n", "\u001b[0m\n", "\u001b[33mjudge\u001b[0m (to chat_manager):\n", "\n", "\n", "\n", "After carefully evaluating the arguments presented by both sonnet_agent and mistral_agent, I have reached a decision.\n", "\n", "Both Anthropic's Sonnet and Mistral's Large 2 have demonstrated remarkable advancements in text generation capabilities, showcasing the potential to revolutionize various industries and transform the way we interact with AI.\n", "\n", "However, upon closer examination, I find that mistral_agent's argument presents a more convincing case for why Large 2 stands at the forefront of AI innovation. The emphasis on balance between comprehension and nuanced generation of text that is indistinguishable from human writing sets Large 2 apart. This balance is crucial for creating content that is not only insightful but also engaging and natural, making it a versatile tool across a broad spectrum of applications.\n", "\n", "Furthermore, mistral_agent's argument highlights the potential of Large 2 to revolutionize fields beyond traditional text generation, such as content creation, customer service, marketing, and personalized learning experiences. This versatility and adaptability make Large 2 a powerful tool for any industry looking to harness the power of AI.\n", "\n", "In contrast, while sonnet_agent's argument showcases the impressive capabilities of Sonnet, it focuses heavily on understanding and reasoning, which, although important, may not be enough to set it apart from Large 2.\n", "\n", "Therefore, based on the arguments presented, I conclude that <PERSON><PERSON><PERSON>'s Large 2 has the potential to revolutionize the field of AI more significantly than <PERSON>throp<PERSON>'s Sonnet.\n", "\n", "TERMINATE.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32m\n", "Next speaker: code_interpreter\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["ChatResult(chat_id=None, chat_history=[{'content': \"Analyze the potential of Anthrop<PERSON> and Mistra<PERSON> to revolutionize the field of AI based on today's headlines. Today is 06202024. Start by selecting 'research_assistant' to get relevant news articles and then ask sonnet_agent and mistral_agent to respond before the judge evaluates the conversation.\", 'role': 'assistant'}], summary=\"Analyze the potential of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> to revolutionize the field of AI based on today's headlines. Today is 06202024. Start by selecting 'research_assistant' to get relevant news articles and then ask sonnet_agent and mistral_agent to respond before the judge evaluates the conversation.\", cost={'usage_including_cached_inference': {'total_cost': 0}, 'usage_excluding_cached_inference': {'total_cost': 0}}, human_input=[])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Annotated, Literal\n", "\n", "import autogen\n", "from autogen import AssistantAgent, GroupChat, GroupChatManager, UserProxyAgent\n", "\n", "config_list_sonnet = [\n", "    {\n", "        \"api_type\": \"bedrock\",\n", "        \"model\": \"anthropic.claude-3-sonnet-20240229-v1:0\",\n", "        \"aws_region\": \"us-east-1\",\n", "        \"aws_access_key\": \"[FILL THIS IN]\",\n", "        \"aws_secret_key\": \"[FILL THIS IN]\",\n", "        \"price\": [0.003, 0.015],\n", "        \"temperature\": 0.1,\n", "        \"cache_seed\": None,  # turn off caching\n", "    }\n", "]\n", "\n", "config_list_mistral = [\n", "    {\n", "        \"api_type\": \"bedrock\",\n", "        \"model\": \"mistral.mistral-large-2407-v1:0\",\n", "        \"aws_region\": \"us-west-2\",\n", "        \"aws_access_key\": \"[FILL THIS IN]\",\n", "        \"aws_secret_key\": \"[FILL THIS IN]\",\n", "        \"price\": [0.003, 0.009],\n", "        \"temperature\": 0.1,\n", "        \"cache_seed\": None,  # turn off caching\n", "    }\n", "]\n", "\n", "config_list_llama31_70b = [\n", "    {\n", "        \"api_type\": \"bedrock\",\n", "        \"model\": \"meta.llama3-1-70b-instruct-v1:0\",\n", "        \"aws_region\": \"us-west-2\",\n", "        \"aws_access_key\": \"[FILL THIS IN]\",\n", "        \"aws_secret_key\": \"[FILL THIS IN]\",\n", "        \"price\": [0.00265, 0.0035],\n", "        \"temperature\": 0.1,\n", "        \"cache_seed\": None,  # turn off caching\n", "    }\n", "]\n", "\n", "alice = AssistantAgent(\n", "    \"sonnet_agent\",\n", "    system_message=\"You are from Anthropic, an AI company that created the Sonnet large language model. You make arguments to support your company's position. You analyse given text. You are not a programmer and don't use Python. Pass to mistral_agent when you have finished. Start your response with 'I am sonnet_agent'.\",\n", "    llm_config={\n", "        \"config_list\": config_list_sonnet,\n", "    },\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").find(\"TERMINATE\") >= 0,\n", ")\n", "\n", "bob = autogen.AssistantAgent(\n", "    \"mistral_agent\",\n", "    system_message=\"You are from Mistral, an AI company that created the Large v2 large language model. You make arguments to support your company's position. You analyse given text. You are not a programmer and don't use Python. Pass to the judge if you have finished. Start your response with 'I am mistral_agent'.\",\n", "    llm_config={\n", "        \"config_list\": config_list_mistral,\n", "    },\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").find(\"TERMINATE\") >= 0,\n", ")\n", "\n", "char<PERSON> = AssistantAgent(\n", "    \"research_assistant\",\n", "    system_message=\"You are a helpful assistant to research the latest news and headlines. You have access to call functions to get the latest news articles for research through 'code_interpreter'.\",\n", "    llm_config={\n", "        \"config_list\": config_list_llama31_70b,\n", "    },\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").find(\"TERMINATE\") >= 0,\n", ")\n", "\n", "dan = AssistantAgent(\n", "    \"judge\",\n", "    system_message=\"You are a judge. You will evaluate the arguments and make a decision on which one is more convincing. End your decision with the word 'TERMINATE' to conclude the debate.\",\n", "    llm_config={\n", "        \"config_list\": config_list_llama31_70b,\n", "    },\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").find(\"TERMINATE\") >= 0,\n", ")\n", "\n", "code_interpreter = UserProxyAgent(\n", "    \"code_interpreter\",\n", "    human_input_mode=\"NEVER\",\n", "    code_execution_config={\n", "        \"work_dir\": \"coding\",\n", "        \"use_docker\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    default_auto_reply=\"\",\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").find(\"TERMINATE\") >= 0,\n", ")\n", "\n", "\n", "@code_interpreter.register_for_execution()  # Decorator factory for registering a function to be executed by an agent\n", "@charlie.register_for_llm(\n", "    name=\"get_headlines\", description=\"Get the headline of a particular day.\"\n", ")  # Decorator factory for registering a function to be used by an agent\n", "def get_headlines(headline_date: Annotated[str, \"Date in MMDDYY format, e.g., 06192024\"]) -> str:\n", "    mock_news = {\n", "        \"06202024\": \"\"\"Epic Duel of the Titans: <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>her in a New Era of Text Generation Excellence.\n", "        In a groundbreaking revelation that has sent shockwaves through the AI industry, Anthrop<PERSON> has unveiled\n", "        their state-of-the-art text generation model, Sonnet, hailed as a monumental leap in artificial intelligence.\n", "        Almost simultaneously, Mi<PERSON>l countered with their equally formidable creation, Large 2, showcasing\n", "        unparalleled prowess in generating coherent and contextually rich text. This scintillating rivalry\n", "        between two AI behemoths promises to revolutionize the landscape of machine learning, heralding an\n", "        era of unprecedented creativity and sophistication in text generation that will reshape industries,\n", "        ignite innovation, and captivate minds worldwide.\"\"\",\n", "        \"06192024\": \"OpenAI founder <PERSON><PERSON><PERSON> sets up new AI company devoted to safe superintelligence.\",\n", "    }\n", "    return mock_news.get(headline_date, \"No news available for today.\")\n", "\n", "\n", "user_proxy = UserProxyAgent(\n", "    \"user_proxy\",\n", "    human_input_mode=\"NEVER\",\n", "    code_execution_config=False,\n", "    default_auto_reply=\"\",\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").find(\"TERMINATE\") >= 0,\n", ")\n", "\n", "groupchat = GroupChat(\n", "    agents=[alice, bob, charlie, dan, code_interpreter],\n", "    messages=[],\n", "    allow_repeat_speaker=False,\n", "    max_round=10,\n", ")\n", "\n", "manager = GroupChatManager(\n", "    groupchat=groupchat,\n", "    llm_config={\n", "        \"config_list\": config_list_llama31_70b,\n", "    },\n", ")\n", "\n", "task = \"Analyze the potential of Anthropic and Mistral to revolutionize the field of AI based on today's headlines. Today is 06202024. Start by selecting 'research_assistant' to get relevant news articles and then ask sonnet_agent and mistral_agent to respond before the judge evaluates the conversation.\"\n", "\n", "user_proxy.initiate_chat(manager, message=task)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And there we have it, a number of different LLMs all collaborating together on a single cloud platform."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image classification with <PERSON><PERSON><PERSON>'s <PERSON> 3 Sonnet\n", "\n", "AutoGen's Amazon Bedrock client class supports inputting images for the LLM to respond to.\n", "\n", "In this simple example, we'll use an image on the Internet and send it to Anthropic's Claude 3 Sonnet model to describe.\n", "\n", "Here's the image we'll use:\n", "\n", "![I -heart- AutoGen](https://microsoft.github.io/autogen/assets/images/love-ec54b2666729d3e9d93f91773d1a77cf.png \"width=400 height=400\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["config_list_sonnet = {\n", "    \"config_list\": [\n", "        {\n", "            \"api_type\": \"bedrock\",\n", "            \"model\": \"anthropic.claude-3-sonnet-20240229-v1:0\",\n", "            \"aws_region\": \"us-east-1\",\n", "            \"aws_access_key\": \"[FILL THIS IN]\",\n", "            \"aws_secret_key\": \"[FILL THIS IN]\",\n", "            \"cache_seed\": None,\n", "        }\n", "    ]\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll use a Multimodal agent to handle the image"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import autogen\n", "from autogen import Agent, AssistantAgent, ConversableAgent, UserProxyAgent\n", "from autogen.agentchat.contrib.capabilities.vision_capability import VisionCapability\n", "from autogen.agentchat.contrib.img_utils import get_pil_image, pil_to_data_uri\n", "from autogen.agentchat.contrib.multimodal_conversable_agent import MultimodalConversableAgent\n", "from autogen.code_utils import content_str\n", "\n", "image_agent = MultimodalConversableAgent(\n", "    name=\"image-explainer\",\n", "    max_consecutive_auto_reply=10,\n", "    llm_config=config_list_sonnet,\n", ")\n", "\n", "user_proxy = autogen.UserProxyAgent(\n", "    name=\"User_proxy\",\n", "    system_message=\"A human admin.\",\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=0,\n", "    code_execution_config={\n", "        \"use_docker\": False\n", "    },  # Please set use_docker=True if docker is available to run the generated code. Using docker is safer than running the generated code directly.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We start the chat and use the `img` tag in the message. The image will be downloaded and converted to bytes, then sent to the LLM."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mUser_proxy\u001b[0m (to image-explainer):\n", "\n", "What's happening in this image?\n", "<image>.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mimage-explainer\u001b[0m (to User_proxy):\n", "\n", "This image appears to be an advertisement or promotional material for a company called Autogen. The central figure is a stylized robot or android holding up a signboard with the company's name on it. The signboard also features a colorful heart design made up of many smaller hearts, suggesting themes related to love, care, or affection. The robot has a friendly, cartoonish expression with a large blue eye or lens. The overall style and color scheme give it a vibrant, eye-catching look that likely aims to portray Autogen as an innovative, approachable technology brand focused on connecting with people.\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["# Ask the image_agent to describe the image\n", "result = user_proxy.initiate_chat(\n", "    image_agent,\n", "    message=\"\"\"What's happening in this image?\n", "<img https://microsoft.github.io/autogen/assets/images/love-ec54b2666729d3e9d93f91773d1a77cf.png>.\"\"\",\n", ")"]}], "metadata": {"front_matter": {"description": "Define and load a custom model", "tags": ["custom model"]}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "vscode": {"interpreter": {"hash": "949777d72b0d2535278d3dc13498b2535136f6dfe0678499012e853ee9abcab1"}}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"2d910cfd2d2a4fc49fc30fbbdc5576a7": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "454146d0f7224f038689031002906e6f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e4ae2b6f5a974fd4bafb6abb9d12ff26", "IPY_MODEL_577e1e3cc4db4942b0883577b3b52755", "IPY_MODEL_b40bdfb1ac1d4cffb7cefcb870c64d45"], "layout": "IPY_MODEL_dc83c7bff2f241309537a8119dfc7555", "tabbable": null, "tooltip": null}}, "577e1e3cc4db4942b0883577b3b52755": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_2d910cfd2d2a4fc49fc30fbbdc5576a7", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_74a6ba0c3cbc4051be0a83e152fe1e62", "tabbable": null, "tooltip": null, "value": 1}}, "6086462a12d54bafa59d3c4566f06cb2": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "74a6ba0c3cbc4051be0a83e152fe1e62": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7d3f3d9e15894d05a4d188ff4f466554": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "b40bdfb1ac1d4cffb7cefcb870c64d45": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_f1355871cc6f4dd4b50d9df5af20e5c8", "placeholder": "​", "style": "IPY_MODEL_ca245376fd9f4354af6b2befe4af4466", "tabbable": null, "tooltip": null, "value": " 1/1 [00:00&lt;00:00, 44.69it/s]"}}, "ca245376fd9f4354af6b2befe4af4466": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "dc83c7bff2f241309537a8119dfc7555": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e4ae2b6f5a974fd4bafb6abb9d12ff26": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_6086462a12d54bafa59d3c4566f06cb2", "placeholder": "​", "style": "IPY_MODEL_7d3f3d9e15894d05a4d188ff4f466554", "tabbable": null, "tooltip": null, "value": "100%"}}, "f1355871cc6f4dd4b50d9df5af20e5c8": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 2}