{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LLM Configuration\n", "\n", "In AutoGen, agents use LLMs as key components to understand and react. To configure an agent's access to LLMs, you can specify an `llm_config` argument in its constructor. For example, the following snippet shows a configuration that uses `gpt-4`:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "llm_config = {\n", "    \"config_list\": [{\"model\": \"gpt-4\", \"api_key\": os.environ[\"OPENAI_API_KEY\"]}],\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["````{=mdx}\n", ":::warning\n", "It is important to never commit secrets into your code, therefore we read the OpenAI API key from an environment variable.\n", ":::\n", "````\n", "\n", "This `llm_config` can then be passed to an agent's constructor to enable it to use the LLM."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import autogen\n", "\n", "assistant = autogen.AssistantAgent(name=\"assistant\", llm_config=llm_config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Introduction to `config_list`\n", "\n", "Different tasks may require different models, and the `config_list` allows specifying the different endpoints and configurations that are to be used. It is a list of dictionaries, each of which contains the following keys depending on the kind of endpoint being used:\n", "\n", "````{=mdx}\n", "import Tabs from '@theme/Tabs';\n", "import TabItem from '@theme/TabItem';\n", "\n", "<Tabs>\n", "  <TabItem value=\"openai\" label=\"OpenAI\" default>\n", "    - `model` (str, required): The identifier of the model to be used, such as 'gpt-4', 'gpt-3.5-turbo'.\n", "    - `api_key` (str, optional): The API key required for authenticating requests to the model's API endpoint.\n", "    - `base_url` (str, optional): The base URL of the API endpoint. This is the root address where API calls are directed.\n", "    - `tags` (List[str], optional): Tags which can be used for filtering.\n", "\n", "    Example:\n", "    ```json\n", "    [\n", "      {\n", "        \"model\": \"gpt-4\",\n", "        \"api_key\": os.environ['OPENAI_API_KEY']\n", "      }\n", "    ]\n", "    ```\n", "  </TabItem>\n", "  <TabItem value=\"azureopenai\" label=\"Azure OpenAI\">\n", "    - `model` (str, required): The deployment to be used. The model corresponds to the deployment name on Azure OpenAI.\n", "    - `api_key` (str, optional): The API key required for authenticating requests to the model's API endpoint.\n", "    - `api_type`: `azure`\n", "    - `base_url` (str, optional): The base URL of the API endpoint. This is the root address where API calls are directed.\n", "    - `api_version` (str, optional): The version of the Azure API you wish to use.\n", "    - `tags` (List[str], optional): Tags which can be used for filtering.\n", "\n", "    Example:\n", "    ```json\n", "    [\n", "      {\n", "        \"model\": \"my-gpt-4-deployment\",\n", "        \"api_type\": \"azure\",\n", "        \"api_key\": os.environ['AZURE_OPENAI_API_KEY'],\n", "        \"base_url\": \"https://ENDPOINT.openai.azure.com/\",\n", "        \"api_version\": \"2024-02-01\"\n", "      }\n", "    ]\n", "    ```\n", "  </TabItem>\n", "  <TabItem value=\"other\" label=\"Other OpenAI compatible\">\n", "    - `model` (str, required): The identifier of the model to be used, such as 'llama-7B'.\n", "    - `api_key` (str, optional): The API key required for authenticating requests to the model's API endpoint.\n", "    - `base_url` (str, optional): The base URL of the API endpoint. This is the root address where API calls are directed.\n", "    - `tags` (List[str], optional): Tags which can be used for filtering.\n", "\n", "    Example:\n", "    ```json\n", "    [\n", "      {\n", "        \"model\": \"llama-7B\",\n", "        \"base_url\": \"http://localhost:1234\"\n", "      }\n", "    ]\n", "    ```\n", "  </TabItem>\n", "</Tabs>\n", "````\n", "\n", "---\n", "\n", "````{=mdx}\n", ":::tip\n", "By default this will create a model client which assumes an OpenAI API (or compatible) endpoint. To use custom model clients, see [here](https://github.com/ag2ai/ag2/blob/main/notebook/agentchat_custom_model.ipynb).\n", ":::\n", "````\n", "\n", "### `OAI_CONFIG_LIST` pattern\n", "\n", "A common, useful pattern used is to define this `config_list` via JSON (specified as a file or an environment variable set to a JSON-formatted string) and then use the [`config_list_from_json`](/docs/reference/oai/openai_utils#config_list_from_json) helper function to load it:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_list = autogen.config_list_from_json(\n", "    env_or_file=\"OAI_CONFIG_LIST\",\n", ")\n", "\n", "# Then, create the assistant agent with the config list\n", "assistant = autogen.AssistantAgent(name=\"assistant\", llm_config={\"config_list\": config_list})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This can be helpful as it keeps all the configuration in one place across different projects or notebooks.\n", "\n", "This function interprets the `env_or_file` argument as follows:\n", "\n", "- If `env_or_file` is an environment variable then:\n", "    - It will first try to load the file from the path specified in the environment variable.\n", "    - If there is no file, it will try to interpret the environment variable as a JSON string.\n", "- Otherwise, it will try to open the file at the path specified by `env_or_file`.\n", "\n", "### Why is it a list?\n", "\n", "Being a list allows you to define multiple models that can be used by the agent. This is useful for a few reasons:\n", "\n", "- If one model times out or fails, the agent can try another model.\n", "- Having a single global list of models and [filtering it](#config-list-filtering) based on certain keys (e.g. name, tag) in order to pass select models into a certain agent (e.g. use cheaper GPT 3.5 for agents solving easier tasks)\n", "- While the core agents, (e.g. conversable or assistant) do not have special logic around selecting configs, some of the specialized agents *may* have logic to select the best model based on the task at hand.\n", "\n", "### How does an agent decide which model to pick out of the list?\n", "\n", "An agent uses the very first model available in the \"config_list\" and makes LLM calls against this model. If the model fails (e.g. API throttling) the agent will retry the request against the 2nd model and so on until prompt completion is received (or throws an error if none of the models successfully completes the request). In general there's no implicit/hidden logic inside agents that is used to pick \"the best model for the task\". However, some specialized agents may attempt to choose \"the best model for the task\". It is developers responsibility to pick the right models and use them with agents.\n", "\n", "### Config list filtering\n", "\n", "As described above the list can be filtered based on certain criteria. This is defined as a dictionary of key to filter on and values to filter by. For example, if you have a list of configs and you want to select the one with the model \"gpt-3.5-turbo\" you can use the following filter:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filter_dict = {\"model\": [\"gpt-3.5-turbo\"]}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This can then be applied to a config list loaded in Python with [`filter_config`](/docs/reference/oai/openai_utils#filter_config):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_list = autogen.filter_config(config_list, filter_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Or, directly when loading the config list using [`config_list_from_json`](/docs/reference/oai/openai_utils#config_list_from_json):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_list = autogen.config_list_from_json(env_or_file=\"OAI_CONFIG_LIST\", filter_dict=filter_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Tags\n", "\n", "Model names can differ between OpenAI and Azure OpenAI, so tags offer an easy way to smooth over this inconsistency. Tags are a list of strings in the `config_list`, for example for the following `config_list`:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["config_list = [\n", "    {\"model\": \"my-gpt-4-deployment\", \"api_key\": \"\", \"tags\": [\"gpt4\", \"openai\"]},\n", "    {\"model\": \"llama-7B\", \"base_url\": \"http://127.0.0.1:8080\", \"tags\": [\"llama\", \"local\"]},\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then when filtering the `config_list` you can can specify the desired tags. A config is selected if it has at least one of the tags specified in the filter. For example, to just get the `llama` model, you can use the following filter:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["filter_dict = {\"tags\": [\"llama\", \"another_tag\"]}\n", "config_list = autogen.filter_config(config_list, filter_dict)\n", "assert len(config_list) == 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding http client in llm_config for proxy\n", "\n", "In Autogen, a deepcopy is used on llm_config to ensure that the llm_config passed by user is not modified internally. You may get an error if the llm_config contains objects of a class that do not support deepcopy. To fix this, you need to implement a `__deepcopy__` method for the class.\n", "\n", "The below example shows how to implement a `__deepcopy__` method for http client and add a  proxy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install httpx\n", "import httpx\n", "\n", "\n", "class MyHttpClient(httpx.Client):\n", "    def __deepcopy__(self, memo):\n", "        return self\n", "\n", "\n", "config_list = [\n", "    {\n", "        \"model\": \"my-gpt-4-deployment\",\n", "        \"api_key\": \"\",\n", "        \"http_client\": MyHttpClient(proxy=\"http://localhost:8030\"),\n", "    }\n", "]\n", "\n", "llm_config = {\n", "    \"config_list\": config_list,\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Using Azure Active Directory (AAD) Authentication\n", "\n", "Azure Active Directory (AAD) provides secure access to resources and applications. Follow the steps below to configure AAD authentication for Autogen.\n", "\n", "#### Prerequisites\n", "- An Azure subscription - [Create one for free](https://azure.microsoft.com/en-us/free/).\n", "- Access granted to the Azure OpenAI Service in the desired Azure subscription.\n", "- Appropriate permissions to register an application in AAD.\n", "- Custom subdomain names are required to enable features like Microsoft Entra ID for authentication.\n", "- Azure CLI - [Installation Guide](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli).\n", "\n", "For more detailed and up-to-date instructions, please refer to the official [Azure OpenAI documentation](https://learn.microsoft.com/en-us/azure/ai-services/openai/).\n", "\n", "#### Step 1: Register an Application in AAD\n", "1. Navigate to the [Azure portal](https://azure.microsoft.com/en-us/get-started/azure-portal).\n", "2. Go to `Azure Active Directory` > `App registrations`.\n", "3. <PERSON>lick on `New registration`.\n", "4. Enter a name for your application.\n", "5. Set the `Redirect URI` (optional).\n", "6. <PERSON><PERSON> `Register`.\n", "\n", "For detailed instructions, refer to the official [Azure AD Quickstart documentation](https://learn.microsoft.com/en-us/entra/identity-platform/quickstart-register-app?tabs=certificate).\n", "\n", "#### Step 2: Configure API Permissions\n", "1. After registration, go to `API permissions`.\n", "2. <PERSON><PERSON> `Add a permission`.\n", "3. Select `Microsoft Graph` and then `Delegated permissions`.\n", "4. Add the necessary permissions (e.g., `User.Read`).\n", "\n", "For more details, see [API permissions in Microsoft Graph](https://learn.microsoft.com/en-us/entra/identity-platform/permissions-consent-overview)\n", "\n", "#### Step 3: Obtain Client ID and Tenant ID\n", "1. Go to `Overview` of your registered application.\n", "2. Note down the `Application (client) ID` and `Directory (tenant) ID`.\n", "\n", "For more details, visit [Register an application with the Microsoft identity platform](https://learn.microsoft.com/en-us/entra/identity-platform/quickstart-register-app?tabs=certificate)\n", "\n", "#### Step 4: Configure Your Application\n", "Use the obtained `Client ID` and `Tenant ID` in your application configuration. Here’s an example of how to do this in your configuration file:\n", "```\n", "aad_config = {\n", "    \"client_id\": \"YOUR_CLIENT_ID\",\n", "    \"tenant_id\": \"YOUR_TENANT_ID\",\n", "    \"authority\": \"https://login.microsoftonline.com/YOUR_TENANT_ID\",\n", "    \"scope\": [\"https://graph.microsoft.com/.default\"],\n", "}\n", "```\n", "#### Step 5: <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>\n", "Use the following code to authenticate and acquire tokens:\n", "\n", "```\n", "from msal import ConfidentialClientApplication\n", "\n", "app = ConfidentialClientApplication(\n", "    client_id=aad_config[\"client_id\"],\n", "    client_credential=\"YOUR_CLIENT_SECRET\",\n", "    authority=aad_config[\"authority\"]\n", ")\n", "\n", "result = app.acquire_token_for_client(scopes=aad_config[\"scope\"])\n", "\n", "if \"access_token\" in result:\n", "    print(\"Token acquired\")\n", "else:\n", "    print(\"Error acquiring token:\", result.get(\"error\"))\n", "```\n", "\n", "For more details, refer to the [Authenticate and authorize in Azure OpenAI Service](https://learn.microsoft.com/en-us/azure/api-management/api-management-authenticate-authorize-azure-openai) and [How to configure Azure OpenAI Service with Microsoft Entra ID authentication](https://learn.microsoft.com/en-us/azure/ai-services/openai/how-to/managed-identity).\n", "\n", "\n", "#### Step 6: Configure Azure OpenAI with <PERSON><PERSON> Auth in AutoGen\n", "To use AAD authentication with Azure OpenAI in AutoGen, configure the `llm_config` with the necessary parameters.\n", "\n", "Here is an example configuration:\n", "\n", "```\n", "llm_config = {\n", "    \"config_list\": [\n", "        {\n", "            \"model\": \"gpt-4\",\n", "            \"base_url\": \"YOUR_BASE_URL\",\n", "            \"api_type\": \"azure\",\n", "            \"api_version\": \"2024-02-01\",\n", "            \"max_tokens\": 1000,\n", "            \"azure_ad_token_provider\": \"DEFAULT\"\n", "        }\n", "    ]\n", "}\n", "```\n", "\n", "For more details, refer to the [Authenticate and authorize in Azure OpenAI Service](https://learn.microsoft.com/en-us/azure/api-management/api-management-authenticate-authorize-azure-openai) and [How to configure Azure OpenAI Service with Microsoft Entra ID authentication](https://learn.microsoft.com/en-us/azure/ai-services/openai/how-to/managed-identity).\n", "\n", "In this configuration:\n", "- `model`: The Azure OpenAI deployment name.\n", "- `base_url`: The base URL of the Azure OpenAI endpoint.\n", "- `api_type`: Should be set to \"azure\".\n", "- `api_version`: The API version to use.\n", "- `azure_ad_token_provider`: Set to \"DEFAULT\" to use the default token provider.\n", "\n", "#### Example of Initializing an Assistant Agent with <PERSON><PERSON> Auth\n", "```\n", "import autogen\n", "\n", "# Initialize the assistant agent with the AAD authenticated config\n", "assistant = autogen.AssistantAgent(name=\"assistant\", llm_config=llm_config)\n", "```\n", "\n", "#### Troubleshooting\n", "If you encounter issues, check the following:\n", "- Ensure your `Client ID` and `Tenant ID` are correct.\n", "- Verify the permissions granted to your application.\n", "- Check network connectivity and Azure service status.\n", "\n", "This documentation provides a complete guide to configure and use AAD authentication with Azure OpenAI in the AutoGen.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Other configuration parameters\n", "\n", "Besides the `config_list`, there are other parameters that can be used to configure the LLM. These are split between parameters specifically used by Autogen and those passed into the model client.\n", "\n", "### AutoGen specific parameters\n", "\n", "- `cache_seed` - This is a legacy parameter and not recommended to be used unless the reason for using it is to disable the default caching behavior. To disable default caching, set this to `None`. Otherwise, by default or if an int is passed the [DiskCache](/docs/reference/cache/disk_cache) will be used. For the new way of using caching, pass a [Cache](/docs/reference/cache/) object into [`initiate_chat`](/docs/reference/agentchat/conversable_agent#initiate_chat).\n", "\n", "### Extra model client parameters\n", "\n", "It is also possible to passthrough parameters through to the OpenAI client. Parameters that correspond to the [`OpenAI` client](https://github.com/openai/openai-python/blob/d231d1fa783967c1d3a1db3ba1b52647fff148ac/src/openai/_client.py#L67) or the [`OpenAI` completions create API](https://github.com/openai/openai-python/blob/d231d1fa783967c1d3a1db3ba1b52647fff148ac/src/openai/resources/completions.py#L35) can be supplied.\n", "\n", "This is commonly used for things like `temperature`, or `timeout`.\n", "\n", "## Example\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm_config = {\n", "    \"config_list\": [\n", "        {\n", "            \"model\": \"my-gpt-4-deployment\",\n", "            \"api_key\": os.environ.get(\"AZURE_OPENAI_API_KEY\"),\n", "            \"api_type\": \"azure\",\n", "            \"base_url\": os.environ.get(\"AZURE_OPENAI_API_BASE\"),\n", "            \"api_version\": \"2024-02-01\",\n", "        },\n", "        {\n", "            \"model\": \"llama-7B\",\n", "            \"base_url\": \"http://127.0.0.1:8080\",\n", "            \"api_type\": \"openai\",\n", "        },\n", "    ],\n", "    \"temperature\": 0.9,\n", "    \"timeout\": 300,\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Other helpers for loading a config list\n", "\n", "- [`get_config_list`](/docs/reference/oai/openai_utils#get_config_list): Generates configurations for API calls, primarily from provided API keys.\n", "- [`config_list_openai_aoai`](/docs/reference/oai/openai_utils#config_list_openai_aoai): Constructs a list of configurations using both Azure OpenAI and OpenAI endpoints, sourcing API keys from environment variables or local files.\n", "- [`config_list_from_models`](/docs/reference/oai/openai_utils#config_list_from_models): Creates configurations based on a provided list of models, useful when targeting specific models without manually specifying each configuration.\n", "- [`config_list_from_dotenv`](/docs/reference/oai/openai_utils#config_list_from_dotenv): Constructs a configuration list from a `.env` file, offering a consolidated way to manage multiple API configurations and keys from a single file.\n", "\n", "See [this notebook](https://github.com/ag2ai/ag2/blob/main/notebook/config_loader_utility_functions.ipynb) for examples of using the above functions."]}], "metadata": {"kernelspec": {"display_name": "masterclass", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}