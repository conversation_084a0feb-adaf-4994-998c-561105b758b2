{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Code Executors\n", "\n", "In the last chapter, \n", "we used two agents powered by a large language model (LLM) to play a game\n", "by exchanging messages.\n", "In this chapter, we introduce code executors, which enable agents to not just chat\n", "but also\n", "to interact with an environment and perform useful computations and take actions.\n", "\n", "## Overview\n", "\n", "In AutoGen, a code executor is a component that\n", "takes input messages (e.g., those containing code blocks), performs execution, and outputs messages\n", "with the results.\n", "AutoGen provides two types of built-in code executors, one is \n", "command line code executor, which runs code in a command line environment\n", "such as a UNIX shell, and the other is Jupyter executor, which runs code\n", "in an interactive [Jupyter kernel](https://github.com/jupyter/jupyter/wiki/Jupy<PERSON>-kernels).\n", "\n", "For each type of executor, AutoGen provides two ways to execute code: locally and in a Docker container.\n", "One way is to execute\n", "code directly in the same host platform where AutoGen is running, i.e.,\n", "the local operating system.\n", "It is for development and testing, but it is not ideal for production\n", "as LLM can generate arbitrary code.\n", "The other way is to execute code in a Docker container.\n", "The table below shows the combinations of code executors and execution\n", "environments.\n", "\n", "| Code Executor (`autogen.coding`) | Environment | Platform |\n", "| ---------------------------- | ----------- | -------- |\n", "| [`LocalCommandLineCodeExecutor`](/docs/reference/coding/local_commandline_code_executor#localcommandlinecodeexecutor)  | Shell       | Local  |\n", "| [`DockerCommandLineCodeExecutor`](/docs/reference/coding/docker_commandline_code_executor#dockercommandlinecodeexecutor) | Shell       | Docker |\n", "| [`jupyter.JupyterCodeExecutor`](/docs/reference/coding/jupyter/jupyter_code_executor#jupytercodeexecutor)           | Jupyter Kernel (e.g., python3) | Local/Docker |\n", "\n", "In this chapter, we will focus on the command line code executors.\n", "For the Jupyter code executor, please refer to the topic page for \n", "[Jupyter Code Executor](/docs/topics/code-execution/jupyter-code-executor)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Local Execution\n", "\n", "The figure below shows the architecture of the local command line code executor\n", "([`autogen.coding.LocalCommandLineCodeExecutor`](/docs/reference/coding/local_commandline_code_executor#localcommandlinecodeexecutor)).\n", "\n", "````mdx-code-block\n", ":::danger\n", "Executing LLM-generated code poses a security risk to your host environment.\n", ":::\n", "````\n", "\n", "```{=mdx}\n", "![Code Executor No Docker](./assets/code-executor-no-docker.png)\n", "```\n", "\n", "Upon receiving a message with a code block, the local command line code executor\n", "first writes the code block to a code file, then starts a new subprocess to\n", "execute the code file. The executor reads the console output of the \n", "code execution and sends it back as a reply message.\n", "\n", "Here is an example of using the code executor to run a Python\n", "code block that prints a random number.\n", "First we create an agent with the code executor\n", "that uses a temporary directory to store the code files.\n", "We specify `human_input_mode=\"ALWAYS\"` to manually validate the safety of the the code being \n", "executed."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import tempfile\n", "\n", "from autogen import ConversableAgent\n", "from autogen.coding import LocalCommandLineCodeExecutor\n", "\n", "# Create a temporary directory to store the code files.\n", "temp_dir = tempfile.TemporaryDirectory()\n", "\n", "# Create a local command line code executor.\n", "executor = LocalCommandLineCodeExecutor(\n", "    timeout=10,  # Timeout for each code execution in seconds.\n", "    work_dir=temp_dir.name,  # Use the temporary directory to store the code files.\n", ")\n", "\n", "# Create an agent with code executor configuration.\n", "code_executor_agent = ConversableAgent(\n", "    \"code_executor_agent\",\n", "    llm_config=False,  # Turn off LLM for this agent.\n", "    code_execution_config={\"executor\": executor},  # Use the local command line code executor.\n", "    human_input_mode=\"ALWAYS\",  # Always take human input for this agent for safety.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before running this example, we need to make sure the `matplotlib` and `numpy`\n", "are installed."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["! pip install -qqq matplotlib numpy"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we have the agent generate a reply given a message with a Python code block."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31m\n", ">>>>>>>> NO HUMAN INPUT RECEIVED.\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK (inferred language is python)...\u001b[0m\n", "exitcode: 0 (execution succeeded)\n", "Code output: \n", "<PERSON><PERSON><PERSON> plot saved to scatter.png\n", "\n"]}], "source": ["message_with_code_block = \"\"\"This is a message with code block.\n", "The code block is below:\n", "```python\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "x = np.random.randint(0, 100, 100)\n", "y = np.random.randint(0, 100, 100)\n", "plt.scatter(x, y)\n", "plt.savefig('scatter.png')\n", "print('<PERSON><PERSON><PERSON> plot saved to scatter.png')\n", "```\n", "This is the end of the message.\n", "\"\"\"\n", "\n", "# Generate a reply for the given code.\n", "reply = code_executor_agent.generate_reply(messages=[{\"role\": \"user\", \"content\": message_with_code_block}])\n", "print(reply)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["During the generation of response, a human input is requested to give an opportunity\n", "to intercept the code execution.\n", "In this case, we choose to continue the execution, \n", "and the agent's reply contains the output of the code execution.\n", "\n", "We can take a look at the generated plot in the temporary directory."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['scatter.png', '6507ea07b63b45aabb027ade4e213de6.py']\n"]}], "source": ["import os\n", "\n", "print(os.listdir(temp_dir.name))\n", "# We can see the output scatter.png and the code file generated by the agent."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Clean up the working directory to avoid affecting future conversations."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["temp_dir.cleanup()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Docker Execution\n", "\n", "To mitigate the security risk of running LLM-generated code locally, \n", "we can use the docker command line code executor \n", "([`autogen.coding.DockerCommandLineCodeExecutor`](/docs/reference/coding/docker_commandline_code_executor#dockercommandlinecodeexecutor))\n", "to execute code in a docker container.\n", "This way, the generated code can only access resources that are explicitly \n", "given to it.\n", "\n", "The figure below illustrates how docker execution works.\n", "\n", "```{=mdx}\n", "![Code Executor Docker](./assets/code-executor-docker.png)\n", "```\n", "\n", "Similar to the local command line code executor, the docker executor \n", "extracts code blocks from input messages, writes them to code files.\n", "For each code file, it starts a docker container to execute the code file,\n", "and reads the console output of the code execution."]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use docker execution, you need to [install Docker](https://docs.docker.com/engine/install/) on your machine.\n", "Once you have <PERSON><PERSON> installed and running, you can set up your code executor agent as follow:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from autogen.coding import DockerCommandLineCodeExecutor\n", "\n", "# Create a temporary directory to store the code files.\n", "temp_dir = tempfile.TemporaryDirectory()\n", "\n", "# Create a Docker command line code executor.\n", "executor = DockerCommandLineCodeExecutor(\n", "    image=\"python:3.12-slim\",  # Execute code using the given docker image name.\n", "    timeout=10,  # Timeout for each code execution in seconds.\n", "    work_dir=temp_dir.name,  # Use the temporary directory to store the code files.\n", ")\n", "\n", "# Create an agent with code executor configuration that uses docker.\n", "code_executor_agent_using_docker = ConversableAgent(\n", "    \"code_executor_agent_docker\",\n", "    llm_config=False,  # Turn off LLM for this agent.\n", "    code_execution_config={\"executor\": executor},  # Use the docker command line code executor.\n", "    human_input_mode=\"ALWAYS\",  # Always take human input for this agent for safety.\n", ")\n", "\n", "# When the code executor is no longer used, stop it to release the resources.\n", "# executor.stop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `work_dir` in the constructor points to a local file system directory just like in the local execution case.\n", "The docker container will mount this directory and the executor write code files\n", "and output to it."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use Code Execution in Conversation\n", "\n", "Writing and executing code is necessary for many tasks such as \n", "data analysis, machine learning, and mathematical modeling.\n", "In AutoGen, coding can be a conversation between a code writer agent and a \n", "code executor agent, mirroring the interaction between a programmer and a\n", "code interpreter.\n", "\n", "```{=mdx}\n", "![Code Writer and Code Executor](./assets/code-execution-in-conversation.png)\n", "```\n", "\n", "The code writer agent can be powered by an LLM such as GPT-4 with code-writing\n", "capability.\n", "And the code executor agent is powered by a code executor.\n", "\n", "The following is an agent with a code writer role specified \n", "using `system_message`. The system message contains important instruction\n", "on how to use the code executor in the code executor agent."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# The code writer agent's system message is to instruct the LLM on how to use\n", "# the code executor in the code executor agent.\n", "code_writer_system_message = \"\"\"You are a helpful AI assistant.\n", "Solve tasks using your coding and language skills.\n", "In the following cases, suggest python code (in a python coding block) or shell script (in a sh coding block) for the user to execute.\n", "1. When you need to collect info, use the code to output the info you need, for example, browse or search the web, download/read a file, print the content of a webpage or a file, get the current date/time, check the operating system. After sufficient info is printed and the task is ready to be solved based on your language skill, you can solve the task by yourself.\n", "2. When you need to perform some task with code, use the code to perform the task and output the result. Finish the task smartly.\n", "Solve the task step by step if you need to. If a plan is not provided, explain your plan first. Be clear which step uses code, and which step uses your language skill.\n", "When using code, you must indicate the script type in the code block. The user cannot provide any other feedback or perform any other action beyond executing the code you suggest. The user can't modify your code. So do not suggest incomplete code which requires users to modify. Don't use a code block if it's not intended to be executed by the user.\n", "If you want the user to save the code in a file before executing it, put # filename: <filename> inside the code block as the first line. Don't include multiple code blocks in one response. Do not ask users to copy and paste the result. Instead, use 'print' function for the output when relevant. Check the execution result returned by the user.\n", "If the result indicates there is an error, fix the error and output the code again. Suggest the full code instead of partial code or code changes. If the error can't be fixed or if the task is not solved even after the code is executed successfully, analyze the problem, revisit your assumption, collect additional info you need, and think of a different approach to try.\n", "When you find an answer, verify the answer carefully. Include verifiable evidence in your response if possible.\n", "Reply 'TERMINATE' in the end when everything is done.\n", "\"\"\"\n", "\n", "code_writer_agent = ConversableAgent(\n", "    \"code_writer_agent\",\n", "    system_message=code_writer_system_message,\n", "    llm_config={\"config_list\": [{\"model\": \"gpt-4\", \"api_key\": os.environ[\"OPENAI_API_KEY\"]}]},\n", "    code_execution_config=False,  # Turn off code execution for this agent.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is an example of solving a math problem through a conversation\n", "between the code writer agent and the code executor agent (created above)."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mcode_executor_agent\u001b[0m (to code_writer_agent):\n", "\n", "Write Python code to calculate the 14th <PERSON><PERSON><PERSON><PERSON> number.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mcode_writer_agent\u001b[0m (to code_executor_agent):\n", "\n", "Sure, here is a Python code snippet to calculate the 14th <PERSON><PERSON><PERSON><PERSON> number. The <PERSON>bonacci series is a sequence of numbers in which each number is the sum of the two preceding ones, usually starting with 0 and 1.\n", "\n", "```python\n", "def <PERSON><PERSON><PERSON><PERSON>(n):\n", "    if(n <= 0):\n", "        return \"Input should be a positive integer.\"\n", "    elif(n == 1):\n", "        return 0\n", "    elif(n == 2):\n", "        return 1\n", "    else:\n", "        fib = [0, 1]\n", "        for i in range(2, n):\n", "            fib.append(fib[i-1] + fib[i-2])\n", "        return fib[n-1]\n", "\n", "print(<PERSON><PERSON><PERSON><PERSON>(14))\n", "```\n", "\n", "This Python code defines a function `fibonacci(n)` which computes the n-th Fibonacci number. The function uses a list `fib` to store the Fibonacci numbers as they are computed, and then returns the (n-1)-th element as the n-th Fibonacci number due to zero-indexing in Python lists.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> NO HUMAN INPUT RECEIVED.\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK (inferred language is python)...\u001b[0m\n", "\u001b[33mcode_executor_agent\u001b[0m (to code_writer_agent):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: \n", "233\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mcode_writer_agent\u001b[0m (to code_executor_agent):\n", "\n", "Great, the execution was successful and the 14th <PERSON><PERSON><PERSON><PERSON> number is 233. The sequence goes as follows: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233... and so on, where each number is the sum of the previous two. Therefore, the 14th number in the <PERSON><PERSON><PERSON><PERSON> series is 233. \n", "\n", "I hope this meets your expectations. If you have any other concerns or need further computations, feel free to ask.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["chat_result = code_executor_agent.initiate_chat(\n", "    code_writer_agent,\n", "    message=\"Write Python code to calculate the 14th <PERSON><PERSON><PERSON><PERSON> number.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["During the previous chat session, human input was requested each time\n", "the code executor agent responded to ensure that the code was safe to execute."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can try a more complex example that involves querying the web.\n", "Let's say we want to get the the stock price gains year-to-date for\n", "<PERSON>sla and Meta (formerly Facebook). We can also use the two agents\n", "with several iterations of conversation.\n"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mcode_executor_agent\u001b[0m (to code_writer_agent):\n", "\n", "Today is 2024-02-28. Write Python code to plot TSLA's and META's stock price gains YTD, and save the plot to a file named 'stock_gains.png'.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mcode_writer_agent\u001b[0m (to code_executor_agent):\n", "\n", "This task requires retrieving the historical data of the stocks from a reliable data source and calculating the Year-To-Date (YTD) gain values, and then plotting them. pandas_datareader library will be used for data retrieval, pandas will be used for data manipulation, and matplotlib for plotting. \n", "\n", "Below is the Python code to achieve this. To start, please install the required libraries by running to the following command:\n", "```sh\n", "pip install yfinance pandas matplotlib\n", "```\n", "Then run the python code:\n", "\n", "```python\n", "# filename: stock_gains.py\n", "\n", "import yfinance as yf\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "\n", "# define the tickers\n", "tickers = ['TSLA', 'META'] \n", "\n", "# define the start and end dates\n", "start_date = datetime(2024, 1, 1)\n", "end_date = datetime(2024, 2, 28)\n", "\n", "# dictionary to hold dataframes\n", "dfs = {}\n", "\n", "for ticker in tickers:\n", "    # get the data for the stocks\n", "    df = yf.download(ticker, start_date, end_date)\n", "\n", "    # get the close price and calculate the cumulative percentage gain\n", "    df['Gain'] = df['Close'].pct_change().cumsum()\n", "\n", "    # add to dictionary\n", "    dfs[ticker] = df    \n", "\n", "# plot\n", "plt.figure(figsize=(10, 5))\n", "for ticker, df in dfs.items():\n", "    plt.plot(df.index, df['Gain'], label=ticker)\n", "\n", "plt.title('YTD Stock Price Gain')\n", "plt.xlabel('Date')\n", "plt.ylabel('Percentage Gain')\n", "plt.legend()\n", "\n", "plt.grid(True)\n", "plt.savefig('stock_gains.png')\n", "plt.close()\n", "\n", "print(\"The 'stock_gains.png' file has been successfully saved\")\n", "```\n", "This script will download the historical data for TSLA and META from the start of the year to the specified date and calculates the YTD gains. It then generates the plot showing these gains and saves it to 'stock_gains.png'.\n", "\n", "Please save the script to a file named 'stock_gains.py' and run it using Python. Remember to have the correct start and end dates for the YTD value when running the script. If your Python version is below 3.8, you should update it to execute this code perfectly.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> NO HUMAN INPUT RECEIVED.\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK 0 (inferred language is sh)...\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK 1 (inferred language is python)...\u001b[0m\n", "\u001b[33mcode_executor_agent\u001b[0m (to code_writer_agent):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: \n", "Requirement already satisfied: yfinance in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (0.2.36)\n", "Requirement already satisfied: pandas in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (2.1.4)\n", "Requirement already satisfied: matplotlib in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (3.8.2)\n", "Requirement already satisfied: numpy>=1.16.5 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (1.26.2)\n", "Requirement already satisfied: requests>=2.31 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (2.31.0)\n", "Requirement already satisfied: multitasking>=0.0.7 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (0.0.11)\n", "Requirement already satisfied: lxml>=4.9.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (5.0.1)\n", "Requirement already satisfied: appdirs>=1.4.4 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (1.4.4)\n", "Requirement already satisfied: pytz>=2022.5 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (2023.3.post1)\n", "Requirement already satisfied: frozendict>=2.3.4 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (2.4.0)\n", "Requirement already satisfied: peewee>=3.16.2 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (3.17.0)\n", "Requirement already satisfied: beautifulsoup4>=4.11.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (4.12.2)\n", "Requirement already satisfied: html5lib>=1.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from yfinance) (1.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from pandas) (2.8.2)\n", "Requirement already satisfied: tzdata>=2022.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from pandas) (2023.4)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (1.2.0)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (4.47.2)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (1.4.5)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (23.2)\n", "Requirement already satisfied: pillow>=8 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (10.2.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from matplotlib) (3.1.1)\n", "Requirement already satisfied: soupsieve>1.2 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from beautifulsoup4>=4.11.1->yfinance) (2.5)\n", "Requirement already satisfied: six>=1.9 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from html5lib>=1.1->yfinance) (1.16.0)\n", "Requirement already satisfied: webencodings in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from html5lib>=1.1->yfinance) (0.5.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from requests>=2.31->yfinance) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from requests>=2.31->yfinance) (3.6)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from requests>=2.31->yfinance) (2.1.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/miniconda3/envs/autogen/lib/python3.11/site-packages (from requests>=2.31->yfinance) (2024.2.2)\n", "\n", "The 'stock_gains.png' file has been successfully saved\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mcode_writer_agent\u001b[0m (to code_executor_agent):\n", "\n", "Great! The code executed successfully and the 'stock_gains.png' file has been saved successfully. This file contains the plot of TSLA's and META's stock price gains from the start of the year until February 28, 2024. You should now be able to view this image file in the same directory that you ran the script from. \n", "\n", "Please make sure to verify this image file. It should contain two plotted lines, each representing the percentage gain over the time for each stock (TSLA and META). The x-axis represents the date, and the y-axis represents the percentage gain. If everything looks correct, this would be the end of the task.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["import datetime\n", "\n", "today = datetime.datetime.now().strftime(\"%Y-%m-%d\")\n", "chat_result = code_executor_agent.initiate_chat(\n", "    code_writer_agent,\n", "    message=f\"Today is {today}. Write Python code to plot TSLA's and META's \"\n", "    \"stock price gains YTD, and save the plot to a file named 'stock_gains.png'.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the previous conversation, the code writer agent generated a code block\n", "to install necessary packages and another code block for a script to \n", "fetch the stock price and calculate gains year-to-date for Tesla and Meta.\n", "The code executor agent installed the packages, executed the script, \n", "and returned the results.\n", "\n", "Let's take a look at the chart that was generated."]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA+gAAAH0CAYAAACuKActAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8g+/7EAAAACXBIWXMAAA9hAAAPYQGoP6dpAACgDUlEQVR4nOzdd3zT1f7H8VeSpnvTBaVQyip7T2XJFLcIiIOhol4v9/oTJ+p1K+pVxI0DBAciKHoVkSGCoiJ7710otKWU7pU2+f3xpYVaRoG2aZv38/HII8k332/yyUkLfeec7zkmh8PhQEREREREREScyuzsAkREREREREREAV1ERERERESkSlBAFxEREREREakCFNBFREREREREqgAFdBEREREREZEqQAFdREREREREpApQQBcRERERERGpAhTQRURERERERKoABXQRERERERGRKkABXURERERERKQKUEAXERERERERqQIU0EVERERERESqAAV0ERERERERkSpAAV1ERERERESkClBAFxEREREREakCFNBFREREREREqgAFdBEREREREZEqQAFdREREREREpApQQBcRERERERGpAhTQRURERERERKoABXQRERERERGRKkABXURERERERKQKUEAXERERERERqQIU0EVERERERESqAAV0ERERERERkSpAAV1ERERERESkClBAFxEREREREakCFNBFREREREREqgAFdBEREREREZEqQAFdREREREREpApQQBcRERERERGpAhTQRURERERERKoABXQRERERERGRKkABXURERERERKQKUEAXERERERERqQIU0EVERERERESqAAV0ERERERERkSpAAV1ERKQG6d27Ny1btnTa60dHRzN69GinvX55Gz16NNHR0c4uQ0REXIQCuoiIVHlXXnklQUFBJCYmlnosLS2N2rVrEx4ejslkOu9l+vTpACW2ubm5ERwcTIcOHbj//vvZtm1bmWvLz8/nzTffpF27dvj7+xMYGEiLFi24++672bFjR/F+f/75J8888wypqamX2hwVZvTo0SXaxd/fnzZt2vD666+Tl5fn7PLOa/ny5QwbNozIyEjc3d0JCAigS5cuPPfcc2f82REREalq3JxdgIiIyPm89957tGzZkgceeICZM2eWeOzxxx8nOTmZJ598koYNGxZvnz9/Pl9++SVvvPEGISEhxdu7d+9efLt///6MHDkSh8NBWloaGzduZMaMGbz33nu88sorjB8//ry1DRkyhJ9++okRI0YwduxYbDYbO3bsYN68eXTv3p3Y2FjACOjPPvsso0ePJjAw8BJbpOJ4eHjw8ccfA5Camso333zDQw89xOrVq5k1a9Z5j9+5cydmc+V////UU0/x/PPPExMTw+jRo4mJiSE3N5e1a9fy+uuvM2PGDPbu3XvBz/vRRx9ht9sroGIREZEzcIiIiFQDr7zyigNwLFy4sHjbqlWrHGaz2fHII4+U2v+///2vA3Ds37//jM8HOP75z3+W2p6cnOzo1q2bA3D8+OOP56xp1apVDsDx4osvlnqsoKDAkZycXOZ6ykuvXr0cLVq0uKhjR40a5fDx8SmxrbCw0NGxY0cH4IiPjz/jcXa73ZGdnX1Rr1keZs2a5QAcw4YNc+Tl5ZV6PDU11fH0009XfmEiIiIXSEPcRUSkWhg/fjytW7fmvvvuIzc3l8LCQu69917q16/P008/XW6vU6tWLWbNmoWbmxsvvvjiOfct6pG97LLLSj1msVioVasWAM888wwPP/wwAA0aNCgeQn7gwAEACgoKeP7552nYsCEeHh5ER0fz+OOPn3FY+U8//USvXr3w8/PD39+fTp06lRpV8HeLFi3C29ubESNGUFBQcN42OJ3ZbKZ3794AxfVGR0dz9dVXs3DhQjp27IiXlxcffPBB8WN/Pwc9NTWVBx54gOjoaDw8PKhbty4jR44kOTm5eJ+8vDyefvppGjVqhIeHB1FRUTzyyCNlGlr/1FNPERISwtSpU3F3dy/1eEBAAM8880yJbf/73/+46qqrqFOnDh4eHjRs2JDnn3+ewsLCEvv9/Rz0AwcOYDKZeO211/jwww+LP7NOnTqxevXq89YqIiJyLhriLiIi1YKbmxsffvgh3bt35/nnnycsLIx169axYMECvL29y/W16tWrR69evVi6dCnp6en4+/ufcb/69esD8MUXX3DZZZfh5nbm/1ZvvPFGdu3aVWrIfWhoKAB33XUXM2bM4KabbuLBBx9k5cqVTJw4ke3bt/Ptt98WP8/06dO54447aNGiBRMmTCAwMJD169ezYMECbrnlljO+9rx587jpppsYPnw406ZNw2KxXHB7FH0RUfSFAxhD2UeMGME999zD2LFjadq06RmPzczMpEePHmzfvp077riD9u3bk5yczPfff8/hw4cJCQnBbrdz7bXX8vvvv3P33XfTrFkzNm/ezBtvvMGuXbv47rvvzlrbrl272LVrF3fddRe+vr5lfk/Tp0/H19eX8ePH4+vryy+//MJTTz1Feno6//3vf897/MyZM8nIyOCee+7BZDLx6quvcuONN7Jv3z6sVmuZ6xARESnB2V34IiIiF2LcuHEOq9Xq8PX1dYwYMeKs+13sEPci999/vwNwbNy48az72O12R69evRyAIzw83DFixAjHu+++6zh48GCZ69mwYYMDcNx1110ltj/00EMOwPHLL784HA5jmLafn5+jS5cujpycnFJ1FDl9iPs333zjsFqtjrFjxzoKCwvP+j6KFA1xP3bsmOPYsWOOPXv2OF566SWHyWRytG7duni/+vXrOwDHggULSj1H/fr1HaNGjSq+/9RTTzkAx9y5c0vtW1T3Z5995jCbzY7ly5eXeHzKlCkOwPHHH3+cteb//e9/DsAxefLkUs9d9D6KLjabrfjxMw3Jv+eeexze3t6O3NzcEm1Sv3794vv79+93AI5atWo5UlJSStXxww8/nLVWERGR89EQdxERqVZefPFFatWqhdls5o033qiw1ynqjc3IyDjrPiaTiYULF/LCCy8QFBTEl19+yT//+U/q16/P8OHDyzRj+/z58wFKTUj34IMPAvDjjz8CsHjxYjIyMnjsscfw9PQsVcffffnllwwfPpx77rmHDz74oMwTt2VlZREaGkpoaCiNGjXi8ccfp1u3biV68sEYqj9w4MDzPt8333xDmzZtuOGGG0o9VlT3nDlzaNasGbGxsSQnJxdfrrjiCgCWLl161udPT08HKNV7npaWVvw+ii4bNmwoftzLy6v4dkZGBsnJyfTo0YPs7OwSs++fzfDhwwkKCiq+36NHDwD27dt33mNFRETORkPcRUSkWvH396dp06YkJycTHh5eYa+TmZkJgJ+f3zn38/Dw4IknnuCJJ57g6NGj/Prrr7z55pvMnj0bq9XK559/fs7jDx48iNlsplGjRiW2R0REEBgYyMGDB4FTw8zLssb5/v37ue222xg6dChvv/32efc/naenJz/88EPxe2vQoAF169YttV+DBg3K9Hx79+5lyJAh59xn9+7dbN++vXjI/98lJSWd9diiz6fo8yri6+vL4sWLAeMc/L8PW9+6dStPPvkkv/zyS3HIL5KWlnbOesE4DeJ0RWH9xIkT5z1WRETkbBTQRUREzmDLli1YLJYyB1GA2rVrc/PNNzNkyBBatGjB7NmzmT59+lnPTT/dmXrBL1bt2rWpXbs28+fPZ82aNXTs2LHMx1osFvr163fe/U7vgb5UdrudVq1aMWnSpDM+HhUVddZji5ax27JlS4ntbm5uxe/j8OHDJR5LTU2lV69e+Pv789xzz9GwYUM8PT1Zt24djz76aJmWVTvbufwOh+O8x4qIiJyNArqIiMjfxMXF8euvv9KtW7fz9qCfidVqpXXr1uzevZvk5GQiIiLOGsDr16+P3W5n9+7dNGvWrHh7YmIiqampxRPRFa3xvmXLllK97X/n6enJvHnzuOKKKxg0aBC//vorLVq0uOD3UR4aNmxYKjyfaZ+NGzfSt2/fC/6iomnTpjRu3JjvvvuOyZMn4+Pjc95jli1bxvHjx5k7dy49e/Ys3r5///4Lem0REZHypnPQRURETpOSksKIESMoLCzkiSeeOOe+u3fvJi4urtT21NRUVqxYQVBQUPGw7aLg+Pfz0gcPHgzA5MmTS2wv6k2+6qqrABgwYAB+fn5MnDiR3NzcEvueqdc2ICCAhQsXEhYWRv/+/YuHyFe2IUOGsHHjxlLnsMOpuocNG0Z8fDwfffRRqX1ycnLIyso652s888wzJCcnM3bsWGw221lfp0hR7/fp2/Pz83nvvffO/4ZEREQqkHrQRUTEZe3atYvPP/8ch8NBeno6GzduZM6cOWRmZjJp0iQGDRp0zuM3btzILbfcwpVXXkmPHj0IDg4mPj6eGTNmcOTIESZPnlwcBjt06ADAE088wc0334zVauWaa66hTZs2jBo1ig8//LB46PWqVauYMWMG119/PX369AGMc+/feOMN7rrrLjp16sQtt9xCUFAQGzduJDs7mxkzZpSqLyQkhMWLF3P55ZfTr18/fv/9dyIjI8u5Fc/t4Ycf5uuvv2bo0KHccccddOjQgZSUFL7//numTJlCmzZtuP3225k9ezb33nsvS5cu5bLLLqOwsJAdO3Ywe/bs4vXWz+aWW25hy5YtTJw4kVWrVnHzzTfToEEDsrKy2LJlC19++SV+fn7F54l3796doKAgRo0axb///W9MJhOfffaZhqeLiIjTKaCLiIjLWrx4MYsXL8ZsNuPv70+DBg0YNWoUd999N82bNz/v8T179uT555/np59+YtKkSRw7dgw/Pz/atWvHK6+8UmJytE6dOvH8888zZcoUFixYgN1uZ//+/fj4+PDxxx8TExPD9OnT+fbbb4mIiGDChAk8/fTTJV7vzjvvJCwsjJdffpnnn38eq9VKbGwsDzzwwFlrjIyM5Oeff6ZHjx7079+f3377rXgd9srg6+vL8uXLefrpp/n222+ZMWMGYWFh9O3bt3jyObPZzHfffccbb7zBp59+yrfffou3tzcxMTHcf//9NGnS5Lyv89JLLzFw4EDeeecdpk2bRnJyMl5eXjRp0oQHH3yQe++9l4iICMBYz33evHk8+OCDPPnkkwQFBXHbbbfRt2/fMs1MLyIiUlFMDn1dLCIiIiIiIuJ0OgddREREREREpApQQBcRERERERGpAhTQRURERERERKoABXQRERERERGRKkABXURERERERKQKUEAXERERERERqQK0DrqLsdvtHDlyBD8/P0wmk7PLERERERERJ3E4HGRkZFCnTh3MZvXdVgUK6C7myJEjREVFObsMERERERGpIg4dOkTdunWdXYaggO5y/Pz8AOOX0N/f/6z72Ww2Fi1axIABA7BarZVVnktRG1cOtXPlUDu7Nn3+FU9tXDnUzpVD7Vx1pKenExUVVZwRxPkU0F1M0bB2f3//8wZ0b29v/P399Q9nBVEbVw61c+VQO7s2ff4VT21cOdTOlUPtXPXo1NeqQycaiIiIiIiIiFQBCugiIiIiIiIiVYACuoiIiIiIiEgVoHPQ5YwKCwtxc3MjNzeXwsJCZ5dT7VmtViwWi7PLEBERERGRKkwBXUpwOBwkJCRw4sQJIiIiOHTokCaNKCeBgYFERESoPUVERERE5IwU0KWEhIQEUlNTCQ0NxW634+fnh9msMyEuhcPhIDs7m6SkJABq167t5IpERERERKQqUkCXYoWFhaSmphIWFkZQUBDp6el4enoqoJcDLy8vAJKSkggLC9NwdxERERERKUXJS4rZbDYAvL29nVxJzVTUrkXtLCIiIiIicjoFdClF50hXDLWriIiIiIiciwK6iIiIiIiISBWggC4iIiIiIiJSBSigS7VmMpnOeXnmmWcA+Pbbb+natSsBAQH4+fnRokUL/u///q/4eaZPn05gYGCZXjM2NhYPDw8SEhLK/w2JiIiIiIjLUkCXau3o0aPFl8mTJ+Pv719i20MPPcSSJUsYPnw4Q4YMYdWqVaxdu5YXX3zxoiZr+/3338nJyeGmm25ixowZFfCORERERETEVWmZNanWIiIiim8HBARgMplKbAP44YcfuOyyy3j44YeLtzVp0oTrr7/+gl9v6tSp3HLLLfTq1Yv777+fRx999KJrFxEREREXk34UVn0A/pHQeayzq5EqSAFdzsnhcJCdX1Dpr+tltZTbrOcRERHMnDmTLVu20LJly4t+noyMDObMmcPKlSuJjY0lLS2N5cuX06NHj3KpU0RERKTGKyzAvHYa9ZJ3YIoPh9otwcPX2VVVvMSt8Oc7sHkO2G3gVxvajwI3d2dXJlWMArqcU46tkJbPLK7019323EC83cvnx/Nf//oXy5cvp1WrVtSvX5+uXbsyYMAAbr31Vjw8PMr8PLNmzaJx48a0aNECgJtvvpmpU6cqoIuIiIiU1e+TsCx9kXYA06cBJgiKhvAWENYcwptDWAsIjgFLNY8qDgfs/QVWvGNcF4nqCt3HgdnivNqkyqrmP/Ui5+fj48OPP/7I3r17Wbp0KX/99RcPPvggb775JitWrMDb27tMzzNt2jRuu+224vu33XYbvXr14u2338bPz6+iyhcRERGpGZJ3w2//BSDFpxFBpGPKSoIT+43Ljnmn9rV4QFisEdbDm0NYM/AJBc9A8AwAD38wV9HptAryYcvXRo950lZjm8kMza6Bbv+CqE7OrU+qNAV0OScvq4Vtzw10yuuWt4YNG9KwYUPuuusunnjiCZo0acJXX33FmDFjznvstm3b+Ouvv1i1alWJ884LCwuZNWsWY8fqHCIREZFqzW7H/OebNDuyAQr6gtXq7IpqFrsdfrgfCvOxN+zHcr/bGXzVVVjz04zh30nbTl0nbQdbNhzdaFzOyASe/qcCu9fJ6xL3A8/+uNWz/N9jzglY8wms/AAyT672Y/WB9rdDl3shuEH5v6bUOArock4mkwlv95o3/CY6Ohpvb2+ysrLKtP/UqVPp2bMn7777bontn3zyCVOnTlVAFxERqc4KbfDtvVi2fE0TwP5VKtw80wiAUj42fA4H/wCrN4WDXoU/txjbfUIgppdxKWK3Gz3qSdsgcZvRC31sF+SkQG4aFOQCDuN2btrF1ePmeYZAX8b7f++9P3EA/nof1n0GtpN/W/pGQJd7oOMY8Aq6uBrFJSmgS433zDPPkJ2dzeDBg6lfvz6pqam89dZb2Gw2+vfvX7xfYWEhGzZsKHGsh4cHjRo14rPPPuO5554rNcncXXfdxaRJk9i6dWvxuekiIiJSjeRnw5xRsHsRDrMbhVhwO7Acpg+GW78Bv3BnV1j9ZSbBoieN232egMB6wJaz7282Q62GxqXZNaUft+WeDOepxnVO6mn3U/92/++PpwMOI+Rn5kJm4kW8oaLe+wBw94Nj28FhNx4KawHd/wUth2gCOLkoCuhS4/Xq1Yt3332XkSNHkpiYSFBQEO3atWPRokU0bdq0eL/MzEzatWtX4tiGDRvyyiuvcPz4cW644YZSz92sWTOaNWvG1KlTmTRpUoW/FxERESlHOanw5c0QtwLcvCgc8gm/b9hFr8NvY0rYDFP7w+3fGkFRLt6Cx4yAXLuNMdTb7ri057N6GpeL+fLEboe89HME+PPcP1vvfcMrjGAe0wfKaSUicU0K6FJjjB49mtGjR5fa3qdPH/r06XNRxxYpLCw862Pbtm0ra4kiIiJSVWQmwec3QsJm8AiAW77CUacjabvyKRg1H+uXQ42hy1MHwG1fQ512531KOYNdi2DLN8Ykade8ZczMbrc5rx6z2Riu7hUI1L/w44t779NO9dYHRUNok/KsUlxYFZ36UERERESkgqTGwbSBRjj3CYUxP0L9bqceD2oAdy6GiNaQnQzTry65TJaUTV4m/PigcbvrfVCnrVPLKRdFPfehTSCqMzQZoHAu5UoBXURERERcR9IOmDoQUvZBQD24YyFEtCq9n28YjP4RGvSC/Ez4Yhhs/rry663Olk2EtDijnfs87uxqRKoFBXQRERERcQ3xa+GTKyHjCITGwp0Lz31+uac/3DoHWtxgDMv+5k5jtm45vyPr4a/3jNtXTwJ3H+fWI1JNKKCLiIiISM23/zeYca2xVFed9jDmJ/Cvc/7j3DxgyDTofI9xf8Fj8PMz4LjEic5qssIC+P7fxszmLW+Cxv3Pf4yIAAroIiIiIlLTbZ8Hn99kDFVv0BNGfQ/ewWU/3myGK1+Bvk8Z939/A/73T2P9dClt5fuQsMlYM3zQRGdXI1KtKKCLiIiISM21/guYfTsU5kHs1XDLHPDwu/DnMZmgx4Nw7TtgssCGL2DWrcY66nLKiYOw9CXj9oAXjHP5RaTMFNBFRESkciVsNoYaL38dslOcXY3UZCveg//dZwy1bnsrDJ1hzMJ9KdrfDjd/AW6esHshfHqtMeFcdooR1u328qm9OnI44MfxYMuG6B7Q7jZnVyRS7WgddBEREalcG76E/b8al99eM4JT13+ce7IukQv1639h6QvG7W7joP/zxlD18tD0Shj5PcwcBodXw1t/WyPd4mEEeKuncV1828s4p93qde7t1pP33bz+9hzn2G52M3r5nWnLN7DnZ+P9Xz3Z+fWIVEMK6CIiIlK5Ug8a154BkJsGqz+C1R9D7FXQ7Z9Qr5v+sJdLs2HmqXB+xZPQ46Hy/5mq18VYou2bO+HYDrAXnHqsMM+45KWV72uei8l8AYH+HPv4R0JMb2P7hchOMSbQA+j5MIQ0Kve3KOIKFNClRhg9ejQzZszgnnvuYcqUKSUe++c//8l7773HqFGjmD59evG+fzdw4EAee+wx+vTpc87XWrp0Kb179+bw4cPExMTQpEkTtmzZUq7vR0SkRks7ZFzf8AFYvWHFu8ZQ4R3zjEud9kZQb349WPSnilygA78bM4gDXD7eCIsVJSwW/vGHcbuwAApyT11suVCQc/K6aFsOFORd4Pa/PV9BXsntRRx2sGUZl0tl9TZmXm92LTQeYCw3dz6Ln4KsY8bydZfdf+k1iLgo/a8nNUZUVBSzZs3ijTfewMvL+NY3NzeXmTNnUq9evRL7Dho0iE8++aTENg8PD3x8fDh69Gjxtvvvv5/09PQS+wYHG7O+Tp8+nWHDhvHbb7+xcuVKunTpUlFvTUSkZkk7bFwHREFES4jpBcd2Gmsmb5wFR9YZvZI/PwNd7oH2I43edpHzSd5jTNxmtxlf8Fzxn8p7bYsbWHzBw7fyXtPhOBXsTw/uF/VFQJ5x7vjRTZAWB9v+Z1ws7tDwCmh2DTQdfObZ7/cvh/WfGbeveQvc3CuvDURqGAV0qTHat2/P3r17mTt3LrfeeisAc+fOpV69ejRo0KDEvh4eHkRERJzxeU7f7uXlRV5eXql9HQ4Hn3zyCe+99x5169Zl6tSpCugiImWRnwXZx43bAXVPbQ9tCte8aQSq1VONYe9ph2DRk7DsFSOkd70XAuud+Xmrk0IbbP8e0o/8rZf15LUtu2TvqS0HN1sOV2RmYsn4HMKbGb2UobEQ0qRyA2FVlnUcZg6F3FSI7Ag3TCm/c86rKpPJGJZ+qRPfnc7hgKMbYNv3xs/p8T2wa4FxMVkg+nJofq0xI75fhPEzO+//jGM73mkM/ReRi6aALjXKHXfcwSeffFIc0KdNm8aYMWNYtmxZub7O0qVLyc7Opl+/fkRGRtK9e3feeOMNfHx8yvV1RERqnKLecw9/8Aos/bhPCPR+1Bgiu3m2Mfz92A74611jbeXm1xkTftXtWKlll6vv/wUbv7ygQ0yAH8Ceo7BnUckHA+oZX3CENj0V3EObXNioA4fD+OKgMN+42AtO3T59e6nbtnPsc/Labiv9OCZoewtEX3ZB7XBWBXnw1a3GbOqB9WDElxd+DrUYTCao08649H3K+P3b9j1s/wESN5+a4PHHhyCqC3jXMkK8bwT0e9rZ1YtUewrocm4Oh9HbUdms3hc1mcttt93GhAkTOHjQmIDojz/+YNasWaUC+rx58/D1Ldnj8Pjjj/P444+X6XWmTp3KzTffjMVioWXLlsTExDBnzhxGjx59wTWLiLiU1JPnnwdEnXs/q6fRa97udtizBFa8A/uWwtZvjUtUV+M89dirwGyp+LrLy/ovjHBuMkOLG8Dd57QJvLyMUFliJm/jusBkZeVfK+jaKBhLyh5I2m6cFpCVZAxHTouDPYtLvpZfHQiIPBm2Tw/IZwjfdlvlt8XGmdDncbj8wUvr6XY4jC894lYYX/zcMkdrb5cXkwnCmhmX3o/C8b3GPBHbvof4NXDor1P7Dn5Vp6KIlAMFdDk3Wza8XPf8+5W3x48Yf7RcoNDQUK666iqmT5+Ow+HgqquuIiQkpNR+ffr04f333y+xrejc8vNJTU1l7ty5/P7778XbbrvtNqZOnaqALiJyPmlxxnXgeQJ6EZMJGvczLolbjR71TbONYHDoLwiKhq73GUu1VfWh3knb4ccHjdt9noCeD5X5UIfNRvLWNOwdBmOxWk89kJ1iBPVjO0peZxw5dblYFg/j/GOL9bRr69+2neW22XrufRK3wOY58MsLELcSbvzwzOc2l8Wvr8Cmr4zh18NmGBO3ScWo1dAY3XLZ/ZAWb4T1XQshopUxoZyIXDIFdKlx7rjjDsaNGwfAu+++e8Z9fHx8aNTo4pb/mDlzJrm5uSXOOXc4HNjtdnbt2kWTJk0u6nlFRFxCWXvQzyS8BVz/njHsdtVHsGYqnDgAPz0CS1+EDqOh8z1Gr3FVk58Fc0Yb55k3vMKYXbw8eAdD/W7G5XS5aUZQz0w8d6A2u51lu6Xil7pr0AvmP2T0/H/QE4bOgLodLuw5Ns2GZRON21dPMtpWKkdApDGJY5d7nF2JSI2igC7nZvU2erOd8boXadCgQeTn52MymRg4cGA5FmWYOnUqDz74YKne8vvuu49p06bx8ssvl/triojUGEXnoJe1B/1M/CKg73+gx4PGMOkV70HKXvjjTaOHvcWNxvD3Om3LpeRyMf9ho3fbNwJu+LDiJy/zDICozhX7Gpeq/e3GZzR7pHHu+LSBMGgidLqrbF8OHPwT/vdP43b3fxtf0IiIVHMK6HJuJtNFDTV3JovFwvbt24tvn0leXh4JCQkltrm5uZ1xOPzpNmzYwLp16/jiiy+IjS05hG7EiBE899xzvPDCC7i56VdLROSMitZADyiH06fcvY0w1+EOYx31P9+Bg78bk8ttng3RPYyg3nigc2fz3jATNnxhnHd+01TwDXVeLVVNRCu4e5kRtLf/YPSox60wluo61ykLx/cay6kV5hvLf/V7ttJKFhGpSDV87QlxVf7+/vj7+5/18QULFlC7du0Sl8svv/y8zzt16lSaN29eKpwD3HDDDSQlJTF//vxLql1EpEYrHuJejsulmc3Q9EoY86MR9loNNYZuH1gOX94M73Yylm7Lzy6/1yyrpB2nzjvv/bixRJWU5BkAwz6DgS8Zn9uWb+CjPsY5+2eSnQIzh0FOCtRpXzkjEkREKom6+aRGmD59+jkf/+6770rse779z/a8b7/99ln3jYiIoLCwsEzPKyLikgptpyYtu5Qh7udSpx0M+Rj6PQMrP4C1M4wloH4cb0xI1ulO6DQW/MIr5vVPl59tnHduy4aY3tCjnM47r4lMJmO0Q2QHmDMGknfBR1fANW9C62Gn9ivIh69uNz7TgCgYMcsYSSEiUkPo60YRERGpHOlHwGE3JiHzqeBlsALqwoDnYfxWGPQKBNY3elx/+y9Mbgnf/dOYFb4i/fQwHNsOvuFw40fVazk4Z6nXFe75zfhCw5YNc8fCvAfAlmssp/bD/cZpDO5+cMvsyvmiRUSkEimgi4iISOUomiAuoG7lDUn28IOu98K/18OwT6FuZ+O85Q2fw/vd4bMbYM/PRvgrTxtnwfrPjfPOh3ysdbkvhG8o3DYXej0KmGDNNGMCuYVPGJMCmiwwbDqEN3d2pSIi5U4B3cneffddoqOj8fT0pEuXLqxateqs+86dO5eOHTsSGBiIj48Pbdu25bPPPqvEakVERC5B2iUssXapzBZofh3ctRjuXAzNrzfC895f4PMh8F43WPeZ0VN7qY7tgnknh7P3egwa9Lz053Q1Zgv0eRxu/Rq8guHoBvjr5NKpg/8Ljfo5tTwRkYqigO5EX331FePHj+fpp59m3bp1tGnThoEDB5KUlHTG/YODg3niiSdYsWIFmzZtYsyYMYwZM4aFCxdWcuUiIiIX4VLWQC9PUZ1h2AyjV73rfeDuawxF/36cMfx92cuQkXD+5zmT/GyYMwpsWUYw7/lQ+dbuahr3M4a8R3Y07nf/lzGPgIhIDaWA7kSTJk1i7NixjBkzhubNmzNlyhS8vb2ZNm3aGffv3bs3N9xwA82aNaNhw4bcf//9tG7dmt9//72SKxcREbkIaXHGdUVNEHehgqKNdbcf2Ar9nwf/SMg6BssmwhstjAneDvxxYcPfFzwKSduMc+xv/FjnnZeHwCi4YyH8ax0MeMHZ1YiIVCjN4u4k+fn5rF27lgkTJhRvM5vN9OvXjxUrVpz3eIfDwS+//MLOnTt55ZVXzrpfXl4eeXl5xffT09MBsNls2Gy2EvsWFBTgcDgoLCzEcfKPEYfDgd1uv6D3JmdW1K4FBQUl2v/vn4OUL7Vz5VA7u7ayfv6WE3GYgQLfOjiq0s+Kmw90/gd0uAvTju8xr5mG+fBK2PotbP0WR1hz7O3HYG811OhtPwvTljm4rfsUByYKr5uCwzMYyul96ncM8K9Xbu15NmrnyqF2rjr0GVQ9JoejvGdFkbI4cuQIkZGR/Pnnn3Tr1q14+yOPPMKvv/7KypUrz3hcWloakZGR5OXlYbFYeO+997jjjjvO+jrPPPMMzz77bKntM2fOxNu75LIkJpOJ2rVrExERgZ+f30W+MzmbjIwMEhISOHr0KPq1ExFX1HfbI/jmJfBHo8dI9qvaE3z5Zx+kQfIS6p74Ezd7PgA2sxeHal3O/pC+ZHrWKbG/b+5Reu18Cjd7Hjsirmdn7RudUbaIyAXJzs7mlltuIS0tDX9/f2eXI6gHvdrx8/Njw4YNZGZmsmTJEsaPH09MTAy9e/c+4/4TJkxg/PhT666mp6cTFRXFgAEDzvhLmJiYSHp6Op6entjtdnx9fTGZTBX1dlyCw+EgOzubjIwMateuTdu2bQHjG8vFixfTv39/rFarc4uswdTOlUPt7NrK9Pk7HLhtvhuAzgNuMoaXV3n/wJGbRuGmLzGvnYY1ZR8xxxYTc2wx9uie2DvcgaPJICi04TZ9ECZ7Hvb6l9Pwlg9oWM5D2/U7VjnUzpVD7Vx1FI2ulapDAd1JQkJCsFgsJCYmltiemJhIRETEWY8zm800atQIgLZt27J9+3YmTpx41oDu4eGBh4dHqe1Wq/WM/yBGRkZisVg4duwYOTk5eHl5KaCXk6CgICIiIkq159k+CylfaufKoXZ2bef8/DOPQUEuYMIaXB/cqsnPiTUELvsXdPsn7FsKqz+GXQswH/gN84HfjPPWgxpA0lbwCcV801TMHp4VV45+xyqF2rlyqJ2dT+1f9SigO4m7uzsdOnRgyZIlXH/99QDY7XaWLFnCuHHjyvw8dru9xDnml6pomHtQUBBLliyhZ8+e+sUtB1arFYtFEwWJiAsrmiDOLwLc3J1by8Uwm6FRX+OSGgdrPoF1n0J6vHHBBDd+ZLw/ERGRi6SA7kTjx49n1KhRdOzYkc6dOzN58mSysrIYM2YMACNHjiQyMpKJEycCMHHiRDp27EjDhg3Jy8tj/vz5fPbZZ7z//vvlXpvFYqGgoABPT08FdBERuXRVZYm18hBYD/o9Db0fg63fwZavockgaNjH2ZWJiEg1p4DuRMOHD+fYsWM89dRTJCQk0LZtWxYsWEB4eDgAcXFxmM2nVsLLysrivvvu4/Dhw3h5eREbG8vnn3/O8OHDnfUWREREyibtZECvKkuslQc3D2gz3LiIiIiUAwV0Jxs3btxZh7QvW7asxP0XXniBF17Q+p8iIlINpR02rmtCD7qIiEgFMZ9/FxEREZFLlFoDe9BFRETKmQK6iIiIVLyiSeLUgy4iInJWCugiIiJS8WrSJHEiIiIVRAFdREREKlZeBuSmGrc1xF1EROSsFNBFRESkYhX1nnsGgoefU0sRERGpyhTQRUREpGIVzeCu3nMREZFzUkAXERGRiqUJ4kRERMpEAV1EREQqliaIExERKRMFdBEREalYaVoDXUREpCwU0EVERKRiqQddRESkTBTQRUREpGKpB11ERKRMFNBFRESk4hTkQ0aCcTugnnNrERERqeIU0EVERKTipMcDDnDzBJ8QZ1cjIiJSpSmgi4iISMUpGt4eUBdMJufWIiIiUsUpoIuIiEjF0QRxIiIiZaaALiIiIhVHE8SJiIiUmQK6iIiIVJziIe6aIE5EROR8FNBFRESk4qSedg66iIiInJMCuoiIiFQcDXEXEREpMwV0ERERqRh2O6QdNm5rkjgREZHzUkAXERGRipGVBIX5YDKDfx1nVyMiIlLlKaCLiIhIxSg6/9yvDliszq1FRESkGlBAFxERkYqh889FREQuiAK6iIiIVIw0zeAuIiJyIRTQRUREpGIUL7GmHnQREZGyUEAXERGRiqEh7iIiIhdEAV1EREQqRnEPej3n1iEiIlJNKKCLiIhIxShaA1096CIiImWigC4iIiLlLzcN8tKM25okTkREpEwU0EVERKT8FQ1v9woGdx/n1iIiIlJNKKCLiIhI+dMEcSIiIhdMAV1ERETKn5ZYExERuWAK6CIiIlL+0uKM60DN4C4iIlJWCugiIiJS/opmcFcPuoiISJkpoIuIiEj5Kx7irhncRUREykoBXURERMqfJokTERG5YAroIiIiUr5suZCZaNwO0DnoIiIiZaWALiIiIuUrPd64tnqDd7BzaxEREalGFNBFRESkfKWenME9IApMJufWIiIiUo0ooIuIiEj5KprBXeefi4iIXBAFdBERESlfaZrBXURE5GIooDvZu+++S3R0NJ6ennTp0oVVq1addd+PPvqIHj16EBQURFBQEP369Tvn/iIiIk5RvMSaetBFREQuhAK6E3311VeMHz+ep59+mnXr1tGmTRsGDhxIUlLSGfdftmwZI0aMYOnSpaxYsYKoqCgGDBhAfHx8JVcuIiJyDsVLrGkGdxERkQuhgO5EkyZNYuzYsYwZM4bmzZszZcoUvL29mTZt2hn3/+KLL7jvvvto27YtsbGxfPzxx9jtdpYsWVLJlYuIiJzD6ZPEiYiISJkpoDtJfn4+a9eupV+/fsXbzGYz/fr1Y8WKFWV6juzsbGw2G8HBWsJGRESqCHshpB8xbmuSOBERkQvi5uwCXFVycjKFhYWEh4eX2B4eHs6OHTvK9ByPPvooderUKRHy/y4vL4+8vLzi++np6QDYbDZsNttZjyt67Fz7yKVRG1cOtXPlUDu7thKff3oyVrsNh8lCgWct0M9EudDvWOVQO1cOtXPVoc+g6lFAr6ZefvllZs2axbJly/D09DzrfhMnTuTZZ58ttX3RokV4e3uf93UWL158SXXK+amNK4fauXKonV3b4sWLCcrcTU8gxxrE4gWLnF1SjaPfscqhdq4camfny87OdnYJ8jcK6E4SEhKCxWIhMTGxxPbExEQiIiLOeexrr73Gyy+/zM8//0zr1q3Pue+ECRMYP3588f309PTiyeX8/f3PepzNZmPx4sX0798fq9VahnckF0ptXDnUzpVD7ezaTv/83XflwG7wjGjM4MGDnV1ajaHfscqhdq4caueqo2h0rVQdCuhO4u7uTocOHViyZAnXX389QPGEb+PGjTvrca+++iovvvgiCxcupGPHjud9HQ8PDzw8PEptt1qtZfoHsaz7ycVTG1cOtXPlUDu7NqvViluGcf65ObA+Zv0slDv9jlUOtXPlUDs7n9q/6lFAd6Lx48czatQoOnbsSOfOnZk8eTJZWVmMGTMGgJEjRxIZGcnEiRMBeOWVV3jqqaeYOXMm0dHRJCQkAODr64uvr6/T3oeIiEix4iXWNEGciIjIhVJAd6Lhw4dz7NgxnnrqKRISEmjbti0LFiwonjguLi4Os/nURPvvv/8++fn53HTTTSWe5+mnn+aZZ56pzNJFRETOLO2wca0l1kRERC6YArqTjRs37qxD2pctW1bi/oEDByq+IBERkUuRerIHPaCuc+sQERGphrQOuoiIiJQPh+O0Ie71nFuLiIhINaSALiIiIuUjNxXyM43b6kEXERG5YAroIiIiUj6Kes99QsHq5dxaREREqiEFdBERESkXJk0QJyIickkU0EVERKRcmNKLArqGt4uIiFwMBXQREREpH5ogTkRE5JIooIuIiEi50BB3ERGRS6OALiIiIuWjuAddAV1ERORiKKCLiIhIuVAPuoiIyKVRQBcREZFLZrbnY8pONu6oB11EROSiKKCLiIjIJfPOP27ccPcFz0Cn1iIiIlJdKaCLiIjIJfPKP9l7HhAFJpNzixEREammFNBFRETkknnna3i7iIjIpVJAFxERkUvmVTTEXRPEiYiIXDQFdBEREblk6kEXERG5dAroIiIicsm8bOpBFxERuVQK6CIiInLJvDXEXURE5JIpoIuIiMilsRfgmZ9i3NYQdxERkYumgC4iIiKXJiMBM3YcZiv4Rji7GhERkWrLzdkFiIiIyHnkZcLh1ZB6EHxCjRDsFw6+4WCxOrs6TGmHjBv+kWDWd/8iIiIXSwFdRESkqslKhri/IG4FHPwTjm4ER+GZ9/WudVpgP3ntV9sI734REBoLXoEVW2/6YQAcAXUxVewriYiI1GgK6CIiIs7kcEBq3KkwHrcCkneV3i8gygjb2cchM9G42AuM+9nHIWnrmZ/fwx9u+Qrqd6+wt2BKO3yyxroV9hoiIiKuQAFdRESkMtntcGwHxP0JB1cYgTw9vvR+obFQr5sRrOt1Kz35mt0OOSmQkQCZCZCRWPr6xAHj9hdD4ba5UK9Lxbynk0PcHf4K6CIiIpdCAV1ERKQiFeTD0Q0ne8dPDlvPTS25j9kNareF+t2gXneo1xW8g8/9vGYz+IQYF1qeeR9bDswcDvt/hc+HwO3fQlSnS39Pf1PUg+7QEmsiIiKXRAFdRESkPOVlwuFVp3rHD6+BgpyS+1i9oW6nU73jdTuCu0/512L1ghGzYOYwOLAcPr8RRn4HkR3K9WWKJ4lTQBcREbkkCugiIiKXIvOYEcSLLkc3lZ7QzbuWEcTrdTN6ySNaV97s6+7exjnoXwyFg3/AZzfAyP9BnXbl8/wOB6SdmiRORERELp4CuoiISFk5HMZSZwdXnDqH/Pju0vsF1Ds5XP3kOeQhTcDkxPnN3X3gltnwxU3GlwifXg+jvofabS79ubNTMBWNEPCPvPTnExERcWEK6MDu3btZunQpSUlJ2O32Eo899dRTTqpKRESczm6HY9tPza5+cAVkHCm9X1hz47zxet2NYF4Ve5I9fOHWOfDZjcYQ/E+vg1E/QESrS3ve1IMA5LoFYHHzKIdCRUREXJfLB/SPPvqIf/zjH4SEhBAREYHptB4Ok8mkgC4i4iryMowZ0dOPwJF1Rhg/9BfkppXcz+xmDA8v6h2P6nL+Cd2qCg8/uO0bY5h7/BqYcS2MngfhLS78ufIyYe0n8OfbAGR7hOJXzuWKiIi4GpcP6C+88AIvvvgijz76qLNLERGR8uZwnAremQnGddHl7/dtWWd+DquPMfN5Ue94ZEfjvO7qytMfbp9rDHM/su5USA9rVrbjc9Ng1Yew4j1jmTeM5dV2hN5A+c8PLyIi4lpcPqCfOHGCoUOHOrsMEREpL6lxsP+3U5eMo2U/1sMffMMhtOlpE7q1AUsN++/SM+BkSL8Ojm6EGdfA6B+N93022Snw1/uw8gPIOzmqIDgGejxIQbMbObZwceXULiIiUoPVsL84LtzQoUNZtGgR9957r7NLERGRi5F5DA78Bvt+NQL5if2l9/HwB78II3z71Qa/ousI8I0wrv0iKmaps6rKKwhu/w4+vRYSNp8K6SGNS+6XeQxWvA2rp0J+prEtNBZ6PAQtbjC+vLDZKr18ERGRmsjlA3qjRo34z3/+w19//UWrVq2wWksue/Pvf//bSZWJiMgZ5abBgT9O9pD/CknbSj5uskBke2jQExr0Mm576OzoM/IOhpHfG+E8cQtMvxrGzIdaDY1z8f94C9ZOP7WOe0Qr6PkwxF4DZrNTSxcREamJXD6gf/jhh/j6+vLrr7/y66+/lnjMZDIpoIuIVAWFBbBuBmz4Ao6sB0fJFTcIb3UykPc0Jm7z9HdOndWRd7CxLvqMa4wvO6ZfDY37w8YvoTDf2CeyA/R8BJoMdO5ycSIiIjWcywf0/fvPMBRSRESqjv3L4adHIWnrqW3BDSGmlxHIo3uAT4jz6qsJfEJO9qRfDcd2GF+GANS/DHo+BDF9FMxFREQqgcsHdBERqaJS42DRk7Dtf8Z9ryCjF7f5tVVznfHqzjfUWBf9mzvBzRMu+z+IvszZVYmIiLgUlwzo48eP5/nnn8fHx4fx48efc99JkyZVUlUiIgJAfjb88Sb8MRkKcsFkho53QJ8nqs9649WVb5gR0kVERMQpXDKgr1+/HtvJGWfXr19/1v1MGs4nIlJ5HA7Y9h0s+g+kHTK21b8crnwFIlo6tTQRERGRyuCSAX3p0qVnvC0iIk6SuNU4z/zAcuO+f10Y+AI0v17nPouIiIjLcMmALiIiVUR2Cix9CdZMNWZmLzr3+bL7wd3b2dWJiIiIVCoFdGDNmjXMnj2buLg48vPzSzw2d+5cJ1UlIlKDFdqMmcJ/eQFyThjbml8HA16AwHrOrU1ERETESczOLsDZZs2aRffu3dm+fTvffvstNpuNrVu38ssvvxAQEODs8kREag6Hw1jD/KdH4fVY+PFBI5yHNTcmJhv2qcK5iIiIuDSXD+gvvfQSb7zxBj/88APu7u68+eab7Nixg2HDhlGvXsX/ofjuu+8SHR2Np6cnXbp0YdWqVWfdd+vWrQwZMoTo6GhMJhOTJ0+u8PpERC5Z2mFYPgne6wof9oaVUyA7GXzCYPBrcM9yYz1zERERERfn8kPc9+7dy1VXXQWAu7s7WVlZmEwmHnjgAa644gqeffbZCnvtr776ivHjxzNlyhS6dOnC5MmTGThwIDt37iQsLKzU/tnZ2cTExDB06FAeeOCBCqtLRKqgwgL4/Q3Y8zO4+4CnP3gGgIe/cdsj4AzbTrs2Wyq33rxM2P4DbPwS9v8GOIztbp7QdDC0GQENrwCLy/83JCIiIlLM5f8yCgoKIiMjA4DIyEi2bNlCq1atSE1NJTs7u0Jfe9KkSYwdO5YxY8YAMGXKFH788UemTZvGY489Vmr/Tp060alTJ4AzPi4iNVR2Cnw9BvYtu/jncPcrGdrPFOQ9A04FfY+T94tuu/uC+TyDrhx2TPuWwdY5Rji3nfZvaP3LoM3Nxnnmnjp9SERERORMXD6g9+zZk8WLF9OqVSuGDh3K/fffzy+//MLixYvp27dvhb1ufn4+a9euZcKECcXbzGYz/fr1Y8WKFeX2Onl5eeTl5RXfT09PB8BmsxWvBX8mRY+dax+5NGrjylHt2zlhM25fj8KUFofD6o299xM4PPwx5aXDyYsp99RtctNPPZabjqnw5O9/foZxIf6iynBgAg+/4mDv8PArDvYOD3+w2xmw9X+4bThx6pjgGOythmNveRME1j/1ZNX1s5Czqva/Z9WA2rhyqJ0rh9q56tBnUPW4fEB/5513yM3NBeCJJ57AarXy559/MmTIEJ588skKe93k5GQKCwsJDw8vsT08PJwdO3aU2+tMnDjxjMP0Fy1ahLf3+ZcwWrx4cbnVImemNq4c1bGd66b8SZu4aZgc+WS6h7Eq5n4ykqNOPnpaL7QZ8Dp5+Ruz3YZbYQ7Wwuzii5vduF9yew5uJ6+Nx7KxntzP7CjEhOPUlwDA31cmtwBWIN/iQ3xQVw4FX8YJ74aQboI/twJby7t5pAqqjr9n1Y3auHKonSuH2tn5KnrEsFw4lw/owcHBxbfNZnONGzo+YcIExo8fX3w/PT2dqKgoBgwYgL+//1mPs9lsLF68mP79+2O1WiujVJejNq4c1bKdC22Yf3kGy8EPALA37IfHdVPo4RVY+aU4HBQW5EJuGuRllOi5N3rr0yA3A3teJuuPe9DyhvHU9fKlbqVXKs5ULX/Pqhm1ceVQO1cOtXPVUTS6VqoOlw/oRbZu3UphYWHxfYvFQosWLSrs9UJCQrBYLCQmJpbYnpiYSERERLm9joeHBx4eHqW2W63WMv2DWNb95OKpjStHtWnnzGMwZzQc/N243+MhzH0ex1zZk7ydzt0dvM/+hR6A3Wbj6Pz5tPPyrR7tLBWi2vyeVWNq48qhdq4camfnU/tXPS67zNry5cuLJ1wD6Nq1K+3ataNt27a0bduW1q1b8/PPP1fY67u7u9OhQweWLFlSvM1ut7NkyRK6detWYa8rIlVY/Fr4sJcRzt19Yfjn0Pc/lT8Du4iIiIg4hcv2oL/33nvcfvvtJbYtXbqU+vXr43A4eOutt3j//ffp169fhdUwfvx4Ro0aRceOHencuTOTJ08mKyureFb3kSNHEhkZycSJEwFjYrlt27YV346Pj2fDhg34+vrSqFGjCqtTRCrB+s9h3ngozINajeHmLyC0qbOrEhEREZFK5LIBfc2aNTzxxBMlttWtW5f69Y2Zhm+//fbi9dEryvDhwzl27BhPPfUUCQkJtG3blgULFhRPHBcXF4f5tGWNjhw5Qrt27Yrvv/baa7z22mv06tWLZcuWVWitIlJBCvJh4QRY/bFxv+lguGGKliITERERcUEuG9APHz5MQMCpP4BnzJhR4tzv4OBgjh8/XuF1jBs3jnHjxp3xsb+H7ujoaBwOR4XXJCKVJCMRZo+EQ38BJujzOPR46PzrjYuIiIhIjeSyAd3Pz4+9e/cSFWUsWXTjjTeWeHz//v3nnOVcROSSHF4DX90GGUeN9cRv/AiaDnJ2VSIiIiLiRC7bTdOlSxc+/fTTsz4+ffp0unTpUokViYjL2PodTL/KCOehsTB2qcK5iIiIiLhuD/r48ePp168ftWrV4uGHHyYsLAyApKQkXnnlFT7//HMWLVrk5CpFqpH8bLB6gcnk7EqqLocD/nwLFj9l3G8yCIZ8DB5+zq1LRERERKoElw3offr04e233+aBBx5g0qRJ+Pv7YzKZSEtLw83NjcmTJ3PFFVc4u0yRqi9xKyx5DnYtAM9AiGgFtdtARGuo3dqYkdzisv/UnFJog/kPwdrpxv3O98CgiVpCTURERESKufRfzffddx/XXHMNX3/9Nbt37wagcePG3HTTTcXnpovIWZw4AEsnwqavgJOTF+amwoHlxqWImxeENy8Z2sNagNXTCUU7SW46zBkFe38BTDDoZeh6r7OrEhEREZEqxqUDOkBUVBQPPPCAs8sQqT4yj8Hy12D1VLDbjG3Nr4dejxr3j26ChE0nrzeDLQvi1xqXIiaLscZ3UWCPaG30vHsFOuMdVay0w/DFMEjaClZvGDIVYgc7uyoRERERqYJcPqCLSBnlpsOKd+DPd4zQDRDTB/o+BZHtT+1Xu82p2/ZCSNkHRzeeFto3QfZxSNpmXDbNOrV/UPRpob2Nce13avnDaufIeph5M2QmgG843PIV1Gnn7KpEREREpIpSQBeRcyvIM3rLl79mBGswQma/ZyCm97mPNVsgpLFxaXWTsc3hgPQjJwP7xlOhPe2QMWz+xAHY/v2p5/AJO9XLXru18QVAYHTVXyt850/w9R1gy4aw5nDLbAjUqTMiIiIicnYK6CI13YHfjUDsE2KEXZ9Q8K51/onb7IXG+eVLXzLCM0CtRnDFf6D5dRc/W7vJBAGRxqXplae2Z6eU7GU/ugmSd0FWEuz52bgU8fA3hsSfPkQ+tClYrBdXU3n7awoseAxwQMMrYOgM8PR3dlUiIiIiUsUpoIvUZHuWwOc3nuEBkxHSfULBN9S49gkzQrxvGJjMxlD2Y9uN3f3qQO/HoO2tFTcju3ew0SN/eq98fpYxS/zpQ+STtkFeOhz8w7gUsXhAWLPTetvbQngLMFViaLcXwoIJsOoD4377UXDV61XniwMRERERqdIU0IHU1FS+/vpr9u7dy8MPP0xwcDDr1q0jPDycyMhIZ5cncnFy0+D7fxu3Q5oaw80zk04OU3dAdrJxKQrhZ+IZCD3GQ+e7jTXOK5u7D0R1Ni5FCm1wbOepwH50ozEZXX4GHN1gXIqYzLjVakT7wlqY/9oHke2M8O4dXP615mXCN3fBrp+M+/2ehcvu17rwIiIiIlJmLh/QN23aRL9+/QgICODAgQOMHTuW4OBg5s6dS1xcHJ9++qmzSxS5OAufgPTDENQA7l5qhF2AwgLISTHCetYx4/L327mp0KAndP931ZtZ3WKFiJbGpe0txja7HU7sLz1EPisJU/IuogCWrDj1HAFRJ3vZ25zqcfevU7Yw7XAYX35kJBiTv2UkQMZR2PKN8UWBxQNu/ABa3FAR715EREREajCXD+jjx49n9OjRvPrqq/j5+RVvHzx4MLfccosTKxO5BLt/hvWfASa4/r1T4RyMIeq+YcalpjCboVZD43J6MM5IoODQWnYv/4am/nmYEzdD6kHjnPq0Q7Dzx1P7etcqeU67w34yfJ8M4KdfF+ScuQ7vEBjxZckefxERERGRMnL5gL569Wo++OCDUtsjIyNJSEhwQkUilygnFb7/l3G76z+gfnenluNUfhE4Gg9g1+4CGg0ejNlqNdonYXPJIfLJu4yh//uWGpey8AwAv9rGMnB+tSGgLrQfCYH1KvQtiYiIiEjN5fIB3cPDg/T09FLbd+3aRWhoqBMqErlECx+HjCMQ3NCYcV1K8gqEBj2MSxFbDiRug4SNpyais7iXDOAlriOcc06+iIiIiNRoLh/Qr732Wp577jlmz54NgMlkIi4ujkcffZQhQ4Y4uTqRC7RrIWz4glND272dXVH1YPWCuh2Mi4iIiIiIk5idXYCzvf7662RmZhIWFkZOTg69evWiUaNG+Pn58eKLLzq7PJGyyzlxatb2bv+Eel2dW4+IiIiIiFwQl+9BDwgIYPHixfz+++9s2rSJzMxM2rdvT79+/ZxdmsiF+ekxY1bxWo3giiedXY2IiIiIiFwglw/oRS6//HIuv/xyZ5chcnF2zIdNs8Bkhuun6PxoEREREZFqyOUD+ltvvXXG7SaTCU9PTxo1akTPnj2xWCyVXJlIGWWnwLz/M253GwdRnZxajoiIiIiIXByXD+hvvPEGx44dIzs7m6CgIABOnDiBt7c3vr6+JCUlERMTw9KlS4mKinJytSJn8NOjkJkIIU2gzxPOrkZERERERC6Sy08S99JLL9GpUyd2797N8ePHOX78OLt27aJLly68+eabxMXFERERwQMPPODsUkVK2/4DbJ592tB2T2dXJCIiIiIiF8nle9CffPJJvvnmGxo2bFi8rVGjRrz22msMGTKEffv28eqrr2rJNal6so7DvJNfHF12v5YIExERERGp5ly+B/3o0aMUFBSU2l5QUEBCQgIAderUISMjo7JLEzm3nx6GrGMQ2gx6T3B2NSIiIiIicolcPqD36dOHe+65h/Xr1xdvW79+Pf/4xz+44oorANi8eTMNGjRwVokipW39DrZ8AyYLXP8euHk4uyIREREREblELh/Qp06dSnBwMB06dMDDwwMPDw86duxIcHAwU6dOBcDX15fXX3/dyZVKjeOw41aYAw7HhR2XlQw/PmjcvvwBiGxf/rWJiIiIiEilc/lz0CMiIli8eDE7duxg165dADRt2pSmTZsW79OnTx9nlSc1icMBx/fC/mWw/zfc9i/nqpwUHLsegeCYkpegBsa1bxiYTCWf58cHITsZwlpAr0ec8lZERERERKT8uXxALxIbG0tsbKyzy5CaJi0e9v8K+38zLunxxQ8VxW5TbhocWW9c/s7qczK0nwzsOGDbdxraLiIiIiJSAymgA4cPH+b7778nLi6O/Pz8Eo9NmjTJSVVJtZR1HA6cDOP7foWUvSUft7hDVBdo0IuCepexcH0cA7q2wJoeByn7Sl7SDoMtCxI3G5fT9XwI6rSttLclIiIiIiIVz+UD+pIlS7j22muJiYlhx44dtGzZkgMHDuBwOGjfXuf2ynnkZcDBP08F8r8HaZMZ6rSHBj0hppcRzq1eADhsNgo2HYPQWKjTqvRzF+RD6hmCu1849HioEt6ciIiIiIhUJpcP6BMmTOChhx7i2Wefxc/Pj2+++YawsDBuvfVWBg0a5OzypKqx5cLhVacCefxacBSW3CesOTToZQTy+t3BM+DiXsvNHUIaGRcREREREanxXD6gb9++nS+//BIANzc3cnJy8PX15bnnnuO6667jH//4h5MrFKcqLICjG4zzyPf9CodWQkFuyX2CGpzqIY/uYUzsJiIiIiIicoFcPqD7+PgUn3deu3Zt9u7dS4sWLQBITk52ZmniDA4HJG071UN+8A/ISy+5j2/4qR7yBj0hsJ5zahURERERkRrF5QN6165d+f3332nWrBmDBw/mwQcfZPPmzcydO5euXbs6uzypaA4HnNh/KpDv/81Ywux0ngFGz3hRKA9pUnrpMxERERERkUvk8gF90qRJZGZmAvDss8+SmZnJV199RePGjTWDe02VfvTUsmf7f4W0QyUft3pDvW6nhq1HtAazxTm1ioiIiIiIy3D5gB4TE1N828fHhylTpjixGqkQOaklA3nyrpKPm61Qt9OpQB7Z0ZigTUREREREpBIpoMfEsHr1amrVqlVie2pqKu3bt2ffvn1OqkwuWX4W/PEm/PEWFOSc9oAJarc5FcjrdQN3H6eVKSIiIiIiAgroHDhwgMLCwlLb8/LyiI+Pd0JFcsnsdtj0FSx5FjKOGttqNYKYPieXPrsMvIOdW6OIiIiIiMjfuGxA//7774tvL1y4kICAU2tVFxYWsmTJEqKjo51QmVySuL9gwWNwZL1xP7A+DHgeml2rid1ERERERKRKc9mAfv311wNgMpkYNWpUicesVivR0dG8/vrrTqhMLsqJg/Dz07D1W+O+ux/0fBC6/AOsns6tTUREREREpAxcNqDb7XYAGjRowOrVqwkJCXFyRXJR8jJg+SRY8S4U5gEmaD8SrngSfMOcXZ2IiIiIiEiZmZ1dgLPt37/fqeH83XffJTo6Gk9PT7p06cKqVavOuf+cOXOIjY3F09OTVq1aMX/+/EqqtIqxF8K6T+Gt9vD7JCOcR/eAe5fDtW8pnIuIiIiISLXjsj3op1uyZAlLliwhKSmpuGe9yLRp0yrsdb/66ivGjx/PlClT6NKlC5MnT2bgwIHs3LmTsLDSAfPPP/9kxIgRTJw4kauvvpqZM2dy/fXXs27dOlq2bFlhdVY5+5fDwgmQsNm4HxwDA16ApoN1nrmIiIiIiFRbLt+D/uyzzzJgwACWLFlCcnIyJ06cKHGpSJMmTWLs2LGMGTOG5s2bM2XKFLy9vc/6pcCbb77JoEGDePjhh2nWrBnPP/887du355133qnQOquEzCRYOx0+vR5mXG2Ec48AGPAi3LcSYq9SOBcRERERkWrN5XvQp0yZwvTp07n99tsr9XXz8/NZu3YtEyZMKN5mNpvp168fK1asOOMxK1asYPz48SW2DRw4kO++++6sr5OXl0deXl7x/fT0dABsNhs2m+2sxxU9dq59KtyJ/Zh3zse0cz6mw6sw4QDAYTJjbz8ae49HwCcEHIAz67xIVaKNXYDauXKonV2bPv+KpzauHGrnyqF2rjr0GVQ9Lh/Q8/Pz6d69e6W/bnJyMoWFhYSHh5fYHh4ezo4dO854TEJCwhn3T0hIOOvrTJw4kWeffbbU9kWLFuHt7X3eOhcvXnzefcqNw0FAzkFqp60lInUdAbmHSjx8wrsBRwM6cCSoC1n2cPj13OfrVxeV2sYuTO1cOdTOrk2ff8VTG1cOtXPlUDs7X3Z2trNLkL9x+YB+1113MXPmTP7zn/84u5QKMWHChBK97unp6URFRTFgwAD8/f3PepzNZmPx4sX0798fq9VacQXaCzAd+gvTzp8w75qPKe1UKHeYLDjqX4ajyWDsTa/E1z+SxkDjiqumUlVaG7s4tXPlUDu7Nn3+FU9tXDnUzpVD7Vx1FI2ularD5QN6bm4uH374IT///DOtW7cu9Y/EpEmTKuR1Q0JCsFgsJCYmltiemJhIRETEGY+JiIi4oP0BPDw88PDwKLXdarWW6R/Esu53weyF8MO/Ycd8yEk5td3NCxr1hWbXYGo8AJN3MACW8q+gyqiwNpYS1M6VQ+3s2vT5Vzy1ceVQO1cOtbPzqf2rHpcP6Js2baJt27YAbNmypcRjpgqcdMzd3Z0OHTqwZMkSrr/+esBYm33JkiWMGzfujMd069aNJUuW8H//93/F2xYvXky3bt0qrM4KY7ZA4jYjnHsFQZMrodnVENMH3M8/9F5ERERERKSmcfmAvnTpUqe99vjx4xk1ahQdO3akc+fOTJ48maysLMaMGQPAyJEjiYyMZOLEiQDcf//99OrVi9dff52rrrqKWbNmsWbNGj788EOnvYdL0vc/YHaDet3B4vI/iiIiIiIi4uKUik7as2cPe/fupWfPnnh5eeFwOCq0Bx1g+PDhHDt2jKeeeoqEhATatm3LggULiieCi4uLw2w+tRJe9+7dmTlzJk8++SSPP/44jRs35rvvvqu+a6A3vMLZFYiIiIiIiFQZLh/Qjx8/zrBhw1i6dCkmk4ndu3cTExPDnXfeSVBQEK+//nqFvv64cePOOqR92bJlpbYNHTqUoUOHVmhNIiIiIiIiUvnM59+lZnvggQewWq3ExcWVWHZs+PDhLFiwwImViYiIiIiIiCtx+R70RYsWsXDhQurWrVtie+PGjTl48KCTqhIRERERERFX4/I96FlZWSV6zoukpKSccXkyERERERERkYrg8gG9R48efPrpp8X3TSYTdrudV199lT59+jixMhEREREREXElLj/E/dVXX6Vv376sWbOG/Px8HnnkEbZu3UpKSgp//PGHs8sTERERERERF+HyPegtW7Zk165dXH755Vx33XVkZWVx4403sn79eho2bOjs8kRERERERMRFuHwPOkBAQABPPPGEs8sQERERERERF+byPeiffPIJc+bMKbV9zpw5zJgxwwkViYiIiIiIiCty+YA+ceJEQkJCSm0PCwvjpZdeckJFIiIiIiIi4opcfoh7XFwcDRo0KLW9fv36xMXFOaEiqYkKCu0cOJ7N7sQMdiZmsDsxkx0J6eRmWQhrcYJujcKcXaKIiIiIiDiZywf0sLAwNm3aRHR0dIntGzdupFatWs4pSqotu93BoRPZ7ErMZFdiBrsSM9iZkMG+Y1nkF9rPcISJMTPWMuW2DvRuqpAuIiIiIuLKXD6gjxgxgn//+9/4+fnRs2dPAH799Vfuv/9+br75ZidXJ1WVw+HgSFquEcITMooD+Z6kTHJshWc8xstqoUm4L43D/Wga7keDWp68/sNatqfC2E/X8Mbwtlzduk7lvhEREREREakyXD6gP//88xw4cIC+ffvi5mY0h91uZ+TIkToHXXA4HBzLyGNXYubJoemnhqhn5hWc8Rh3NzONQn1pEu5Lkwg/moT50TTCj8hAL8xmU/F+NpuNtKZ2fsmqw49bEvjXl+vJyC1gROd6lfX2RERERESkCnHpgO5wOEhISGD69Om88MILbNiwAS8vL1q1akX9+vWdXV6N9/HyfaTl2Ajx9SDE14Navu6E+HoQ6uuBv5cbJpPp/E9SjlKy8ouHpRs945nsSsogNdt2xv3dzCZiQn2Ke8SbhPvSJNyPesHeuFnKNv+imxleH9qKAB93Zq6MY8LczaTn2LinV8PyfGsiIiIiIlINuHxAb9SoEVu3bqVx48Y0btzY2SW5lK/XHmZHQsYZH3O3mIsDe9G1cXGnlq87Qd6nXXys+HqUPdCn59qMnvCEzNMCeSbJmXln3N9sguhaPjQO96VpuJ8RyCP8iK7lg7vbpS+EYDGbePH6lgR4WXl/2V4m/rSD1BwbjwxsWulfUoiIiIiIiPO4dEA3m800btyY48ePK5w7wU0d6nLweDbJmXkkZ+ZxPDOfY5l5ZOQWkF9o52haLkfTcsv0XFaLiQAvd4J9rAR6uxPkbSXYx51Ab3cCvawkZ+axMzGT3YkZ53zOqGAvmoT5GUPTT/aINwz1xdNqKa+3fUYmk4lHB8Xi72nllQU7eH/ZXtJzbDx/XcsSw+JFRERERKTmcumADvDyyy/z8MMP8/7779OyZUtnl+NS7uoRc8btubZCjmflk5yRx/GsPJIzjOBuBPl8TmTlk5KVT2p2PieybeTYCrEVOoqDflnUDvA8OTT91KRtjcJ88fFw7q/EP3o3xN/LjSe/28IXK+PIyC3g9WFtsJZxyLyIiIiIiFRfLh/QR44cSXZ2Nm3atMHd3R0vL68Sj6ekpDipMtflabUQGehFZKDX+XfGCPQnsotCu40T2UaIP3Hydmq2jQAvK03C/Wga4UujMD8CvKwV/C4u3q1d6uPvaeWBrzbw/cYjZOYV8N6t7Su8F19ERERERJzL5QP65MmTnV2CXCJPq4XaAV7UDihboK8OrmlTB19PN/7x+Vp+2ZHEyGmr+HhUR/w9q+4XCyIiIiIicmlcPqCPGjXK2SWInFGfpmF8ekcX7py+mlX7U7jlo7+YMaYztXw9nF2aiIiIiIhUAJ3YCuzdu5cnn3ySESNGkJSUBMBPP/3E1q1bnVyZuLrODYL58u6u1PJxZ0t8OsM+WMGR1BxnlyUiIiIiIhXA5QP6r7/+SqtWrVi5ciVz584lMzMTgI0bN/L00087uToRaBkZwOx7u1EnwJO9x7LoP+lXhk1ZwX++28IXKw+y9mAKGblnXqtdRERERESqD5cf4v7YY4/xwgsvMH78ePz8/Iq3X3HFFbzzzjtOrEzklIahvsz5R3dGT1vF7qRMVh1IYdWBkhMY1g3yIjbCn9gIP2Jr+xF7cq12N80ALyIiIiJSLbh8QN+8eTMzZ84stT0sLIzk5GQnVCRyZpGBXvx0fw92JWayMzGdHUcz2JGQwc6EDBLSczl8IofDJ3L4eXti8THubmYah/lyU4e6jLmsgROrFxERERGR83H5gB4YGMjRo0dp0KBkeFm/fj2RkZFOqkrkzNwsZprX8ad5HX9od2p7anY+OxIy2HE0nZ2JGWw/msGuxAyy8wvZeiSdrUe2YTGbGNkt2mm1i4iIiIjIubl8QL/55pt59NFHmTNnDiaTCbvdzh9//MFDDz3EyJEjnV2eSJkEervTNaYWXWNqFW+z2x0cPpHDl6vjeH/ZXp75fiv1gr3p3TTMiZWKiIiIiMjZuPzJqS+99BKxsbFERUWRmZlJ8+bN6dmzJ927d+fJJ590dnkiF81sNlGvljePDGzKTR3qYnfAuJnr2ZGQ7uzSRERERETkDFw+oLu7u/PRRx+xb98+5s2bx+eff86OHTv47LPPsFgszi5P5JKZTCZeuqEVXRoEk5lXwJ3T15CUkevsskRERERE5G9cNqDb7XZeeeUVLrvsMjp16sS7775Lnz59GDZsGI0bN3Z2eSLlyt3NzAe3d6BBiA/xqTmM/XQtOfmFzi5LRERERERO47IB/cUXX+Txxx/H19eXyMhI3nzzTf75z386uyyRChPo7c600Z0I9Lay8VAqD87ZgN3ucHZZIiIiIiJykssG9E8//ZT33nuPhQsX8t133/HDDz/wxRdfYLfbnV2aSIVpEOLDB7d1wGoxMX9zAq8t2unskkRERERE5CSXDehxcXEMHjy4+H6/fv0wmUwcOXLEiVWJVLwuMbV4+cbWALy3bC+z1xxyckUiIiIiIgIuHNALCgrw9PQssc1qtWKz2ZxUkUjlGdKhLv+6ohEAj8/dzJ97k51ckYiIiIiIuOw66A6Hg9GjR+Ph4VG8LTc3l3vvvRcfH5/ibXPnznVGeSIV7oF+TdifnMW8TUf5x+frmHtfdxqG+jq7LBERERERl+WyAX3UqFGltt12221OqETEOcxmE68NbUN8ag7r41K5Y/pqvr3vMoJ93J1dmoiIiIiIS3LZgP7JJ584uwQRp/O0Wvjw9o7c8N4fHDyezb2freWzuzrj4WZxdmkiIiIiIi7HZc9BFxFDqJ8H00Z3ws/DjVUHUpjwzWYcDi2/JiIiIiJS2RTQRYQm4X68e2t7LGYTc9fH884ve5xdkoiIiIiIy1FAFxEAejYJ5bnrWgDw+uJdzNukJQdFRERERCqTArqIFLu1S33uurwBABO+2czRtBwnVyQiIiIi4joU0EWkhAmDm9E2KpCMvAIe0/noIiIiIiKVRgHdSVJSUrj11lvx9/cnMDCQO++8k8zMzHMe8+GHH9K7d2/8/f0xmUykpqZWTrHiUiwnl19zdzPz665jzFlz2NkliYiIiIi4BAV0J7n11lvZunUrixcvZt68efz222/cfffd5zwmOzubQYMG8fjjj1dSleKqGoX58mD/JgA8P28bR1I11F1EREREpKIpoDvB9u3bWbBgAR9//DFdunTh8ssv5+2332bWrFkcOXL2ibn+7//+j8cee4yuXbtWYrXiqu7qEVM81H3CXA11FxERERGpaAroTrBixQoCAwPp2LFj8bZ+/fphNptZuXKlEysTOUVD3UVEREREKpebswtwRQkJCYSFhZXY5ubmRnBwMAkJCeX6Wnl5eeTl5RXfT09PB8Bms2Gz2c56XNFj59pHLk11aOP6QR78X9+GvLpwN8/N20bXBoHUDvB0dlkXpDq0c02gdnZt+vwrntq4cqidK4fauerQZ1D1KKCXo8cee4xXXnnlnPts3769kqoxTJw4kWeffbbU9kWLFuHt7X3e4xcvXlwRZclpqnob13ZAtK+FA5kF3P3RMu5tZsdkcnZVF66qt3NNoXZ2bfr8K57auHKonSuH2tn5srOznV2C/I0Cejl68MEHGT169Dn3iYmJISIigqSkpBLbCwoKSElJISIiolxrmjBhAuPHjy++n56eTlRUFAMGDMDf3/+sx9lsNhYvXkz//v2xWq3lWpMYqlMbN+ucxbXvrWBHGmRHtGRoh7rOLqnMqlM7V2dqZ9emz7/iqY0rh9q5cqidq46i0bVSdSigl6PQ0FBCQ0PPu1+3bt1ITU1l7dq1dOjQAYBffvkFu91Oly5dyrUmDw8PPDw8Sm23Wq1l+gexrPvJxasObRxbJ5AH+zdh4k87mPjTLnrHRlAn0MvZZV2Q6tDONYHa2bXp8694auPKoXauHGpn51P7Vz2aJM4JmjVrxqBBgxg7diyrVq3ijz/+YNy4cdx8883UqVMHgPj4eGJjY1m1alXxcQkJCWzYsIE9e/YAsHnzZjZs2EBKSopT3oe4lrt6xNCunjGr+2Oa1V1EREREpNwpoDvJF198QWxsLH379mXw4MFcfvnlfPjhh8WP22w2du7cWeK8kClTptCuXTvGjh0LQM+ePWnXrh3ff/99pdcvrsdiNvHfm4xZ3X/bdYzZaw45uyQRERERkRpFQ9ydJDg4mJkzZ5718ejo6FI9lM888wzPPPNMBVcmcnaNwnx5aEATXpq/gxfmbadH49BqN9RdRERERKSqUg+6iFyQOy/XUHcRERERkYqggC4iF0RD3UVEREREKoYCuohcsKKh7gAvzNvOkdQcJ1ckIiIiIlL96Rx0Ebkod14ew4ItCayLS+WxuZuZMaYTJpPJafXY7Q6ybYVk5xWQlV9IVl4BWXkFpOfksT7ZRNbaePIKHWTnF5KZV1C8X3Z+AVl5J/c/eT/PZmdgi3Aev6oZHm4Wp70nEREREXEtCugiclEsZhP/HdqGK99czm+7jvHV6kPc3LlemY49W5jOzi8kK7+A7Dzjujg0nxaeM/MKS4Xr7Hzj2HNUC7u3XtD7m7HiIJvj05hyWwfC/D0v6FgRERERkYuhgC4iF61h6Gmzuv+4nQPHs8nJPy1055cO01l5BeTYzhWmL43ZBD7ubvh4uOHtYcHbaiE3M426EaH4ebnj427B290NHw/j2tej5H0fDwsJaXk8/u1m1sWlcs07v/PB7R1pGxVYYTWLiIiIiIACuohcotOHuk/5de8FHfv3MO3j7oa3u8W4727B18OtVHj2+dt9I2SfOs7DzVxiqL3NZmP+/PkMHtweq9Va5tpa1PFn7Kdr2J2UybAPVvDSDa24qUPdC3p/IiIiIiIXQgFdRC6JxWzinVva8+Fv+zCbTJcUpquS6BAfvv3nZYz/agOLtiXy0JyNbDuSzuODY3GzaH5NERERESl/CugicsnqBHrxzLUtnF1GufP1cGPKbR14c8lu3lyym2l/7GdnYjrvjGhPkI+7s8sTERERkRpG3UAiIudgNpt4oH8TptzWAW93C3/sOc617/7OjoR0Z5cmIiIiIjWMArqISBkMahnBt/ddRr1gbw6l5HDje3/y0+ajzi5LRERERGoQBXQRkTJqGuHH9+Muo0fjELLzC/nHF+uYtGgndrvD2aWJiIiISA2ggC4icgECvd35ZHQn7rq8AQBv/bKHuz9bS0auzcmViYiIiEh1p4AuInKB3Cxmnry6Oa8PbYO7m5mftydyw3t/sicp09mliYiIiEg1poAuInKRhnSoy5x7uhHh78mepEyufns5n604gMOhIe8iIiIicuEU0EVELkGbqEC+/5dxXnquzc5//reVUZ+sJjE919mliYiIiEg1o4AuInKJwvw8mTGmM89c0xwPNzO/7TrGwMm/MV+zvIuIiIjIBVBAFxEpB2azidGXNeDHf19Oy0h/UrNt3PfFOsbP3kC6JpATERERkTJQQBcRKUeNwvyY+4/L+NcVjTCbYO66eK6cvJy/9h13dmkiIiIiUsUpoIuIlDN3NzMPDmjKnHu7U7+WN/GpOYz46C9emr+dvIJCZ5cnIiIiIlWUArqISAXpUD+I+f/uwYjOUTgc8OFv+7junT/YfjTd2aWJiIiISBXk5uwCRERqMh8PNybe2Jq+seE8NncTOxIyuO6dP3hwQBPu6hGDxWwqdYzD4SArv5CMXBsZuQWk55y8zrVhtZhpFRlA3SAvTKbSx4qIiIhI9aWALiJSCfo1D2dBvZ489s1mft6eyMSfdvDj5qOE+3uWCOAZuQVk5hVQaD/3Wuohvh60qxdIu3qBtI0KpE3dQHw89E+6iIiISHWmv+ZERCpJiK8HH43swOw1h3j2h21sOpwGpJ11fzezCT9PN/w8rfh7ueHnYSUrv4BtR9JJzsxj8bZEFm9LBMBsgibhfrSrF0S7KCO4Nwz1xXyGHnoRERERqZoU0EVEKpHJZGJ4p3p0bxjCz9sTcXcz4+9pLQ7iAV7GtZ+nG15WyxmHsefaCtl6JI31camsj0tlw6FU4lNz2JGQwY6EDL5cFQeAn4cbbU6GdaOnPYhgH/fKfssiIiIiUkYK6CIiThAV7M2Yyxpc1LGeVgsd6gfToX5w8bbE9FwjsB86wYa4VDYdTiMjr4Df9yTz+57k4v3q1/I+2cMeRLt6gcRG+OPupvlCRURERKoCBXQRkRog3N+TQS0jGNQyAoCCQjs7EzPYcCj1ZE/7CfYey+Lg8WwOHs/muw1HAGNJuFaRAcWhvW29QOoEeGoCOhEREREnUEAXEamB3CxmWtQJoEWdAG7tUh+AtGwbGw+nFve0r49LJS3HxtqDJ1h78ASwH4Awv6IJ6IJoGxVI67oBeLvrvwsRERGRiqa/uEREXESAt5WeTULp2SQUMJZz25+cdaqX/dAJth/NICkjj4VbE1m41ZiAzmI20TTcr3jG+Hb1gogJ8dEEdCIiIiLlTAFdRMRFmUwmYkJ9iQn15cb2dQHIyS9ky5E01sedKJ6A7mhaLtuOprPtaDpfrDQmoPP3LJqAzjiXvVm4D5k22J+cRZbNQVqOjbQcG+knr0tfCsgvKKRN3UC6NqxF94a1qBvk7czmEBEREXE6BXQRESnm5W6hU3QwnaJPTUB3NC2HDXGprD+UakxAF59Kem4By3cns3x38mlHu8GaPy7o9fYey2Lu+ngA6gV70y2mFt0b1aJbTC3C/D3L4y2JiIiIVBsK6CIick61A7yo3cqLK1vVBsBWaGdnQgbrDxmTz22IS2VfchYAvh5uBHhZS1+8jWv/07bZHQ5W709hxb7jbDqcRlxKNnEp2Xy15hAADUN96N4whG4Na9E1ppaWiBMREZEaTwFdREQuiNVipmVkAC0jA7i9qzEBXXpWLj8vXsg1Vw3AarWW+bn6NA0DICPXxpoDJ/hzbzIr9h1n65F09h7LYu+xLD776yAAsRF+9GsWzv39GmO1aGk4ERERqXkU0EVE5JJ5uVuwXMKccX6eVvrEhtEn1gjsqdn5rNyfwoq9x1mx9zg7EzPYkWBc2kYF0q95eDlVLiIiIlJ1KKCLiEiVE+jtzsAWEQxsYazrnpyZx2PfbOLn7UlsOpyqgC4iIiI1ksYIiohIlRfi61G8PNym+DQnVyMiIiJSMRTQRUSkWmgVGQDA5sNpOBwOJ1cjIiIiUv4U0EVEpFpoVtsfN7OJ41n5HE3LdXY5IiIiIuVOAV1ERKoFT6uFxuF+AGw6rGHuIiIiUvMooIuISLXRumiYe3yqcwsRERERqQAK6CIiUm20qlsU0NOdXImIiIhI+VNAFxGRauPURHGpmihOREREahwFdCdJSUnh1ltvxd/fn8DAQO68804yMzPPuf+//vUvmjZtipeXF/Xq1ePf//43aWk6D1NEXEdsbT+sFhMnsm0cPpHj7HJEREREypUCupPceuutbN26lcWLFzNv3jx+++037r777rPuf+TIEY4cOcJrr73Gli1bmD59OgsWLODOO++sxKpFRJzLw81C0whjorgtWg9dREREahg3ZxfgirZv386CBQtYvXo1HTt2BODtt99m8ODBvPbaa9SpU6fUMS1btuSbb74pvt+wYUNefPFFbrvtNgoKCnBz00cpIq6hVWQAW+LT2RSfxpWtaju7HBGpQX7YdJRv95s5+Os+agd6ExHgSbi/J+F+nvh7uWEymZxdoojUcEp1TrBixQoCAwOLwzlAv379MJvNrFy5khtuuKFMz5OWloa/v/85w3leXh55eXnF99PTjYmVbDYbNpvtrMcVPXaufeTSqI0rh9q5clRmOzc/2YO+6VCqPtcqQr9nFU9tXPGOpuXy0NebsTvMLE/YU+pxDzczYX4ehPt7EOZ38uLvQZifJy1q+9Ew1EcBvoxq8s9zQaGd9NwC0nNtpOUUkJZjIy3HRnqOjdScAtJzbKTl2gjz9WB8/8bOLrdGfgbVnQK6EyQkJBAWFlZim5ubG8HBwSQkJJTpOZKTk3n++efPOSweYOLEiTz77LOlti9atAhvb+/zvs7ixYvLVI9cPLVx5VA7V47KaOe0TAA31h9I5scf56O/h6sO/Z5VPLVxxVl42ITdYSHM00G0n4P0fEjLN5Fmg+wCE3kFdg6dyOHQWea/CPN00CrYQetgO/V8wax/m86ruv88J+XA4ngzR7JNZBdAdgHkFpbtg6/t5SDWtruCKzy/7OxsZ5cgf6OAXo4ee+wxXnnllXPus3379kt+nfT0dK666iqaN2/OM888c859J0yYwPjx40scGxUVxYABA/D39z/rcTabjcWLF9O/f3+sVusl1yylqY0rh9q5clRmO+cX2Hlz2xKyC6FVt97UCz7/l41SsfR7VvHUxhXLbnfw6hvLgVwG1LXz+C39SrRznq2QpMw8ktLzSMrIIzHj1O0jablsOJRKUi4sOWJiyREz4f4e9IsNo3/zMDpHB2G1aNqn01X3n+ejabm8u2wvX286QqH9zCuK+Lhb8PeyEuBlJcDLDX9PK4HeVvw93QjwslInwJPBbUuf1lrZikbXStWhgF6OHnzwQUaPHn3OfWJiYoiIiCApKanE9oKCAlJSUoiIiDjn8RkZGQwaNAg/Pz++/fbb8/6j5uHhgYeHR6ntVqu1TP8glnU/uXhq48qhdq4cldHOVis0q+3PpsNp7EjMpmF4QIW+npSdfs8qntq4Yvy26xjxqbn4e7rRJrigVDtbrVZ8vT2JCTvz8Rm5NpbuPMbCrQks25FEYnoeX6w6xBerDhHgZaVvbBgDWkTQq0koXu6WSnpXVV91+3lOycrn/WV7mLHiIPkFdgCuiA1jROd61PJ1PxnGjUt1+VKmOrW/q1BAL0ehoaGEhoaed79u3bqRmprK2rVr6dChAwC//PILdrudLl26nPW49PR0Bg4ciIeHB99//z2enp7lVruISHXSMjKATYfT2BSfylWtNVGciFyaWavjALiubR3cTfsu+Hg/TyvXtqnDtW3qkGsr5M+9ySzcksjP2xM5npXP3PXxzF0fj6fVTM/GoQxsEUHfZmEEeruX91uRCpCZV8DU5fv5aPk+MvMKAOgcHcwjg5rSMTrYydVJTaOA7gTNmjVj0KBBjB07lilTpmCz2Rg3bhw333xz8Qzu8fHx9O3bl08//ZTOnTuTnp7OgAEDyM7O5vPPPyc9Pb14SEpoaCgWi76NFRHX0ToygJnA5sNaak1ELk1yZh6LtyUCMKxDJPvWXXhAP52n1cIVseFcERtOod3BmgMpLNyayMKtCcSn5rBoWyKLtiViMZvoGhPMwBYRDGgeQUSAOl6qmlxbIV+sjOPdpXtIycoHoHltfx4e1JTeTUI1KaBUCAV0J/niiy8YN24cffv2xWw2M2TIEN56663ix202Gzt37iyeuGHdunWsXLkSgEaNGpV4rv379xMdHV1ptYuIOFurusaw9s3xaTgcDv2RJCIXbe66w9gKHbSJCiQ2wo9Li+clWcwmusTUoktMLf5zdTO2Hkln0dYEFm5NZGdiBn/sOc4fe47z1P+20iYqkIEtwhnYIoKGob7lWIVcqIJCO3PXxTP5510cScsFoEGID+P7N+GqVrUxawZAqUAK6E4SHBzMzJkzz/p4dHQ0DsepSSd69+5d4r6IiCtrEu6Hu5uZjNwCDh7PJjrEx9kliUg15HA4mLX6EAAjOkVV6GuZTCZaRgbQMjKA8QOaciA5i4VbE1i4NYF1calsPGRcXl2wk0ZhvsVhvVVkQI35ErKg0M7sNYeZ9vs+GrqbubKK/W3rcDj4aUsCry/ayd5jWQBE+Htyf7/G3NShbrU5r1yqNwV0ERGpdqwWM81q+7PxUCqb49MU0EXkoqzan8K+Y1l4u1u4uk0doPICY3SID/f0asg9vRqSlJ7Lom3GMPgVe4+zJymTPUmZvLt0L3UCPBnQIoIBLcLpHB2MWzUMiQ6Hg2U7j/HS/O3sTsoEYA9mPv7jAPf1aeLk6oz6lu9O5r8Ld7I53jh1Ksjbyj/7NOK2rvXxtOpUUqk8CugiIlIttY4MKA7o17Rx/lI1IlL9fHWy9/zaNnXw9XDDZrM5pY4wf09u61qf27rWJy3HxtIdScaM8DuPcSQtl+l/HmD6nwcI8rbSt5nRs96jcUi1CI5b4tN4af52/tx7HIBAbys9GtXih00JvLpwN5FBPlzXNtJp9a2LO8GrC3bw174UwFge7a4eMdzVowF+nprhXCqfArqIiFRLrSKN89A3HU51biEiUi2lZdv4cfNRAG7uXM/J1ZwS4GXl+naRXN8uklxbIct3J7NwawI/b0/kRLaNr9ce5uu1h/F2t9CriTEjfJ/YMAK8qlaYPJKaw2uLdvLt+ngcDnC3mBlzWTT39WmEtxukHzvCr0fNPDRnI6G+HnRvFFKp9e1MyOC/C3fy83ZjgkB3i5nbu9Xnvt4NqeVbeolikcqigC4iItVS0URxW+PTsdsdmrRHRC7IdxviySuwExvhR5uT/55UNZ5WC/2bh9O/eTgFhXZWHUhh0dZEFm1N4EhaLj9tSeCnLQlYLSa6xtQ6OSN8OGH+zpsRPiPXxvvL9jL19/3knVwr/Lq2dXhoQFOigr0BYzLk6+vb8alVm/lbErnns7XMvrcbzWr7V3h9ccezeePnXXy3wfjiwGyCmzrU5f5+TYgM9Krw1xc5HwV0ERGplhqH+eLhZiYjr4ADx7OI0azHIlJGDoeDL1cZa5/f3CmqWkzC5mYx071hCN0bhvD0Nc3ZHJ92cpK5RPYkZbJ8dzLLdyfzn/9toV1UIANbRDCwRUSlzdFhK7Qza1Uck3/ezfGTS5J1bhDME4Ob0SYqsNT+ZhO8emNLkrNsrNqfwuhPVjH3vssqLCQnpefy9i97mLU6DluhMdfA4FYRjO/flEZh+v9Dqg4FdBERqZbcLGaa1/FnfZxxHroCuoiU1abDaexIyMDdzcwN7eo6u5wLZjKZaF03kNZ1A3l4YCx7j2UWh/WNh1JZF2dcJv60g6bhfgxsEc6AFhG0qONf7l9GOBwOFm9L5OUFO9h3cubzmBAfHrsylv7Nw8/5eh5WCx/d3pGhH/zJrsRMRk9bxdf3difAu/yG66dl25jy214++WM/uTajR79H4xAeGRhbPBJLpCpRQBcRkWqrdWSAEdAPpzl1kiERqV5mrTZ6zwe3jCjXMOgsDUN9ua93I+7r3YiEtFwWbTOWb/trXwo7EzPYmZjBW7/soW6QFwOaRzCwRTgdo4OxXMSpQQ6Hg8MncthwKJUNh1JZuf84W+LTAQj2cef/+jVmROd6ZV6SLMDbyvQxnbnhvT/YnZTJ2M/W8OkdnS95Arzs/AI++eMAH/y6l/TcAgDa1QvkkYGxdGtY65KeW6QiKaCLiEi11apuIHCQTSeXxREROZ+svAK+33AEqFqTw5WXiABPRnaLZmS3aFKz81my3ZgR/rfdxzh8Iodpf+xn2h/7qeXjTr9m4QxsGU73hmefET4t28bGw6nFgXzjodTiIexFPNzM3Hl5A+7t3RD/i5j5vE6gF9PHdGbYlBWs2p/Cg7M38vaIdhc1t0h+gZ1Zq+N4a8kekjPzAGga7sdDA5vSr1lYtTidQVybArqIiFRbRTO5b41P00RxIlIm8zYdISu/kAYhPnRpEOzscipUoLc7QzrUZUiHuuTkF/LrrmMsOjkj/PGsfL5ac4iv1hzCx91C79gwBraIICrIi02H04rD+L7krFLPa7WYaFbbn7ZRgbSpG8jljUMIv8SJ6ZrV9ueDkR0YNW0VP24+Sri/J09d07zMxxfaHfxvQzxv/LyLQyk5AEQFezG+fxOubRN5UaMFRJxBAV1ERKqthqE+eFktZOUXsi85SxP9iMh5fbnKWPt8eDWZHK68eLlbGNQygkEtI7AV2lm5L4WFWxNYtC2BxPQ8ftx0lB83HT3jsdG1vGkTFWgE8qhAmtf2r5A12Ls3DOG1oW24f9YGpv2xnzqBntzVI+acxxSdA//6ol3sTMwAINTPg39f0Yjhnerh7la2ofYiVYUCuoiIVFtFE8WtPXiCzfGpCugick47EtLZcCgVN7OJIe2r3+Rw5cVqMXN54xAubxzCs9e2YOPhVBZuTWTxtgTScmy0igw4FcjrBhLk415ptV3XNpKEtFwm/rSDF37cTri/J9e0qXPGff/cm8x/F+5kfVwqAP6ebtzbuyGju0fj7a6YI9WTfnJFRKRaaxUZYAT0w+nc0M7Z1YhUDQWF9uLgclXr2lzXtg6B3pUXsqqqWSd7z/s3DyfUz8PJ1VQNZrOJdvWCaFcviMeujHV2OQDc3TOGo2m5TP/zAA/O3kiIr0eJid02HU7lvwt3snx3MgBeVgtjLovmnp4Na8Skf+LaFNBFRKRaa31ymZzN8anOLUSkisjMK+BfM9exdOcxAFYdSOHFH7czoEU4wzpGcVmjEJc8HzfXVsi36+MBY3i7VF0mk4n/XN2cxPRcftqSwN2freHre7tjMZt4fdFOftqSABjnwo/oXI9xVzQizO/SzoEXqSoU0EVEpFormihuS3w6hXaHSwYPkSIJabncMX01246m42k1M7p7A37ddYztR9OZt+ko8zYdpU6AJzd1qMtNHaKoV8vb2SVXmoVbjeHbkYFe9Ggc6uxy5DwsZhNvDG9LcuZKVh84wdApf5KZV4DdASYT3NA2kgf6NyEq2HV+hsU1KKCLiEi1FhPqi7e7hez8QvYdy6RxuJ+zSxJxim1H0rlj+moS0nMJ8XXn41GdaBsVyGNXxrIlPo05aw7x3YYjHEnL5a1f9vDWL3voFlOLYZ3qMqhFbbzcy3/Sr6rky1XG2udDO9bVF3nVhKfVwkcjO3LTlBXsScoEjNMTHhrQlKYR+rdeaiYFdBERqdYsZhMt6wSw6kAKmw6nKaCLS1q2M4l/frGOrPxCGoX58snoTiV6FltGBtAyMoAJg5uxaFsic9Yc4vc9yazYd5wV+47zlMdWrmlbh2Edo2hTN6DGzW6+PzmLv/alYDbBsI4a3l6dBHq788VdXfhiZRy9m4bSvl6Qs0sSqVAK6CIiUu21jDQC+ub4NIZ0cN2ZmcU1zVwZx3/+t4VCu4NuMbWYcluHs06U5Wm1cG2bOlzbpg6HT2Tzzdp45qw9xOETOcxcGcfMlXE0CfdlWMcobmgXSS3fmjGR2lerjcnhejUJpU6gl5OrkQsV7u/J+P5NnF2GSKVQQBcRkWrv1ERxaU6uxLkKCu0cSc3lwPEsDh7P4sDxbGoHeDKqezRWi9YCrmnsdgevLNzBB7/uA+DG9pG8fGPrMq/7XDfIm/v7NeZfVzTir33Hmb3mED9tSWBXYiYv/Lidl3/aQb9m4QztWJdeTUJxq6Y/Q7ZCO1+vPQzA8E71nFyNiMi5KaCLiEi11+pkQN96JI2CQnu1DRJlYSu0c/hEjhHCk40QXhTGD6VkU2B3lDpmxd7jvHNL+xp/jrErybUV8uDsjfy4+SgAD/Rrwr/7Nrqooelms4nujULo3iiEZ3Ns/LDxCHPWHGLj4TQWbE1gwdYEwvw8GNKhLkM71CUm1Le8306FWrI9keTMPEJ8PejbLMzZ5YiInJMCuoiIVHsNavng6+FGZl4Be45lEhvh7+ySylVGro33l+3lx81HOXwih8IzhPAiHm5m6tfypn4tHyL8PZm95hBLdiQxctpKPh7ViQAvrRFc3R3PzGPsp2tYF5eK1WLilSGtubF9+ZzaEeBl5bau9bmta312JKQzZ81hvl0fT1JGHu8v28v7y/bSKTqIoR2juKpVbXw8qv6fkrNODm8f2rGuRpKISJVX9f9VFREROQ+z2USLOv6s3J/C5sNpNSagF9odfL32EP9duIvkzLzi7Z5WM9G1fIiu5UP9EG/jupZxHeHvifm0GaqvbVuHO6avZvWBEwz/YAWf3tGZMH+tF1xd7U/OYuzn6zl4PBt/Tzc+uL0j3RrWqpDXio3w5z9XN+fRQbH8siOR2WsOs2xnEqsPnGD1gRM88/1Wrm5dm2Edo+hQP6hKTiwXn5rDr7uM9eCHa3I4EakGFNBFRKRGaF03wAjo8WkMrQF/iP+17zjP/bCNbUfTAWgQ4sNDA5rSMTqIMD+PMoehTtHBzL6nGyOnrWJHQgY3TVnBZ3d2pn4tn4osXyrA3nR4+sNVpObYiAr24pPRnWgUVvGrFri7mRnUsjaDWtYmMT2Xb9YdZs6aw+xPzmL2msPMXnOYmBAfhnaMYkj7yCr1BdDs1YdwOKBbTC2iQ/QzLyJVnwK6iIjUCC0ja8ZEcXHHs3lp/nYWbE0AwN/Tjfv7NeH2rvXLPPnX3zWr7c8393bn9mkrOXg8myHvr2DGHZ1oUSegPEuXCpKcmcenf+7n3W0WCh022kYF8vGojoQ4YYb1cH9P7uvdiH/0asiagyeYvfoQP24+yr7kLF5ZsIPXFu2kd5NQhnaM4orYsIv+mS0PhXYHc9YYw9tv7lz9v7QTEdeggC4iIjVC67qBAGw7ko6t0F7tzjXNyLXx7tK9TPt9P/mFdswmuLVLfR7o34RgH/dLfv56tbyZc283Rk1bzfaj6dz8wV98PKojXWIqZni0XLot8WlM//MA3288Qn6BHTAxoHkYb97s/An/TCYTnaKD6RQdzNPXtmD+pqPMXnOINQdPsGRHEkt2JFHLx50b2kUyrFMUTcIrvqf/737bfYwjabkEelsZ2CKi0l9fRORiKKCLiEiNUD/YGz8PNzLyCtidmEnzOtXjPPSiXr7XFu0kOTMfgB6NQ3jyquY0jSjfUBPm58msu7sydsYaVh1IYeS0VbxzS3v6Nw+/6OcstDvYEp9Oci44HGefvE7KpqDQzqJtiUz/4wCrDqQUb28d6U8brxM8ObwNHlVsNn5fDzeGdYpiWKco9h7LZM6aw3yz7jDHMvL4+Pf9fPz7ftpEBTKsY12uaVMHf8+Kn6gwv8DOlGV7AbihXSSe1qrVZiIiZ6OALiIiNYLZbKJlZAAr9h1nS3xatQjoK/Ye5/l5p84zjwnx4YmrmnFFbFiFTbgV4GXl0zs7M27men7ensi9n6/l5RtbXdB5+3a7g/WHTvDDxqP8uPkoxzLyADfe2bmMVnUDaVM3gFaRAbSJCiS8Cp2PXJWlZufz5apDfLbiAEfScgFwM5sY3Ko2Yy6LpmVtX+bPn19iAsCqqGGoL49dGctDA5rw665jxioC25PYeCiVjYdSee6HbQxuVZuhHevSLaZWhfycFxTaeeCrDazcn4KHm5nbu9Yv99cQEakoCugiIlJjtK5rBPRN8akM61R1zzk9eDyLifN3lOt55hfC02phym3teWzuZr5ee5iHv95ESlY+9/RqeNZjHA6jp/yHTUf4cdNR4lNzih/z8bCQm1/AiWwbv+06xm8nZ80GCPf3oFWkEdpbRwXSOjKAoHIYsl9T7EzIYPqf+/l2fTy5NjsAtXzcuaVLPW7rWr/4Cw6bzebMMi+Ym8VM32bh9G0WTnJmHt+tj+er1YfYnZTJt+vj+XZ9PP2bh/PWze3Kdbi+3e7gkW828ePmo1gtJj64vUO1W7ddRFybArqIiNQYxRPFHa6aE8Vl5Np4Z+kePvn9APmFdixmE7d2qcf/9Suf88wvhJvFzH9vak2wjzsf/raPiT/tICUrn8eujC3Rq7krMYMfNh7hh41HOHA8u3i7j7uFAS0iuKZNbbrUD2TBggVEt72MbQmZbDqcxqbDaexOyiAxPY/E9ER+3p5YfGxUsBdt6gYyvFMUlzcKqZLLc1Uku93Bkh1JfPLHfv7ce7x4e/Pa/oy5LJpr2tSpUUOyQ3w9uKtHDHde3oCNh9OYveYQX689zOJtidz68V9MHdWpXL60cTgcPPm/LcxdF4/FbOKdW9rTu2lYObwDEZHKo4AuIiI1Ruu6RkDfnpBBfoHdqTNIn67Q7mD2mkO8/rfzzP9zdXOnTJ5VxGQy8fjgZtTycWfiTzv44Ld9xT3pP20+yg+bjrArMbN4f0+rmb6x4VzTpja9m4YVh0ibzYab2Wj/Dg1CivfPzi9g65F0Nh5KZXO8Edr3J2dxKCWHQyk5zNt0lPb1Arm/XxN6NnaNoP7n3mQmzt9RvNqAxWxiYItwxlzWgI5VdC3x8mIymWgbFUjbqEBuaBfJXTPWsC4ulSFT/mTGmM5EBXtf9HM7HA6en7edmSvjMJngjeFtNTGciFRLCugiIlJj1Av2xt/TjfTcAnYlZhT3qDvTn3uTee6HbexIyACM88yfvLoZfZpW3HnmF+qeXg0J8nHnsW82MWftYeasPVz8mNVioleTMK5pU5t+zcLx8Sj7nw7e7m7FM30XScuxsSU+jcXbEvlyVRzr4lIZNW0VbaMCub9fY3o3Ca0y7VKediZk8PJP21m60xj+7+vhxm1d63N7t/pEBno5ubrK1yk6mK/v7caoaavYdyyLG9//k+ljLn7pv9cX7eL/27vzuKjq/X/gL/Z9VUBA9s0dUlvEXVAJ9ZK3ay6kYn5V1BI1MrUF+Xrdiswy/WWZSF3QtC/p45ZpBlKK3kQkWUJEAU0vuLOJIgyf3x/ExAgDg8LMwLyejweP6pzP+ZzPeR0ce8/M53N2pRYCADa9OAB/83Voz+ESESkNC3QiIuoytLS00L+nBVIv3kbWtTKVFuiXb9/D+kO5OJJT/9Vuc0NdLA30xswhLmr5CLiXBjvB0kgPr+3JQG2dwFDP7pg4wB7j+/SAhXH7rbptYaSHoZ7dMdSzOxaN8sCOXwoQ/+tl/PZHKebEpsHXyRJLA7wwyqdrFOolZQ/w4dEL2J/+B+pE/cJvLz/ngtfGeKKbCp5jrk687MyQuGgowmJP43xJBabu+A92zByEoZ7dWz+4kW3HLuKTYxcBAGtD+uKlNix4SESkbligExFRl9Lf0VJaoE9XwfnLH9RgW/JFxKbKzjNfFuit9oujjevbAyfeHANdbS2ljNXW3BDvTOyD8JEe+OyXS/jqP5dx7o9SzNmdhgE9LRAR4NWhK9p3pIoHNdjxcwF2niiQLv4W3L8HVozvBdfuJioenfroYWGIrxcMwYKvzuA/BXcQFnsaMVN8EeLnqNDxX5woxPtH8gAAq4N7YeYQ1w4cLRFRx2OBTkREXUrDPHRlLxQnqRP4Oq1+nvnte+ozz7ytbMyU/6mujZkB3prQB/NHeODz4wX46tRlZF4tw9y4M+jvaIElAV4I7N05CvUaSR0Sfr2Cj5LycefP34PBLlZYPaE3BjpbqXh06snCSA9xrzyD5fvO4fvMYkTs/Q3Xyx9g3nD3Fu95/K+Xsfa73wEAywK9MX+E/KcQEBF1FizQiYioS+n/59faz5eUo7pWAgPdjl8Nu8k8cxsTvDOhT5f5mray2JgZYHVwb8wf4S4t1LOulWHel2fQ18EcEQFeGNvHTi0zFULgcHYJ3juSh8Jb9wDU/x6sDOqltmNWJwa6Otg67SnYmRliV2oh1h86j5Kyarw9oXezz35PPHsVbx/IBgCEj/TAkgBPZQ+ZiKhDsEAnIqIupaeVESyN9VBaVYMLJZXo37Pj5qEX3aqfZ/7j751jnnln0d3UAKue740Ff36i/uXJIuT8txzzv0pHH3tzLAnwwrg+ds0WbqpwpugO1h3KRcaVUgBAd1N9LA30xrSnnaDL3wOFaWtr4d1JfWBvYYh1h3KxK7UQ1yseYPNLvjJvtH2fWYzI/ecgBBDm74o3g3z4BggRdRks0ImIqEvR0tJCf0cLHM+/hcxrpR1SoJc/qMEnyRcRm1qIGomAjrYWXv7zeebqPs+8M7E20cebQb0wb7g7dh4vQNzJIvxeXI7wf6WjVw8zRAR4YXzfHiot1HceL8A/v88FABjp6WD+CHfMG+EO0zasdk+y5o1wh625ASL313/l/XZlNXbMHAwLIz0k5V5HxN4M1Alg6mAnvDuxD4tzIupS+LcHERF1OQ0Feva19p2H3tw88xHeNnhnQm94daJ55p2NtYk+VvxZqH9xohC7TxbhfEkFFsafRa8eZlgS4IUgJRfqQghsPHweO34uAAC8OLAn3gzyga25odLG0JWF+Dmiu6kBFnyVjv8U3MHUHacwf4Q7Vv5fFmrrBEL8HLD+7/3V5lsURETthQU6ERF1OQ0LxWW240JxJy/ewv9+99c8cw8bE7w9sQ9G+9i22zmoZVYm+ogc74P/Ge6GXScKEZtaX6gvij8LHzszvBbgieB+9h1etNVK6rAqMUv6vPiVz/fCghEtL2hGbTfUszu+XvAcwmLTcL6kAsv3nQMABPXtgQ+m+EKHxTkRdUEs0ImIqMtpeP55XkkFHtRIYKj3+AvFFf45z/zon/PMLYz0sDTQCy8/x3nmqmJprI/l43wwd5g7vkgtRGxqIfKuV+DVhAx42ebjtQAvTOhv3yEF3IMaCV5NyMBPudehrQVs/PsAvPQ0n7vdUfo6WCBxoT9mx55Gwc17GOVjg4+nP8W5/UTUZbFAJyKiLsfR0gjWJvq4c+8h8koq4Otk2eY+yu7X4JPkfOw+WcR55mrKwlgPy8d6Y+4wN8SmFmLXiULk36jEkj0Z+DgpH6+N8cTEAQ7tVqiXVdXgf75MQ1rRXRjoauOTGQMxto9du/RN8jlZG+PA4qFIL7qLoZ7doa/L4pyIui4W6ERE1OU0LBT384WbyLxW1qYCvVZSh6/P/IEPfrwgfY71SG8bvM155mqr/lsN3nhlmBt2pxZh5/ECXLxRiYi9v+GjpHwsGeOFSb5PVqhfL3+A2btO43xJBcwMdfHF7KfxjJt1O14FtcTcUA+je3E6CRF1fXwLUkXu3LmD0NBQmJubw9LSEnPnzkVlZWWLxyxYsAAeHh4wMjKCjY0NQkJCcP78eSWNmIioc2l4HnrW1dJm99dI6lB46x5S8m4g7mQR/vffv2Pu7jSMfD8Fb32bjTv3HsLDxgSxc55G3CvPsDjvBMwN9bAkwAsnVo7B62O9YWGkh4Kb97D0698wdvPPSDx7FbWSujb3W3CzEi/+v5M4X1IBWzMD7FswhMU5ERF1CH6CriKhoaEoLi7G0aNHUVNTgzlz5mD+/PlISEiQe8ygQYMQGhoKZ2dn3LlzB2vWrMG4ceNQWFgIHZ3Hn19JRNQVNTxeLf3yXfyYU4LLt6tw+c69+n/ersK10vuQ1Ilmj7Uw0sOyQC+Ecp55p2RuqIfXArwQNtQVX566jM+PF6Dg1j0s33cOW5MvYvFoT7zg56DQPOasq2UIiz2N2/cewq27Cb585Rk4WRsr4SqIiEgTsUBXgdzcXBw+fBhpaWkYPHgwAGDr1q0IDg5GTEwMHBwcmj1u/vz50n93dXXFP//5T/j6+qKoqAgeHh5KGTsRUWfRsJL7pZv3MP+r9GbbGOppw9naGC7dTOBibQyX7vX/9HO2hLmhnjKHSx3AzFAPi0d7Yra/K748VYTPfylA4a17iNx/DluT8/HqaE9MfspRbqGeevEW5n95BvceStDP0Ry75zyD7qYGSr4KIiLSJCzQVeDUqVOwtLSUFucAEBgYCG1tbfz666+YPHlyq33cu3cPsbGxcHNzg5MTV48lInpUD3NDjO1jh18LbsOlmwmcuxnDtZsxXKxN4NKtvii3NTPgc5Q1gKmBLhaN8sTsIX99on75dhXe+CYTW5Mv1hfqAx1lvi3xfWYxln39Gx5K6uDv0Q07Zg6CGd+0ISKiDsYCXQVKSkpgayu70Imuri6sra1RUlLS4rHbt2/HihUrcO/ePfj4+ODo0aPQ15e/mnB1dTWqq6ul/11eXg4AqKmpQU1NjdzjGva11IaeDDNWDuasHOqa8/bpvi3ul0hqIZEoaTBdmLre/0fpawP/M9QZ0wc7ICHtD+w8UYQrd6qw4v8y8XFyPhaOcMPkpxywL/0aor/LhRDA833t8P4/+sNAR7XX11ky7uyYs3IwZ/XBe6B+tIQQzU/AozZbuXIlNm3a1GKb3NxcJCYmIi4uDnl5eTL7bG1tER0djYULF8o9vqysDDdu3EBxcTFiYmJw7do1pKamwtDQsNn2a9asQXR0dJPtCQkJMDbmHDoiItJc1RIg9boWkv6rjcqa+m9SmOkJVPz570Pt6vAPtzrwSxZE1FVVVVVhxowZKCsrg7m5uaqHQ2CB3q5u3ryJ27dvt9jG3d0d//rXv/D666/j7t270u21tbUwNDTE/v37FfqKOwA8fPgQVlZW2LlzJ6ZPn95sm+Y+QXdycsKtW7da/ENYU1ODo0ePYuzYsdDT41f6OgIzVg7mrBzMWbN19vt//6EEe9L+wOcninCrsv7RektGe+DV0e7Q0lKP6ryzZ9xZMGflYM7qo7y8HN27d2eBrkb4Ffd2ZGNjAxsbm1bbDRkyBKWlpUhPT8egQYMAAMnJyairq8Ozzz6r8PmEEBBCyBTgjzIwMICBQdMFbfT09BR6QVS0HT0+ZqwczFk5mLNm66z3X09PDwtGeWGWvzsO/HYNVsb6COrXQ9XDalZnzbizYc7KwZxVj/mrHz47RgV69+6NoKAgzJs3D6dPn0ZqaipeffVVTJs2TbqC+7Vr19CrVy+cPn0aAFBQUIANGzYgPT0dV65cwcmTJzFlyhQYGRkhODhYlZdDRETUJRjp62D6M85qW5wTEVHXxwJdReLj49GrVy8EBAQgODgYw4YNw2effSbdX1NTg7y8PFRVVQEADA0Ncfz4cQQHB8PT0xNTp06FmZkZTp482WTBOSIiIiIiIup8+BV3FbG2tkZCQoLc/a6urmi8PICDgwMOHTqkjKERERERERGRCvATdCIiIiIiIiI1wAKdiIiIiIiISA2wQCciIiIiIiJSAyzQiYiIiIiIiNQAC3QiIiIiIiIiNcACnYiIiIiIiEgNsEAnIiIiIiIiUgMs0ImIiIiIiIjUAAt0IiIiIiIiIjXAAp2IiIiIiIhIDbBAJyIiIiIiIlIDLNCJiIiIiIiI1ICuqgdAyiWEAACUl5e32K6mpgZVVVUoLy+Hnp6eMoamcZixcjBn5WDOmo33v+MxY+VgzsrBnNVHQ03QUCOQ6rFA1zAVFRUAACcnJxWPhIiIiIiI1EFFRQUsLCxUPQwCoCX4dolGqaurw3//+1+YmZlBS0tLbrvy8nI4OTnhjz/+gLm5uRJHqDmYsXIwZ+VgzpqN97/jMWPlYM7KwZzVhxACFRUVcHBwgLY2Zz+rA36CrmG0tbXRs2dPhdubm5vzhbODMWPlYM7KwZw1G+9/x2PGysGclYM5qwd+cq5e+DYJERERERERkRpggU5ERERERESkBligU7MMDAwQFRUFAwMDVQ+ly2LGysGclYM5azbe/47HjJWDOSsHcyaSj4vEEREREREREakBfoJOREREREREpAZYoBMRERERERGpARboRERERERERGqABToRERERERGRGmCBrmIbNmzA008/DTMzM9ja2uKFF15AXl6eTJsHDx5g8eLF6NatG0xNTfHiiy/i+vXr0v3nzp3D9OnT4eTkBCMjI/Tu3RsfffSR3HOmpqZCV1cXfn5+rY5PCIF3330X9vb2MDIyQmBgIPLz82XarFu3Dv7+/jA2NoalpaXC156ZmYnhw4fD0NAQTk5OeO+992T25+Tk4MUXX4Srqyu0tLSwZcsWhftujBnLzxgAtmzZAh8fHxgZGcHJyQnLli3DgwcPFD5HA03N+cGDBwgLC0P//v2hq6uLF154oUmblJQUaGlpNfkpKSlR6ByNaWrOKSkpCAkJgb29PUxMTODn54f4+HiZNp9//jmGDx8OKysrWFlZITAwEKdPn1ao/86is9//oqIizJ07F25ubjAyMoKHhweioqLw8OHDVvtOSUnBwIEDYWBgAE9PT+zevVtm/y+//IJJkybBwcEBWlpaOHDgQKt9NocZy89YIpHgnXfekel77dq1eJz1hjU15+LiYsyYMQPe3t7Q1tbG0qVLm7TZvXt3k78vDA0NWx1zczQ158TERIwdOxY2NjYwNzfHkCFDcOTIkTZnQ6QSglRq/PjxIjY2VmRnZ4vffvtNBAcHC2dnZ1FZWSltEx4eLpycnERSUpI4c+aMeO6554S/v790/xdffCGWLFkiUlJSxKVLl8RXX30ljIyMxNatW5uc7+7du8Ld3V2MGzdO+Pr6tjq+jRs3CgsLC3HgwAFx7tw58be//U24ubmJ+/fvS9u8++67YvPmzWL58uXCwsJCoesuKysTdnZ2IjQ0VGRnZ4s9e/YIIyMjsWPHDmmb06dPi8jISLFnzx7Ro0cP8eGHHyrU96OYsfyM4+PjhYGBgYiPjxeFhYXiyJEjwt7eXixbtkyhczSmqTlXVlaK8PBw8dlnn4nx48eLkJCQJm2OHTsmAIi8vDxRXFws/ZFIJAqdozFNzXndunXi7bffFqmpqeLixYtiy5YtQltbW/z73/+WtpkxY4bYtm2byMjIELm5uSIsLExYWFiIq1evKnSOzqCz3/8ffvhBhIWFiSNHjohLly6JgwcPCltbW/H666+32G9BQYEwNjYWy5cvF7///rvYunWr0NHREYcPH5a2OXTokHjrrbdEYmKiACC+/fbbVsfbHGYsP+N169aJbt26ie+++04UFhaK/fv3C1NTU/HRRx+1Ou5HaWrOhYWFYsmSJSIuLk74+fmJiIiIJm1iY2OFubm5zN8XJSUlrY65OZqac0REhNi0aZM4ffq0uHDhgli1apXQ09MTZ8+ebVM2RKrAAl3N3LhxQwAQP//8sxBCiNLSUqGnpyf2798vbZObmysAiFOnTsntZ9GiRWL06NFNtk+dOlW8/fbbIioqqtUXzrq6OtGjRw/x/vvvS7eVlpYKAwMDsWfPnibtY2NjFf6f7e3btwsrKytRXV0t3fbmm28KHx+fZtu7uLg8doH+KGb8V8aLFy8WY8aMkTlu+fLlYujQoQqdoyWaknNjs2fPbrFAv3v3bpv7bI0m5twgODhYzJkzR+7+2tpaYWZmJuLi4h77HOquM9//Bu+9955wc3Nrse8VK1aIvn37Nhnb+PHjm23/JAX6o5jxXxlPmDBBvPLKKzJt/v73v4vQ0NAW+1aEpuTc2MiRI+UW6E/y2tgSTcy5QZ8+fUR0dLTc/Y9mQ6Qq/Iq7mikrKwMAWFtbAwDS09NRU1ODwMBAaZtevXrB2dkZp06darGfhj4axMbGoqCgAFFRUQqNpbCwECUlJTLntrCwwLPPPtviuRVx6tQpjBgxAvr6+tJt48ePR15eHu7evftEfbeGGf+Vsb+/P9LT06VfAy4oKMChQ4cQHBz8ROcGNCfntvDz84O9vT3Gjh2L1NTUdulTk3NubsyNVVVVoaampsU2nV1XuP+t3Ueg/vWscb9A/euZMv78MuO/+vX390dSUhIuXLgAoP6rzydOnMDzzz+v0Phboik5K6qyshIuLi5wcnJCSEgIcnJy2qVfTc25rq4OFRUVLR73aDZEqqKr6gHQX+rq6rB06VIMHToU/fr1AwCUlJRAX1+/yTxNOzs7ufNXT548ia+//hrff/+9dFt+fj5WrlyJ48ePQ1dXsdve0L+dnZ3C51ZUSUkJ3NzcmvTbsM/KyuqJ+peHGctmPGPGDNy6dQvDhg2DEAK1tbUIDw/H6tWrn+jcmpSzIuzt7fHpp59i8ODBqK6uxs6dOzFq1Cj8+uuvGDhw4GP3q8k579u3D2lpadixY4fcNm+++SYcHByaFB1dRVe4/xcvXsTWrVsRExPTat/N9VteXo779+/DyMhIoTG2FTOWzXjlypUoLy9Hr169oKOjA4lEgnXr1iE0NFSh8cujSTkrwsfHB7t27cKAAQNQVlaGmJgY+Pv7IycnBz179nzsfjU555iYGFRWVuKll15qdn9z2RCpCj9BVyOLFy9GdnY29u7d+9h9ZGdnIyQkBFFRURg3bhyA+kVdZsyYgejoaHh7ezd7XHx8PExNTaU/x48ff+wxPKpv377SftvjXfYnwYxlpaSkYP369di+fTvOnj2LxMREfP/991i7du0TjYc5y/Lx8cGCBQswaNAg+Pv7Y9euXfD398eHH374ROPR1JyPHTuGOXPm4PPPP0ffvn2b7WPjxo3Yu3cvvv3228deXEnddfb7f+3aNQQFBWHKlCmYN2+edHvjfsPDwx/vwtoJM5a1b98+xMfHIyEhAWfPnkVcXBxiYmIQFxfX5rE1xpxlDRkyBLNmzYKfnx9GjhyJxMRE2NjYtPiGpCI0NeeEhARER0dj3759sLW1bbbv9siGqN2o+jv2VG/x4sWiZ8+eoqCgQGZ7UlJSs3NXnZ2dxebNm2W25eTkCFtbW7F69WqZ7Xfv3hUAhI6OjvRHS0tLui0pKUmUl5eL/Px86U9VVZW4dOmSACAyMjJk+hsxYoRYsmRJk2uQN2eqqKhI2m/DYk0zZ85sMlc3OTlZABB37txp0kd7zEFnxk0zHjZsmIiMjJRp07D4y+MsYCaE5uXcmLw56M2JjIwUzz33nEJtm6OpOaekpAgTExOZxQ4f9f777wsLCwuRlpYmt01n19nv/7Vr14SXl5eYOXNmk9eaxv1ev35dCCHE8OHDm8zV3bVrlzA3N282H7TDHHRm3DTjnj17ik8++USmzdq1a+WuH6MITcu5MXlz0Jvzj3/8Q0ybNk2hts3R1JwbFsj97rvv2pwNkaqwQFexuro6sXjxYuHg4CAuXLjQZH/D4h3ffPONdNv58+ebLN6RnZ0tbG1txRtvvNGkD4lEIrKysmR+Fi5cKHx8fERWVpbc1SobFu+IiYmRbisrK2vXBcwePnwo3bZq1aoOWSSOGcvPeODAgWLFihUyxyUkJAgjIyNRW1ur0HkaX4sm5txYWwr0wMBAMXny5DafQ5NzPnbsmDAxMWlSIDS2adMmYW5u3uLiRp1ZV7j/V69eFV5eXmLatGkKv86sWLFC9OvXT2bb9OnTO2SROGb8l0cztra2Ftu3b5dps379euHl5aXQOR69Fk3MuTFFC/Ta2lrh4+PzWE9Y0eScExIShKGhoThw4IDc87eUDZGqsEBXsYULFwoLCwuRkpIi8ziNqqoqaZvw8HDh7OwskpOTxZkzZ8SQIUPEkCFDpPuzsrKEjY2NePnll2X6uHHjhtzzKrK6phD1j7+wtLQUBw8eFJmZmSIkJKTJI5MuX74sMjIyRHR0tDA1NRUZGRkiIyNDVFRUyO23tLRU2NnZiZkzZ4rs7Gyxd+9eYWxsLPOpWHV1tbQve3t7ERkZKTIyMkR+fn6r426MGcvPOCoqSpiZmYk9e/aIgoIC8eOPPwoPDw/x0ksvtTruR2lqzkLUf6qQkZEhJk2aJEaNGiU9rsGHH34oDhw4IPLz80VWVpaIiIgQ2tra4qeffmp13I/S1JyTk5OFsbGxWLVqlcyYb9++LXNufX198c0338i0ae3+dSad/f5fvXpVeHp6ioCAAHH16lWZ87ek4RFgb7zxhsjNzRXbtm1r8giwiooK6e8SALF582aRkZEhLl++3Oq4G2PG8jOePXu2cHR0lD5mLTExUXTv3r3JG72K0NSchRDS39NBgwaJGTNmiIyMDJGTkyPdHx0dLX2sWHp6upg2bZowNDSUaaMoTc05Pj5e6Orqim3btskcU1pa2qZsiFSBBbqKAWj2JzY2Vtrm/v37YtGiRcLKykoYGxuLyZMny7wwRUVFNduHi4uL3PMq+sJZV1cn3nnnHWFnZycMDAxEQECAyMvLk2kze/bsZs9/7NixFvs+d+6cGDZsmDAwMBCOjo5i48aNMvsLCwub7XfkyJGtjrsxZiw/45qaGrFmzRrh4eEhDA0NhZOTk1i0aNFjPQ5Mk3N2cXFp9rgGmzZtkmZsbW0tRo0aJZKTk1sdc3M0NWd5xzR+PZB3H6Kiolodd2fR2e9/bGys3GtozbFjx4Sfn5/Q19cX7u7uMtfcsL+5fmfPnt1q340xY/kZl5eXi4iICOHs7CwMDQ2Fu7u7eOutt2Qe56koTc65tTEvXbpUODs7C319fWFnZyeCg4Nlnt/dFpqa88iRI1t9PVAkGyJV0BJCCBARERERERGRSnEVdyIiIiIiIiI1wAKdiIiIiIiISA2wQCciIiIiIiJSAyzQiYiIiIiIiNQAC3QiIiIiIiIiNcACnYiIiIiIiEgNsEAnIiIiIiIiUgMs0ImIiIiIiIjUAAt0IiKiTiosLAxaWlrQ0tKCnp4e7OzsMHbsWOzatQt1dXUK97N7925YWlp23ECJiIhIISzQiYiIOrGgoCAUFxejqKgIP/zwA0aPHo2IiAhMnDgRtbW1qh4eERERtQELdCIiok7MwMAAPXr0gKOjIwYOHIjVq1fj4MGD+OGHH7B7924AwObNm9G/f3+YmJjAyckJixYtQmVlJQAgJSUFc+bMQVlZmfTT+DVr1gAAqqurERkZCUdHR5iYmODZZ59FSkqKai6UiIhIA7BAJyIi6mLGjBkDX19fJCYmAgC0tbXx8ccfIycnB3FxcUhOTsaKFSsAAP7+/tiyZQvMzc1RXFyM4uJiREZGAgBeffVVnDp1Cnv37kVmZiamTJmCoKAg5Ofnq+zaiIiIujItIYRQ9SCIiIio7cLCwlBaWooDBw402Tdt2jRkZmbi999/b7Lvm2++QXh4OG7dugWgfg760qVLUVpaKm1z5coVuLu748qVK3BwcJBuDwwMxDPPPIP169e3+/UQERFpOl1VD4CIiIjanxACWlpaAICffvoJGzZswPnz51FeXo7a2lo8ePAAVVVVMDY2bvb4rKwsSCQSeHt7y2yvrq5Gt27dOnz8REREmogFOhERUReUm5sLNzc3FBUVYeLEiVi4cCHWrVsHa2trnDhxAnPnzsXDhw/lFuiVlZXQ0dFBeno6dHR0ZPaZmpoq4xKIiIg0Dgt0IiKiLiY5ORlZWVlYtmwZ0tPTUVdXhw8++ADa2vVLz+zbt0+mvb6+PiQSicy2p556ChKJBDdu3MDw4cOVNnYiIiJNxgKdiIioE6uurkZJSQkkEgmuX7+Ow4cPY8OGDZg4cSJmzZqF7Oxs1NTUYOvWrZg0aRJSU1Px6aefyvTh6uqKyspKJCUlwdfXF8bGxvD29kZoaChmzZqFDz74AE899RRu3ryJpKQkDBgwABMmTFDRFRMREXVdXMWdiIioEzt8+DDs7e3h6uqKoKAgHDt2DB9//DEOHjwIHR0d+Pr6YvPmzdi0aRP69euH+Ph4bNiwQaYPf39/hIeHY+rUqbCxscF7770HAIiNjcWsWbPw+uuvw8fHBy+88ALS0tLg7OysikslIiLq8riKOxEREREREZEa4CfoRERERERERGqABToRERERERGRGmCBTkRERERERKQGWKATERERERERqQEW6ERERERERERqgAU6ERERERERkRpggU5ERERERESkBligExEREREREakBFuhEREREREREaoAFOhEREREREZEaYIFOREREREREpAZYoBMRERERERGpARboRERERERERGqABToRERERERGRGvj/8rLITpLP5z4AAAAASUVORK5CYII=", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Image\n", "\n", "Image(os.path.join(temp_dir, \"stock_gains.png\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Because code execution leave traces like code files and output in the file system, \n", "we may want to clean up the working directory after each conversation concludes."]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["temp_dir.cleanup()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Stop the docker command line executor to clean up the docker container."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["executor.stop()  # Stop the docker command line code executor."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Command Line or Jupyter Code Executor?\n", "\n", "The command line code executor does not keep any state in memory between\n", "executions of different code blocks it receives, as it writes each code block to\n", "a separate file and executes the code block in a new process.\n", "\n", "Contrast to the command line code executor, the Jupyter code executor\n", "runs all code blocks in the same Jupyter kernel, which keeps the state\n", "in memory between executions.\n", "See the topic page for [Jupyter Code Executor](/docs/topics/code-execution/jupyter-code-executor).\n", "\n", "The choice between command line and Jupyter code executor depends on the\n", "nature of the code blocks in agents' conversation.\n", "If each code block is a \"script\" that does not use variables from\n", "previous code blocks, the command line code executor is a good choice.\n", "If some code blocks contain expensive computations (e.g., training a\n", "machine learning model and loading a large amount of data), and you want to\n", "keep the state in memory to avoid repeated computations,\n", "the Jupyter code executor is a better choice."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Note on User Proxy Agent and Assistant Agent\n", "\n", "### User Proxy Agent\n", "\n", "In the previous examples, we create the code executor agent directly using\n", "the [`ConversableAgent`](/docs/reference/agentchat/conversable_agent#conversableagent) class. Existing AutoGen examples often create \n", "code executor agent using the [`UserProxyAgent`](/docs/reference/agentchat/user_proxy_agent#userproxyagent) class, \n", "which is a subclass of\n", "[`ConversableAgent`](/docs/reference/agentchat/conversable_agent#conversableagent) with `human_input_mode=ALWAYS` and `llm_config=False` --\n", "it always requests human input for every message and does not use LLM.\n", "It also comes with default `description` field for each of the\n", "`human_input_mode` setting.\n", "This class is a convenient short-cut for creating an agent that is\n", "intended to be used as a code executor."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Assistant Agent\n", "\n", "In the previous examples, we created the code writer agent directly using\n", "the [`ConversableAgent`](/docs/reference/agentchat/conversable_agent#conversableagent) class. Existing AutoGen examples often create the code writer\n", "agent using the [`AssistantAgent`](/docs/reference/agentchat/assistant_agent#assistantagent) class, which is a subclass of\n", "[`ConversableAgent`](/docs/reference/agentchat/conversable_agent#conversableagent) with `human_input_mode=NEVER` and `code_execution_config=False` \n", "-- it never requests human input and does not use code executor.\n", "It also comes with default `system_message` and `description` fields.\n", "This class is a convenient short-cut for creating an agent that is\n", "intended to be used as a code writer and does not execute code.\n", "\n", "In fact, in the previous example we use the default `system_message` field\n", "of the [`AssistantAgent`](/docs/reference/agentchat/assistant_agent#assistantagent) class to instruct the code writer agent how to use\n", "code executor."]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('You are a helpful AI assistant.\\n'\n", " 'Solve tasks using your coding and language skills.\\n'\n", " 'In the following cases, suggest python code (in a python coding block) or '\n", " 'shell script (in a sh coding block) for the user to execute.\\n'\n", " '    1. When you need to collect info, use the code to output the info you '\n", " 'need, for example, browse or search the web, download/read a file, print the '\n", " 'content of a webpage or a file, get the current date/time, check the '\n", " 'operating system. After sufficient info is printed and the task is ready to '\n", " 'be solved based on your language skill, you can solve the task by yourself.\\n'\n", " '    2. When you need to perform some task with code, use the code to perform '\n", " 'the task and output the result. Finish the task smartly.\\n'\n", " 'Solve the task step by step if you need to. If a plan is not provided, '\n", " 'explain your plan first. Be clear which step uses code, and which step uses '\n", " 'your language skill.\\n'\n", " 'When using code, you must indicate the script type in the code block. The '\n", " 'user cannot provide any other feedback or perform any other action beyond '\n", " \"executing the code you suggest. The user can't modify your code. So do not \"\n", " \"suggest incomplete code which requires users to modify. Don't use a code \"\n", " \"block if it's not intended to be executed by the user.\\n\"\n", " 'If you want the user to save the code in a file before executing it, put # '\n", " \"filename: <filename> inside the code block as the first line. Don't include \"\n", " 'multiple code blocks in one response. Do not ask users to copy and paste the '\n", " \"result. Instead, use 'print' function for the output when relevant. Check \"\n", " 'the execution result returned by the user.\\n'\n", " 'If the result indicates there is an error, fix the error and output the code '\n", " 'again. Suggest the full code instead of partial code or code changes. If the '\n", " \"error can't be fixed or if the task is not solved even after the code is \"\n", " 'executed successfully, analyze the problem, revisit your assumption, collect '\n", " 'additional info you need, and think of a different approach to try.\\n'\n", " 'When you find an answer, verify the answer carefully. Include verifiable '\n", " 'evidence in your response if possible.\\n'\n", " 'Reply \"TERMINATE\" in the end when everything is done.\\n'\n", " '    ')\n"]}], "source": ["import pprint\n", "\n", "from autogen import AssistantAgent\n", "\n", "pprint.pprint(AssistantAgent.DEFAULT_SYSTEM_MESSAGE)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Best Practice\n", "\n", "It is very important to note that the [`UserProxyAgent`](/docs/reference/agentchat/user_proxy_agent#userproxyagent) and [`AssistantAgent`](/docs/reference/agentchat/assistant_agent#assistantagent)\n", "are meant to be shortcuts to avoid writing the `system_message` instructions\n", "for the [`ConversableAgent`](/docs/reference/agentchat/conversable_agent#conversableagent) class. \n", "They are not suitable for all use cases.\n", "As we will show in the next chapter, tuning the \n", "`system_message` field\n", "is vital for agent to work properly in more complex conversation patterns\n", "beyond two-agent chat.\n", "\n", "As a best practice, always tune your agent's `system_message` instructions\n", "for your specific use case and avoid subclassing [`UserProxyAgent`](/docs/reference/agentchat/user_proxy_agent#userproxyagent) and\n", "[`AssistantAgent`](/docs/reference/agentchat/assistant_agent#assistantagent)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "In this chapter, we introduced code executors, how to set up Docker and local\n", "execution, and how to use code execution in a conversation to solve tasks.\n", "In the next chapter, we will introduce tool use, which is similar to code\n", "executors but restricts what code an agent can execute."]}], "metadata": {"kernelspec": {"display_name": "autogen", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}