{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tool Use\n", "\n", "In the previous chapter, we explored code executors which give\n", "agents the super power of programming.\n", "Agents writing arbitrary code is useful, however,\n", "controlling what code an agent writes can be challenging. \n", "This is where tools come in.\n", "\n", "Tools are pre-defined functions that agents can use. Instead of writing arbitrary\n", "code, agents can call tools to perform actions, such as searching the web,\n", "performing calculations, reading files, or calling remote APIs.\n", "Because you can control what tools are available to an agent, you can control what\n", "actions an agent can perform.\n", "\n", "````mdx-code-block\n", ":::note\n", "Tool use is currently only available for LLMs\n", "that support OpenAI-compatible tool call API.\n", ":::\n", "````"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Tools\n", "\n", "Tools can be created as regular Python functions.\n", "For example, let's create a calculator tool which\n", "can only perform a single operation at a time."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:48.858694Z", "start_time": "2024-04-25T01:49:48.854420Z"}}, "outputs": [], "source": ["from typing import Annotated, Literal\n", "\n", "Operator = Literal[\"+\", \"-\", \"*\", \"/\"]\n", "\n", "\n", "def calculator(a: int, b: int, operator: Annotated[Operator, \"operator\"]) -> int:\n", "    if operator == \"+\":\n", "        return a + b\n", "    elif operator == \"-\":\n", "        return a - b\n", "    elif operator == \"*\":\n", "        return a * b\n", "    elif operator == \"/\":\n", "        return int(a / b)\n", "    else:\n", "        raise ValueError(\"Invalid operator\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The above function takes three arguments:\n", "`a` and `b` are the integer numbers to be operated on;\n", "`operator` is the operation to be performed.\n", "We used type hints to define the types of the arguments and the return value.\n", "\n", "````mdx-code-block\n", ":::tip\n", "Always use type hints to define the types of the arguments and the return value\n", "as they provide helpful hints to the agent about the tool's usage.\n", ":::\n", "````"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Registering Tools\n", "\n", "Once you have created a tool, you can register it with the agents that\n", "are involved in conversation."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:48.946697Z", "start_time": "2024-04-25T01:49:48.857869Z"}}, "outputs": [{"data": {"text/plain": ["<function __main__.calculator(a: int, b: int, operator: Annotated[Literal['+', '-', '*', '/'], 'operator']) -> int>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "from autogen import ConversableAgent\n", "\n", "# Let's first define the assistant agent that suggests tool calls.\n", "assistant = ConversableAgent(\n", "    name=\"Assistant\",\n", "    system_message=\"You are a helpful AI assistant. \"\n", "    \"You can help with simple calculations. \"\n", "    \"Return 'TERMINATE' when the task is done.\",\n", "    llm_config={\"config_list\": [{\"model\": \"gpt-4\", \"api_key\": os.environ[\"OPENAI_API_KEY\"]}]},\n", ")\n", "\n", "# The user proxy agent is used for interacting with the assistant agent\n", "# and executes tool calls.\n", "user_proxy = ConversableAgent(\n", "    name=\"User\",\n", "    llm_config=False,\n", "    is_termination_msg=lambda msg: msg.get(\"content\") is not None and \"TERMINATE\" in msg[\"content\"],\n", "    human_input_mode=\"NEVER\",\n", ")\n", "\n", "# Register the tool signature with the assistant agent.\n", "assistant.register_for_llm(name=\"calculator\", description=\"A simple calculator\")(calculator)\n", "\n", "# Register the tool function with the user proxy agent.\n", "user_proxy.register_for_execution(name=\"calculator\")(calculator)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the above code, we registered the `calculator` function as a tool with\n", "the assistant and user proxy agents. We also provide a name and a description\n", "for the tool for the assistant agent to understand its usage.\n", "\n", "````mdx-code-block\n", ":::tip\n", "Always provide a clear and concise description for the tool as it helps the\n", "agent's underlying LLM to understand the tool's usage.\n", ":::\n", "````\n", "\n", "Similar to code executors, a tool must be registered with at least two agents\n", "for it to be useful in conversation. \n", "The agent registered with the tool's signature\n", "through \n", "[`register_for_llm`](/docs/reference/agentchat/conversable_agent#register_for_llm)\n", "can call the tool;\n", "the agent registered with the tool's function object through \n", "[`register_for_execution`](/docs/reference/agentchat/conversable_agent#register_for_execution)\n", "can execute the tool's function."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, you can use \n", "[`autogen.register_function`](/docs/reference/agentchat/conversable_agent#register_function-1)\n", "function to register a tool with both agents at once."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:48.953345Z", "start_time": "2024-04-25T01:49:48.947026Z"}}, "outputs": [], "source": ["from autogen import register_function\n", "\n", "# Register the calculator function to the two agents.\n", "register_function(\n", "    calculator,\n", "    caller=assistant,  # The assistant agent can suggest calls to the calculator.\n", "    executor=user_proxy,  # The user proxy agent can execute the calculator calls.\n", "    name=\"calculator\",  # By default, the function name is used as the tool name.\n", "    description=\"A simple calculator\",  # A description of the tool.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using Tool\n", "\n", "Once the tool is registered, we can use it in conversation.\n", "In the code below, we ask the assistant to perform some arithmetic\n", "calculation using the `calculator` tool."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:57.947530Z", "start_time": "2024-04-25T01:49:48.953943Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "What is (44232 + 13312 / (232 - 32)) * 5?\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_4rElPoLggOYJmkUutbGaSTX1): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "  \"a\": 232,\n", "  \"b\": 32,\n", "  \"operator\": \"-\"\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_4rElPoLggOYJmkUutbGaSTX1) *****\u001b[0m\n", "200\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_SGtr8tK9A4iOCJGdCqkKR2Ov): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "  \"a\": 13312,\n", "  \"b\": 200,\n", "  \"operator\": \"/\"\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_SGtr8tK9A4iOCJGdCqkKR2Ov) *****\u001b[0m\n", "66\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_YsR95CM1Ice2GZ7ZoStYXI6M): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "  \"a\": 44232,\n", "  \"b\": 66,\n", "  \"operator\": \"+\"\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_YsR95CM1Ice2GZ7ZoStYXI6M) *****\u001b[0m\n", "44298\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_oqZn4rTjyvXYcmjAXkvVaJm1): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "  \"a\": 44298,\n", "  \"b\": 5,\n", "  \"operator\": \"*\"\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_oqZn4rTjyvXYcmjAXkvVaJm1) *****\u001b[0m\n", "221490\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "The result of the calculation is 221490. TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["chat_result = user_proxy.initiate_chat(assistant, message=\"What is (44232 + 13312 / (232 - 32)) * 5?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's verify the answer:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:57.956063Z", "start_time": "2024-04-25T01:49:57.948882Z"}}, "outputs": [{"data": {"text/plain": ["221490"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["(44232 + int(13312 / (232 - 32))) * 5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The answer is correct.\n", "You can see that the assistant is able to understand the tool's usage\n", "and perform calculation correctly."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON>\n", "\n", "If you are familiar with [OpenAI's tool use API](https://platform.openai.com/docs/guides/function-calling), \n", "you might be wondering\n", "why we didn't create a tool schema.\n", "In fact, the tool schema is automatically generated from the function signature\n", "and the type hints.\n", "You can see the tool schema by inspecting the `llm_config` attribute of the\n", "agent."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:57.956696Z", "start_time": "2024-04-25T01:49:57.952767Z"}}, "outputs": [{"data": {"text/plain": ["[{'type': 'function',\n", "  'function': {'description': 'A simple calculator',\n", "   'name': 'calculator',\n", "   'parameters': {'type': 'object',\n", "    'properties': {'a': {'type': 'integer', 'description': 'a'},\n", "     'b': {'type': 'integer', 'description': 'b'},\n", "     'operator': {'enum': ['+', '-', '*', '/'],\n", "      'type': 'string',\n", "      'description': 'operator'}},\n", "    'required': ['a', 'b', 'operator']}}}]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["assistant.llm_config[\"tools\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can see the tool schema has been automatically generated from the function\n", "signature and the type hints, as well as the description.\n", "This is why it is important to use type hints and provide a clear description\n", "for the tool as the LLM uses them to understand the tool's usage."]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use Pydantic model for the type hints to provide more complex\n", "type schema. In the example below, we use a Pydantic model to define the\n", "calculator input."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:57.965069Z", "start_time": "2024-04-25T01:49:57.958274Z"}}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class CalculatorInput(BaseModel):\n", "    a: Annotated[int, <PERSON>(description=\"The first number.\")]\n", "    b: Annotated[int, <PERSON>(description=\"The second number.\")]\n", "    operator: Annotated[<PERSON><PERSON>, <PERSON>(description=\"The operator.\")]\n", "\n", "\n", "def calculator(input: Annotated[CalculatorInput, \"Input to the calculator.\"]) -> int:\n", "    if input.operator == \"+\":\n", "        return input.a + input.b\n", "    elif input.operator == \"-\":\n", "        return input.a - input.b\n", "    elif input.operator == \"*\":\n", "        return input.a * input.b\n", "    elif input.operator == \"/\":\n", "        return int(input.a / input.b)\n", "    else:\n", "        raise ValueError(\"Invalid operator\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Same as before, we register the tool with the agents using the name `\"calculator\"`.\n", "\n", "````mdx-code-block\n", ":::tip\n", "Registering tool to the same name will override the previous tool.\n", ":::\n", "````"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:57.990811Z", "start_time": "2024-04-25T01:49:57.962315Z"}}, "outputs": [{"data": {"text/plain": ["<function __main__.calculator(input: typing.Annotated[__main__.CalculatorInput, 'Input to the calculator.']) -> int>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["assistant.register_for_llm(name=\"calculator\", description=\"A calculator tool that accepts nested expression as input\")(\n", "    calculator\n", ")\n", "user_proxy.register_for_execution(name=\"calculator\")(calculator)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can see the tool schema has been updated to reflect the new type schema."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:49:57.991342Z", "start_time": "2024-04-25T01:49:57.972554Z"}}, "outputs": [{"data": {"text/plain": ["[{'type': 'function',\n", "  'function': {'description': 'A calculator tool that accepts nested expression as input',\n", "   'name': 'calculator',\n", "   'parameters': {'type': 'object',\n", "    'properties': {'input': {'properties': {'a': {'description': 'The first number.',\n", "        'title': 'A',\n", "        'type': 'integer'},\n", "       'b': {'description': 'The second number.',\n", "        'title': 'B',\n", "        'type': 'integer'},\n", "       'operator': {'description': 'The operator.',\n", "        'enum': ['+', '-', '*', '/'],\n", "        'title': 'Operator',\n", "        'type': 'string'}},\n", "      'required': ['a', 'b', 'operator'],\n", "      'title': 'CalculatorInput',\n", "      'type': 'object',\n", "      'description': 'Input to the calculator.'}},\n", "    'required': ['input']}}}]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["assistant.llm_config[\"tools\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's use the tool in conversation."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:50:17.808416Z", "start_time": "2024-04-25T01:49:57.975143Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "What is (1423 - 123) / 3 + (32 + 23) * 5?\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_Uu4diKtxlTfkwXuY6MmJEb4E): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "    \"input\": {\n", "        \"a\": (1423 - 123) / 3,\n", "        \"b\": (32 + 23) * 5,\n", "        \"operator\": \"+\"\n", "    }\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_Uu4diKtxlTfkwXuY6MmJEb4E) *****\u001b[0m\n", "Error: Expecting value: line 1 column 29 (char 28)\n", " You argument should follow json format.\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "I apologize for the confusion, I seem to have made a mistake. Let me recalculate the expression properly.\n", "\n", "First, we need to do the calculations within the brackets. So, calculating (1423 - 123), (32 + 23), and then performing remaining operations.\n", "\u001b[32m***** Suggested tool call (call_mx3M3fNOwikFNoqSojDH1jIr): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "    \"input\": {\n", "        \"a\": 1423,\n", "        \"b\": 123,\n", "        \"operator\": \"-\"\n", "    }\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_mx3M3fNOwikFNoqSojDH1jIr) *****\u001b[0m\n", "1300\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_hBAL2sYi6Y5ZtTHCNPCmxdN3): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "    \"input\": {\n", "        \"a\": 32,\n", "        \"b\": 23,\n", "        \"operator\": \"+\"\n", "    }\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_hBAL2sYi6Y5ZtTHCNPCmxdN3) *****\u001b[0m\n", "55\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_wO3AP7EDeJvsVLCpvv5LohUa): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "    \"input\": {\n", "        \"a\": 1300,\n", "        \"b\": 3,\n", "        \"operator\": \"/\"\n", "    }\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_wO3AP7EDeJvsVLCpvv5LohUa) *****\u001b[0m\n", "433\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_kQ2hDhqem8BHNlaHaE9ezvvQ): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "    \"input\": {\n", "        \"a\": 55,\n", "        \"b\": 5,\n", "        \"operator\": \"*\"\n", "    }\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_kQ2hDhqem8BHNlaHaE9ezvvQ) *****\u001b[0m\n", "275\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "\u001b[32m***** Suggested tool call (call_1FLDUdvAZmjlSD7g5GFFJOpO): calculator *****\u001b[0m\n", "Arguments: \n", "{\n", "    \"input\": {\n", "        \"a\": 433,\n", "        \"b\": 275,\n", "        \"operator\": \"+\"\n", "    }\n", "}\n", "\u001b[32m***************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION calculator...\u001b[0m\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[33mUser\u001b[0m (to Assistant):\n", "\n", "\u001b[32m***** Response from calling tool (call_1FLDUdvAZmjlSD7g5GFFJOpO) *****\u001b[0m\n", "708\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mAssistant\u001b[0m (to User):\n", "\n", "The calculation result of the expression (1423 - 123) / 3 + (32 + 23) * 5 is 708. Let's proceed to the next task.\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["chat_result = user_proxy.initiate_chat(assistant, message=\"What is (1423 - 123) / 3 + (32 + 23) * 5?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's verify the answer:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T01:50:17.818095Z", "start_time": "2024-04-25T01:50:17.808502Z"}}, "outputs": [{"data": {"text/plain": ["708"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["int((1423 - 123) / 3) + (32 + 23) * 5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Again, the answer is correct. You can see that the assistant is able to understand\n", "the new tool schema and perform calculation correctly."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How to hide tool usage and code execution within a single agent?\n", "\n", "Sometimes it is preferable to hide the tool usage inside a single agent, \n", "i.e., the tool call and tool response messages are kept invisible from outside\n", "of the agent, and the agent responds to outside messages with tool usages\n", "as \"internal monologues\". \n", "For example, you might want build an agent that is similar to\n", "the [OpenAI's Assistant](https://platform.openai.com/docs/assistants/how-it-works)\n", "which executes built-in tools internally.\n", "\n", "To achieve this, you can use [nested chats](/docs/tutorial/conversation-patterns#nested-chats).\n", "Nested chats allow you to create \"internal monologues\" within an agent\n", "to call and execute tools. This works for code execution as well.\n", "See [nested chats for tool use](/docs/notebooks/agentchat_nested_chats_chess) for an example."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "In this chapter, we showed you how to create, register and use tools.\n", "Tools allows agents to perform actions without writing arbitrary code.\n", "In the next chapter, we will introduce conversation patterns, and show\n", "how to use the result of a conversation."]}], "metadata": {"kernelspec": {"display_name": "autogen", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}