.api-method > .menu__link {
    align-items: center;
    justify-content: start;
  }

.api-method > .menu__link::before {
  width: 50px;
  height: 20px;
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
  font-weight: 600;
  border-radius: 0.25rem;
  border: 1px solid;
  border-inline-start-width: 5px;
  margin-right: var(--ifm-spacing-horizontal);
  text-align: center;
  flex-shrink: 0;
}

.get > .menu__link::before {
  content: "get";
  background-color: var(--ifm-color-warning-contrast-background);
  color: var(--ifm-color-warning-contrast-foreground);
  border-color: var(--ifm-color-warning-dark);
}

.post > .menu__link::before {
  content: "post";
  background-color: var(--ifm-color-success-contrast-background);
  color: var(--ifm-color-success-contrast-foreground);
  border-color: var(--ifm-color-success-dark);
}

.delete > .menu__link::before {
  content: "del";
  background-color: var(--ifm-color-danger-contrast-background);
  color: var(--ifm-color-danger-contrast-foreground);
  border-color: var(--ifm-color-danger-dark);
}

.put > .menu__link::before {
  content: "put";
  background-color: var(--ifm-color-info-contrast-background);
  color: var(--ifm-color-info-contrast-foreground);
  border-color: var(--ifm-color-info-dark);
}

.patch > .menu__link::before {
  content: "patch";
  background-color: var(--ifm-color-success-contrast-background);
  color: var(--ifm-color-success-contrast-foreground);
  border-color: var(--ifm-color-success-dark);
}

.head > .menu__link::before {
  content: "head";
  background-color: var(--ifm-color-secondary-contrast-background);
  color: var(--ifm-color-secondary-contrast-foreground);
  border-color: var(--ifm-color-secondary-dark);
}
