[{"title": "AutoTx - Crypto Transactions Agent", "link": "https://www.agentcoin.org/blog/autotx", "description": "Generates on-chain transactions, which are submitted to a smart account so users can easily approve & execute them.", "image": "autotx.png", "tags": ["tools", "groupchat", "app", "blockchain"]}, {"title": "AutoGen Virtual Focus Group", "link": "https://github.com/msamylea/autogen_focus_group", "description": "A virtual consumer focus group with multiple custom personas, product details, and final analysis created with AutoGen, Ollama/Llama3, and Streamlit.", "image": "default.png", "tags": ["ui", "groupchat"]}, {"title": "Function Generator & Validator", "link": "https://github.com/abhaymathur21/TensionCode", "description": "A platform where user-required code is generated and simultaneously validated against sample data by AutoGen.", "image": "TensionCode.png", "tags": ["app", "ui"]}, {"title": "Autogen Robot", "link": "https://github.com/AaronWard/generative-ai-workbook/tree/main/personal_projects/19.autogen-robot", "description": "This project showcases how agent's can act as a brain to control a physical robot.", "image": "robot.jpg", "tags": ["robotics"]}, {"title": "AutoGen Group Chat Playground", "link": "https://huggingface.co/spaces/thinkall/AutoGen_Playground", "description": "A huggingface space to explore AutoGen group chat, build agents with zero-code, and access source code for reuse.", "tags": ["ui"]}, {"title": "A Stateful Dev Environment Powered by Jupyter Notebook", "link": "https://github.com/olimoz/AI_Teams_AutoGen/blob/main/JupyterNotebooksForAutoGen.ipynb", "description": "An AutoGen Teams Powered by Jupyter notebook.", "tags": ["extension", "tools"]}, {"title": "Solving Security Vulnerabilities with AutoGen", "link": "https://www.linkedin.com/pulse/solving-security-vulnerabilities-llms-society-mind-model-leah-bonser-kcswc?trk=public_post_feed-article-content", "description": "An article discussing the use of AutoGen to tackle security vulnerabilities.", "image": "default.png", "tags": ["app"]}, {"title": "Research Agents via AutoGen", "link": "https://youtu.be/AVInhYBUnKs?feature=shared", "description": "A guide to building a team of AI research agents using AutoGen.", "image": "default.png", "tags": ["groupchat", "tools"]}, {"title": "AutoGen with Ollama and LiteLLM", "link": "https://youtu.be/y7wMTwJN7rA", "description": "A demonstration of integrating Ollama, LiteLLM, and AutoGen.", "image": "default.png", "tags": ["extension"]}, {"title": "AutoGen Engineer", "link": "https://chat.openai.com/g/g-Y50TY4F35-<PERSON>Gen-engineer", "description": "Join the AutoGen Engineer group chat to collaborate and build with others.", "image": "default.png", "tags": ["groupchat", "app"]}, {"title": "AutoGen with Obsidian", "link": "https://youtu.be/iWdVAr4xMkg", "description": "Learn how to integrate AutoGen with Obsidian for note-taking and management.", "image": "default.png", "tags": ["tools", "app"]}, {"title": "AutoGen Builder GPT", "link": "https://chat.openai.com/g/g-EwugVj4zq-AutoGen-builder", "description": "A platform for building conversational AI agents with AutoGen Builder GPT.", "image": "default.png", "tags": ["groupchat", "ui"]}, {"title": "AutoGen Multi-Round Human Interaction Chatbot with Gradio 4.0", "link": "https://huggingface.co/spaces/thinkall/AutoGen-human-input-demo", "description": "Experience a multi-round human interaction chatbot built with AutoGen and Gradio 4.0.", "image": "default.png", "tags": ["ui", "app"]}, {"title": "Agentcloud.dev (UI for AutoGen)", "link": "https://github.com/rnadigital/agentcloud", "description": "Agentcloud.dev provides a user interface for managing and collaborating with AutoGen agents.", "image": "default.png", "tags": ["ui"]}, {"title": "Next.js + FASTAPI Based UI for AutoGen", "link": "https://github.com/victordibia/AutoGen-ui", "description": "A project featuring a UI for AutoGen built with Next.js and FastAPI.", "image": "default.png", "tags": ["ui"]}, {"title": "Full Function UI for AutoGen Powered by Panel", "link": "https://youtu.be/9lSaRP9GLCY?si=HihULAe3FFyteFHY", "description": "A UI allows users to directly interact with AI agents in real-time during a group chat scenario", "tags": ["ui"]}, {"title": "AutoGen Monitoring and Observability", "link": "https://docs.arize.com/phoenix/quickstart/llm-traces/autogen-support", "description": "Documentation on monitoring and observability features for AutoGen.", "image": "default.png", "tags": ["extension"]}, {"title": "Postgres Data Analytics AI Agent with AutoGen", "link": "https://www.youtube.com/playlist?list=PLS_o2ayVCKvDzj2YxeFqMq9UbR1PkPEh0", "description": "Utilizing AutoGen to speak directly to Postgres Database.", "image": "default.png", "tags": ["tools", "app"]}, {"title": "AutoGen with Local LLMs", "link": "https://hackernoon.com/beep-beep-bop-bop-how-to-deploy-multiple-ai-agents-using-local-llms", "description": "An article on deploying multiple AI agents using local LLMs with AutoGen.", "image": "default.png", "tags": ["extension"]}, {"title": "AutoGen with FastApi backend and React Frontend", "link": "https://github.com/bonadio/AutoGenwebdemo", "description": "A demonstration of using AutoGen with a FastAPI backend and React frontend.", "image": "default.png", "tags": ["ui"]}, {"title": "Talk to AutoGen Agents Using Whisper and Gradio", "link": "https://youtu.be/WysBjwJoulo", "description": "Interact with AutoGen agents using Whisper and Gradio interfaces.", "image": "default.png", "tags": ["ui"]}, {"title": "AutoGen + LangChain + ChromaDB = You Super AI Assistant", "link": "https://www.youtube.com/watch?v=fd9fcRhYoFQ", "description": "Create a super AI assistant combining AutoGen, LangChain, and ChromaDB.", "image": "default.png", "tags": ["app"]}, {"title": "AutoGen + Flowise = Super AI Agents on No-Code Platform", "link": "https://github.com/sugarforever/LangChain-Advanced/blob/main/Integrations/AutoGen/autogen_flowise_ai_agent.ipynb", "description": "Build super AI agents on a no-code platform using AutoGen and Flowise.", "image": "default.png", "tags": ["app"]}, {"title": "AutoGen with RunPod and TextGen WebUI", "link": "https://youtu.be/FHXmiAvloUg", "description": "Learn how to use AutoGen with RunPod and TextGen WebUI for enhanced AI agent integration.", "image": "default.png", "tags": ["ui", "extension"]}, {"title": "<PERSON> Collaborates with AutoGen for Tweet Analysis", "link": "https://github.com/ngaut/jarvis#demo", "description": "Explore how <PERSON> collaborates with AutoGen for tweet analysis.", "image": "default.png", "tags": ["tools", "app"]}, {"title": "AutoGen + LangChain + PlayHT = Super AI Agent that Speaks", "link": "https://www.youtube.com/watch?v=zo2ft4Qje1Y", "description": "Combine AutoGen, <PERSON><PERSON><PERSON><PERSON>, and PlayHT to create a speaking super AI agent.", "image": "default.png", "tags": ["tools", "app"]}, {"title": "AutoGen Flow with FastAPI and Nextjs", "link": "https://github.com/jaemil/agentsflow", "description": "A development flow using AutoGen with FastAPI and Next.js.", "image": "default.png", "tags": ["ui"]}, {"title": "Build Vision-Enabled AI Agents with AutoGen + Llava", "link": "https://youtu.be/JgVb8A6OJwM", "description": "Tutorial on building vision-enabled AI agents using AutoGen and llava.", "image": "default.png", "tags": ["tools", "app"]}, {"title": "AutoGen + Chainlit chat interface with multi-agent conversation", "link": "https://github.com/antoineross/Autogen-UI/tree/main", "description": "Chainlit chat interface with multi-agent conversation between agents to complete a tasks", "image": "default.png", "tags": ["ui"]}, {"title": "XForce IDE: Build AutoGen based workforces from a drag and drop UI", "link": "https://ide.x-force.ai", "description": "X-Force IDE is a low-code, agent-as-a-service UI framework that lets you create AutoGen-based workforces from a drag-and-drop-like user interface.", "image": "x-force-ide-ui.png", "tags": ["ui"]}, {"title": "Multimodal Webagent created with AutoGen and OpenAI's Assistants API", "link": "https://github.com/schauppi/MultimodalWebAgent", "description": "A multimodal webbrowsing agent that autonomously browses the web.", "image": "webagent.jpg", "tags": ["tools", "app"]}, {"title": "Create Issues from Code Commits - using Autogen", "link": "https://blog.composio.dev/automating-task-creation-with-openai-assistant-from-code-comments-to-linear-issues/", "description": "Automatically creating linear tasks and assigning them to the right person, project, and team from GitHub commits using AutoGen Agents.", "image": "composio-autogen.png", "tags": ["tools"]}, {"title": "Agentok - Autogen Visualized", "link": "https://github.com/hughlv/agentok", "description": "Offering intuitive visual tools that streamline the creation and management of complex AutoGen workflows.", "image": "agentok.png", "tags": ["tools", "ui", "app"]}, {"title": "Expense Tracker - using Autogen", "link": "https://github.com/Kirushikesh/Personal-Finance-Agent", "description": "Tracks personal finance income and expenses then helps the user to analyse it using AutoGen agents.", "image": "default.png", "tags": ["tools", "app"]}, {"title": "Multimodal Ecommerce Retail Chatbot", "link": "https://github.com/haard7/multi-agent-fullstack-project", "description": "Chatbot handling recommendation, purchase, order tracking and fraud detection in an ecommerce retail", "image": "default.png", "tags": ["ui", "groupchat"]}]