---
title: "Ann<PERSON>: Assisting AutoGen Devs Via AutoGen"
authors:
  - gagb
tags: [AutoGen]
---

import AutoAnny<PERSON>ogo from './img/AutoAnnyLogo.jpg';

<div style={{ display: "flex", justifyContent: "center" }}>
  <img src={AutoAnnyLogo} alt="AutoAnny Logo" style={{ width: "250px" }} />
</div>
<p align="center"><em><PERSON><PERSON> is a Discord bot powered by AutoGen to help AutoGen's Discord server.</em></p>


## TL;DR

We are adding a new sample app called Anny-- a simple Discord bot powered
by AutoGen that's intended to assist AutoGen Devs. See [`samples/apps/auto-anny`](https://github.com/ag2ai/build-with-ag2/tree/main/samples/apps/auto-anny) for details.

## Introduction

Over the past few months, AutoGen has experienced large growth in number of users and number of community requests and feedback.
However, accommodating this demand and feedback requires manually sifting through issues, PRs, and discussions on GitHub, as well as managing messages
 from AutoGen's 14000+ community members on Discord. There are many tasks that AutoGen's developer community has to perform everyday,
  but here are some common ones:
- Answering questions
- Recognizing and prioritizing bugs and features
- Maintaining responsiveness for our incredible community
- Tracking growth

This requires a significant amount of effort. Agentic-workflows and interfaces promise adding
 immense value-added automation for many tasks, so we thought *why don't we use AutoGen to make
  our lives easier?!* So we're turning to automation to help us and allow
   us to focus on what's most critical.

## Current Version of Anny
The current version of Anny is pretty simple -- it uses the Discord API and AutoGen to enable a bot
 that can respond to a set of commands.

For example, it supports commands like `/heyanny help` for command listing, `/heyanny ghstatus` for
 GitHub activity summary, `/heyanny ghgrowth` for GitHub repo growth indicators, and `/heyanny ghunattended` for listing unattended issues and PRs. Most of these commands use multiple AutoGen agents to accomplish these task.

To use Anny, please follow instructions in [`samples/apps/auto-anny`](https://github.com/ag2ai/build-with-ag2/tree/main/samples/apps/auto-anny).

## It's Not Just for AutoGen
If you're an open-source developer managing your own project, you can probably relate to our challenges. We invite you to check out Anny and contribute to its development and roadmap.
