---
title: "Code execution is now by default inside docker container"
authors:
  - olga<PERSON><PERSON>
tags: [AutoGen]
---


## TL;DR

AutoGen 0.2.8 enhances operational safety by making 'code execution inside a Docker container' the default setting, focusing on informing users about its operations and empowering them to make informed decisions regarding code execution.

The new release introduces a breaking change where the `use_docker` argument is set to `True` by default in code executing agents. This change underscores our commitment to prioritizing security and safety in AutoGen.

## Introduction

AutoGen has code-executing agents, usually defined as a `UserProxyAgent`, where code execution is by default ON. Until now, unless explicitly specified by the user, any code generated by other agents would be executed by code-execution agents locally, i.e. wherever AutoGen was being executed. If AutoGen happened to be run in a docker container then the risks of running code were minimized. However, if AutoGen runs outside of Docker, it's easy particularly for new users to overlook code-execution risks.

AutoGen has now changed to by default execute any code inside a docker container (unless execution is already happening inside a docker container). It will launch a Docker image (either user-provided or default), execute the new code, and then terminate the image, preparing for the next code execution cycle.

We understand that not everyone is concerned about this especially when playing around with AutoGen for the first time. We have provided easy ways to turn this requirement off. But we believe that making sure that the user is aware of the fact that code will be executed locally, and prompting them to think about the security implications of running code locally is the right step for AutoGen.

## Example

The example shows the default behaviour which is that any code generated by assistant agent and executed by user_proxy agent, will attempt to use a docker container to execute the code. If docker is not running, it will throw an error. User can decide to activate docker or opt in for local code execution.

```python
from autogen import AssistantAgent, UserProxyAgent, config_list_from_json
assistant = AssistantAgent("assistant", llm_config={"config_list": config_list})
user_proxy = UserProxyAgent("user_proxy", code_execution_config={"work_dir": "coding"})
user_proxy.initiate_chat(assistant, message="Plot a chart of NVDA and TESLA stock price change YTD.")
```

To opt out of from this default behaviour there are some options.

### Disable code execution entirely

- Set `code_execution_config` to `False` for each code-execution agent. E.g.:

```python
user_proxy = autogen.UserProxyAgent(name="user_proxy", llm_config=llm_config, code_execution_config=False)
```

### Run code execution locally

- `use_docker` can be set to `False` in `code_execution_config` for each code-execution agent.
- To set it for all code-execution agents at once: set `AUTOGEN_USE_DOCKER` to `False` as an environment variable.

E.g.:

```python
user_proxy = autogen.UserProxyAgent(name="user_proxy", llm_config=llm_config,
    code_execution_config={"work_dir":"coding", "use_docker":False})
```

## Related documentation

- [Code execution with docker](https://ag2ai.github.io/ag2/docs/Installation#code-execution-with-docker-default)
- [How to disable code execution in docker](https://ag2ai.github.io/ag2/docs/FAQ#agents-are-throwing-due-to-docker-not-running-how-can-i-resolve-this)

## Conclusion

AutoGen 0.2.8 now improves the code execution safety and is ensuring that the user is properly informed of what autogen is doing and can make decisions around code-execution.
