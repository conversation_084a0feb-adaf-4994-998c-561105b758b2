import re
import json
import os
import argparse
import datetime

def read_predictions(eval_file):
    if not os.path.exists(eval_file):
        print(f"Error: Evaluation file not found at {eval_file}")
        return {}

    try:
        with open(eval_file, 'r', encoding='utf-8') as file:
            data = file.read()
    except Exception as e:
        print(f"Error reading evaluation file {eval_file}: {e}")
        return {}

    predictions = {}
    pattern = r"Prediction for ([^:]+\.json):(.*?)(?=Prediction for|\Z)"
    blocks = re.finditer(pattern, data, re.DOTALL)
    parsed_count = 0

    for block in blocks:
        content = block.group(2).strip()
        idx = block.group(1).strip()
        agent_name_match = re.search(r"Agent Name:\s*([\w_]+)", content, re.IGNORECASE)
        step_number_match = re.search(r"Step Number:\s*(\d+)", content, re.IGNORECASE)

        if agent_name_match and step_number_match:
            agent_name = agent_name_match.group(1)
            step_number = step_number_match.group(1)
            predictions[idx] = {
                'predicted_agent': agent_name,
                'predicted_step': f"{step_number}"
            }
            parsed_count += 1
        else:
            print(f"Warning: Could not parse Agent Name/Step Number for {idx} in {eval_file}")

    print(f"--- Predictions Read from {eval_file} ---")
    print(f"Successfully parsed predictions for {parsed_count} files.")
    print("=======================================")
    return predictions

def read_actual_data(labeled_json):
    try:
        with open(labeled_json, 'r', encoding='utf-8') as file:
            data = json.load(file)
        mistake_agent = data.get('mistake_agent')
        mistake_step = data.get('mistake_step')
        if mistake_agent is not None and mistake_step is not None:
            return str(mistake_agent), str(mistake_step)
        else:
            print(f"Warning: 'mistake_agent' or 'mistake_step' key missing in {labeled_json}")
            return None, None
    except FileNotFoundError:
        print(f"Error: Actual data file not found during read: {labeled_json}")
        return None, None
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {labeled_json}")
        return None, None
    except Exception as e:
        print(f"Error reading actual data from {labeled_json}: {e}")
        return None, None

def evaluate_accuracy(predictions, data_path, total_files):
    correct_agent = 0
    correct_step = 0
    files_evaluated = 0
    detailed_results = []

    if total_files == 0:
        print("Error: No JSON files found in the data path to evaluate against.")
        return 0.0, 0.0, detailed_results

    print(f"\n--- Starting Evaluation ---")
    print(f"Total reference JSON files found in {data_path}: {total_files}")
    print(f"Predictions available for {len(predictions)} files.")
    print("=======================================")

    for idx, pred in predictions.items():
        labeled_file = os.path.join(data_path, f"{idx}")

        if os.path.exists(labeled_file):
            files_evaluated += 1
            actual_agent, actual_step = read_actual_data(labeled_file)

            if actual_agent is not None and actual_step is not None:
                agent_correct = actual_agent in pred['predicted_agent']
                step_correct = actual_step in pred['predicted_step']
                
                if agent_correct:
                    correct_agent += 1
                if step_correct:
                    correct_step += 1
                
                # Store detailed result
                detailed_results.append({
                    'file': idx,
                    'predicted_agent': pred['predicted_agent'],
                    'actual_agent': actual_agent,
                    'predicted_step': pred['predicted_step'],
                    'actual_step': actual_step,
                    'agent_correct': agent_correct,
                    'step_correct': step_correct
                })
            else:
                print(f"Skipping evaluation for {idx} due to issues reading actual data.")

        else:
            print(f"Warning: Labeled file not found for prediction key '{idx}': {labeled_file}")

    print("\n--- Evaluation Summary ---")
    print(f"Total reference files in data_path: {total_files}")
    print(f"Predictions parsed from eval file:  {len(predictions)}")
    print(f"Files evaluated (prediction found & actual data read): {files_evaluated}")
    print(f"Correct Agent Predictions: {correct_agent}")
    print(f"Correct Step Predictions:  {correct_step}")

    agent_accuracy = (correct_agent / total_files) * 100 if total_files > 0 else 0.0
    step_accuracy = (correct_step / total_files) * 100 if total_files > 0 else 0.0

    return agent_accuracy, step_accuracy, detailed_results

def generate_detailed_report(eval_file, data_path, agent_accuracy, step_accuracy, total_files, predictions, detailed_results):
    """Generate a comprehensive evaluation report"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report = f"""
=== FAILURE ATTRIBUTION EVALUATION REPORT ===
Generated: {timestamp}

--- Configuration ---
Evaluation File: {eval_file}
Data Path:       {data_path}
Total Reference Files: {total_files}
Predictions Parsed: {len(predictions)}
Files Successfully Evaluated: {len(detailed_results)}

--- Overall Results ---
Agent Accuracy: {agent_accuracy:.2f}% ({len([r for r in detailed_results if r['agent_correct']])}/{total_files})
Step Accuracy:  {step_accuracy:.2f}% ({len([r for r in detailed_results if r['step_correct']])}/{total_files})

--- Detailed Results ---
"""
    
    # Add detailed results for each file
    for result in detailed_results:
        agent_status = "✓" if result['agent_correct'] else "✗"
        step_status = "✓" if result['step_correct'] else "✗"
        
        report += f"""
File: {result['file']}
  Agent: {agent_status} Predicted: {result['predicted_agent']} | Actual: {result['actual_agent']}
  Step:  {step_status} Predicted: {result['predicted_step']} | Actual: {result['actual_step']}
"""
    
    # Add summary statistics
    agent_errors = [r for r in detailed_results if not r['agent_correct']]
    step_errors = [r for r in detailed_results if not r['step_correct']]
    
    report += f"""
--- Error Analysis ---
Agent Prediction Errors: {len(agent_errors)} files
Step Prediction Errors: {len(step_errors)} files
Both Correct: {len([r for r in detailed_results if r['agent_correct'] and r['step_correct']])} files
Both Incorrect: {len([r for r in detailed_results if not r['agent_correct'] and not r['step_correct']])} files

=== END OF REPORT ===
"""
    
    return report

def main():
    parser = argparse.ArgumentParser(description="Enhanced evaluation tool for agent and step prediction accuracy with detailed reporting.")
    parser.add_argument(
        "--data_path",
        type=str,
        default='../Who&When/Algorithm-Generated',
        help="Path to the directory containing the ground truth files."
    )
    parser.add_argument(
        "--eval_file",
        type=str,
        required=True,
        help="Path to the evaluation log file containing the predictions."
    )
    parser.add_argument(
        "--output_file",
        type=str,
        default=None,
        help="Optional: Path to save the detailed evaluation results. If not provided, results are only printed to console."
    )
    parser.add_argument(
        "--brief",
        action="store_true",
        help="Show only brief summary (same as original evaluate.py)"
    )
    args = parser.parse_args()
    
    data_path = args.data_path
    eval_file = args.eval_file

    if not os.path.isdir(data_path):
        print(f"Error: Data directory not found at {data_path}")
        actual_total_files = 0
    else:
        try:
            json_files_in_data_path = [
                f for f in os.listdir(data_path)
                if f.endswith('.json') and os.path.isfile(os.path.join(data_path, f))
            ]
            actual_total_files = len(json_files_in_data_path)
        except Exception as e:
            print(f"Error reading data directory {data_path}: {e}")
            actual_total_files = 0

    predictions = read_predictions(eval_file)

    agent_accuracy, step_accuracy, detailed_results = evaluate_accuracy(predictions, data_path, actual_total_files)

    if args.brief:
        # Brief output (same as original)
        print("\n--- Final Accuracy Results ---")
        print(f"Evaluation File: {eval_file}")
        print(f"Data Path:       {data_path}")
        print(f"Agent Accuracy: {agent_accuracy:.2f}%")
        print(f"Step Accuracy:  {step_accuracy:.2f}%")
        print(f"(Accuracy calculated based on {actual_total_files} total files in data path)")
    else:
        # Detailed report
        report = generate_detailed_report(eval_file, data_path, agent_accuracy, step_accuracy, 
                                        actual_total_files, predictions, detailed_results)
        print(report)
        
        # Save to file if output_file is specified
        if args.output_file:
            try:
                with open(args.output_file, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"\nDetailed report saved to: {args.output_file}")
            except Exception as e:
                print(f"Error saving report to {args.output_file}: {e}")

if __name__ == "__main__":
    main()
