absl-py==2.1.0
accelerate==1.2.1
aiohappyeyeballs==2.4.4
aiohttp==3.11.10
aiohttp-cors==0.7.0
aiosignal==1.3.2
airportsdata==20241001
alabaster==0.7.16
alembic==1.14.0
annotated-types==0.7.0
anyio==4.7.0
arrow==1.3.0
arxiv==2.1.3
asgiref==3.8.1
astor==0.8.1
astunparse==1.6.3
async-timeout==5.0.1
attrs==24.2.0
autogen==0.5.3
babel==2.16.0
backoff==2.2.1
banal==1.0.6
bcrypt==4.2.1
beautifulsoup4==4.12.3
bibtexparser==1.4.2
biopython==1.84
blake3==1.0.4
blessed==1.20.0
build==1.2.2.post1
cachetools==5.5.0
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.0
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.1.7
cloudpickle==3.1.1
colorama==0.4.6
coloredlogs==15.0.1
colorful==0.5.6
compressed-tensors==0.9.0
contexttimer==0.3.3
contourpy==1.3.0
cryptography==44.0.0
cycler==0.12.1
dataset==1.6.2
datasets==3.2.0
Deprecated==1.2.15
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
docker==7.1.0
docutils==0.21.2
docx==0.2.4
docx2txt==0.8
durationpy==0.9
easyocr==1.7.2
einops==0.8.0
et_xmlfile==2.0.0
exceptiongroup==1.2.2
fake-useragent==2.0.3
fastapi==0.115.6
feedparser==6.0.11
ffmpeg==1.4
ffmpeg-python==0.2.0
filelock==3.16.1
FLAML==2.3.2
flatbuffers==24.3.25
fonttools==4.55.3
free_proxy==1.1.3
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2024.10.0
future==1.0.0
gast==0.6.0
geographiclib==2.0
geopandas==1.0.1
geopy==2.4.1
gguf==0.10.0
gitdb==4.0.11
GitPython==3.1.43
google-api-core==2.24.0
google-auth==2.37.0
google-cloud-speech==2.29.0
google-pasta==0.2.0
googleapis-common-protos==1.66.0
googlemaps==4.10.0
gpustat==1.1.1
greenlet==3.1.1
grpcio==1.68.1
grpcio-status==1.68.1
h11==0.14.0
h5py==3.12.1
haversine==2.9.0
html5lib==1.1
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.26.5
humanfriendly==10.0
idna==3.10
imageio==2.36.1
imageio-ffmpeg==0.5.1
imagesize==1.4.1
importlib_metadata==8.5.0
importlib_resources==6.4.5
iniconfig==2.0.0
inquirerpy==0.3.4
interegular==0.3.3
Jinja2==3.1.4
jiter==0.8.2
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
keras==3.7.0
kiwisolver==1.4.7
kubernetes==31.0.0
langdetect==1.0.9
lark==1.2.2
lazy_loader==0.4
libclang==18.1.1
llvmlite==0.43.0
lm-format-enforcer==0.10.9
lxml==5.3.0
Mako==1.3.8
Markdown==3.7
markdown-it-py==3.0.0
markdownify==0.14.1
MarkupSafe==3.0.2
matplotlib==3.9.4
mdurl==0.1.2
meteostat==1.6.8
mistral_common==1.5.2
ml-dtypes==0.4.1
mmh3==5.0.1
monotonic==1.6
more-itertools==10.5.0
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.1.0
multiprocess==0.70.16
multitasking==0.0.11
namex==0.0.8
nest-asyncio==1.6.0
networkx==3.2.1
ninja==********
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.560.30
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
oauthlib==3.2.2
onnxruntime==1.19.2
openai==1.59.3
openai-whisper==20240930
opencensus==0.11.4
opencensus-context==0.1.3
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
opentelemetry-api==1.29.0
opentelemetry-exporter-otlp-proto-common==1.29.0
opentelemetry-exporter-otlp-proto-grpc==1.29.0
opentelemetry-instrumentation==0.50b0
opentelemetry-instrumentation-asgi==0.50b0
opentelemetry-instrumentation-fastapi==0.50b0
opentelemetry-proto==1.29.0
opentelemetry-sdk==1.29.0
opentelemetry-semantic-conventions==0.50b0
opentelemetry-util-http==0.50b0
opt_einsum==3.4.0
optree==0.13.1
orjson==3.10.12
osmnx==2.0.0
outcome==1.3.0.post0
outlines==0.1.11
outlines_core==0.1.26
overrides==7.7.0
packaging==24.2
pandas==2.2.3
partial-json-parser==0.2.1.1.post5
pdf2image==1.17.0
pdfminer.six==20231228
pdfplumber==0.11.4
peewee==3.17.8
pfzy==0.3.4
pillow==10.4.0
platformdirs==4.3.6
pluggy==1.5.0
posthog==3.7.4
prometheus-fastapi-instrumentator==7.0.2
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.2.1
proto-plus==1.25.0
protobuf==5.29.1
psutil==6.1.0
py-cpuinfo==9.0.0
py-spy==0.4.0
pyarrow==18.1.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
-e git+https://github.com/ag2ai/ag2@190a93dc676493cea5057c28dc35618703c02f83#egg=pyautogen
pybind11==2.13.6
pyclipper==1.3.0.post6
pycountry==24.6.1
pycparser==2.22
pydantic==2.10.3
pydantic_core==2.27.1
pydub==0.25.1
Pygments==2.18.0
PyMuPDF==1.25.1
pyogrio==0.10.0
pyparsing==3.2.0
PyPDF2==3.0.1
pypdfium2==4.30.0
PyPika==0.48.9
pyproj==3.6.1
pyproject_hooks==1.2.0
PySocks==1.7.1
pysqlite3==0.5.4
pytesseract==0.3.13
pytest==8.3.4
python-bidi==0.6.3
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-pptx==1.0.2
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
ray==2.41.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
rich==13.9.4
rpds-py==0.22.3
rsa==4.9
safetensors==0.4.5
scholarly==1.7.11
scikit-image==0.24.0
scikit-learn==1.6.0
scipy==1.13.1
seaborn==0.13.2
selenium==4.27.1
sentence-transformers==3.3.1
sentencepiece==0.2.0
sgmllib3k==1.0.0
shapely==2.0.6
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
smmap==5.0.1
sniffio==1.3.1
snowballstemmer==2.2.0
sortedcontainers==2.4.0
soundfile==0.12.1
soupsieve==2.6
SpeechRecognition==3.12.0
Sphinx==7.4.7
sphinx-rtd-theme==3.0.2
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
SQLAlchemy==1.4.54
starlette==0.41.3
sympy==1.13.1
tabula-py==2.10.0
tenacity==9.0.0
tensorboard==2.18.0
tensorboard-data-server==0.7.2
tensorflow==2.18.0
tensorflow-io-gcs-filesystem==0.37.1
termcolor==2.5.0
tf_keras==2.18.0
threadpoolctl==3.5.0
tifffile==2024.8.30
tiktoken==0.7.0
tokenizers==0.21.0
tomli==2.2.1
torch==2.5.1
torchaudio==2.5.1
torchvision==0.20.1
tqdm==4.67.1
transformers==4.47.1
trio==0.27.0
trio-websocket==0.11.1
triton==3.1.0
typer==0.15.1
types-python-dateutil==2.9.0.20241206
typing_extensions==4.12.2
tzdata==2024.2
ultralytics==8.3.51
ultralytics-thop==2.0.13
urllib3==2.2.3
uvicorn==0.34.0
uvloop==0.21.0
virtualenv==20.29.1
vllm==0.7.0
watchfiles==1.0.3
waybackpy==3.0.6
wcwidth==0.2.13
webencodings==0.5.1
websocket-client==1.8.0
websockets==14.1
Werkzeug==3.1.3
wikipedia==1.4.0
Wikipedia-API==0.7.1
wrapt==1.17.0
wsproto==1.2.0
xformers==0.0.28.post3
xgrammar==0.1.11
XlsxWriter==3.2.0
xxhash==3.5.0
yarl==1.18.3
yfinance==0.2.51
youtube-dl==2021.12.17
yt-dlp==2024.12.13
zipp==3.21.0
