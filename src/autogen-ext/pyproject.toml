[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "autogen-ext"
version = "0.6.4"
license = {file = "LICENSE-CODE"}
description = "AutoGen extensions library"
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "autogen-core==0.6.4",
]

[project.optional-dependencies]
anthropic = ["anthropic>=0.48"]
langchain = ["langchain_core~= 0.3.3"]
azure = [
    "azure-ai-inference>=1.0.0b9",
    "azure-ai-projects>=1.0.0b11",
    "azure-core",
    "azure-identity",
    "azure-search-documents>=11.4.0",
]
docker = ["docker~=7.0", "asyncio_atexit>=1.0.1"]
ollama = ["ollama>=0.4.7", "tiktoken>=0.8.0"]
openai = ["openai>=1.66.5", "tiktoken>=0.8.0", "aiofiles"]
file-surfer = [
    "autogen-agentchat==0.6.4",
    "magika>=0.6.1rc2",
    "markitdown[all]~=0.1.0a3",
]

llama-cpp = [
    "llama-cpp-python>=0.3.8",
]

graphrag = ["graphrag>=1.0.1"]
chromadb = ["chromadb>=1.0.0"]
mem0 = ["mem0ai>=0.1.98"]
mem0-local = [
    "mem0ai>=0.1.98",
    "neo4j>=5.25.0",
    "chromadb>=1.0.0"
]
web-surfer = [
    "autogen-agentchat==0.6.4",
    "playwright>=1.48.0",
    "pillow>=11.0.0",
    "magika>=0.6.1rc2",
    "markitdown[all]~=0.1.0a3",
]
magentic-one = [
    "autogen-agentchat==0.6.4",
    "magika>=0.6.1rc2",
    "markitdown[all]~=0.1.0a3",
    "playwright>=1.48.0",
    "pillow>=11.0.0",
]
video-surfer = [
    "autogen-agentchat==0.6.4",
    "opencv-python>=4.5",
    "ffmpeg-python",
    "openai-whisper",
]
diskcache = [
    "diskcache>=5.6.3"
]
redis = [
    "redis>=5.2.1"
]

grpc = [
    "grpcio~=1.70.0",
]

jupyter-executor = [
    "ipykernel>=6.29.5",
    "nbclient>=0.10.2",
]

docker-jupyter-executor = [
    "docker~=7.0",
    "asyncio_atexit>=1.0.1",
    "websockets>=15.0.1",
    "requests>=2.32.3",
    "aiohttp>=3.11.16",
]

task-centric-memory = ["chromadb>=1.0.0"]

semantic-kernel-core = [
    "semantic-kernel>=1.17.1",
]

gemini = [
    "google-genai>=1.0.0",
]

semantic-kernel-google = [
    "semantic-kernel[google]>=1.17.1",
]

semantic-kernel-hugging-face = [
    "semantic-kernel[hugging_face]>=1.17.1",
]

semantic-kernel-mistralai = [
    "semantic-kernel[mistralai]>=1.17.1",
]

semantic-kernel-ollama = [
    "semantic-kernel[ollama]>=1.17.1",
]

semantic-kernel-onnx = [
    "semantic-kernel[onnx]>=1.17.1",
]

semantic-kernel-anthropic = [
    "semantic-kernel[anthropic]>=1.17.1",
]

semantic-kernel-pandas = [
    "semantic-kernel[pandas]>=1.17.1",
]

semantic-kernel-aws = [
    "semantic-kernel[aws]>=1.17.1",
]

semantic-kernel-dapr = [
    "semantic-kernel[dapr]>=1.17.1",
]

http-tool = [
    "httpx>=0.27.0",
    "json-schema-to-pydantic>=0.2.0"
]

semantic-kernel-all = [
    "semantic-kernel[google,hugging_face,mistralai,ollama,onnx,anthropic,usearch,pandas,aws,dapr]>=1.17.1",
]

rich = ["rich>=13.9.4"]

mcp = ["mcp>=1.11.0"]
canvas = [
    "unidiff>=0.7.5",
]

[tool.hatch.build.targets.wheel]
packages = ["src/autogen_ext"]

[dependency-groups]
dev = [
    "autogen_test_utils",
    "langchain-experimental",
    "pandas-stubs>=2.2.3.241126",
    "httpx>=0.28.1",
    "opentelemetry-proto>=1.28.0"
]

[tool.ruff]
extend = "../../pyproject.toml"
include = ["src/**", "tests/*.py"]
exclude = ["src/autogen_ext/agents/web_surfer/*.js", "src/autogen_ext/runtimes/grpc/protos", "tests/protos", "README.md"]

[tool.pyright]
extends = "../../pyproject.toml"
include = ["src", "tests"]
exclude = ["src/autogen_ext/runtimes/grpc/protos", "tests/protos", "src/autogen_ext/agents/openai/_openai_assistant_agent.py", "tests/test_openai_assistant_agent.py"]

[tool.pytest.ini_options]
minversion = "6.0"
testpaths = ["tests"]
markers = [
    "grpc",
]

[tool.poe]
include = "../../shared_tasks.toml"

[tool.poe.tasks]
test.sequence = [
    "playwright install",
    "pytest -n 1 --cov=src --cov-report=term-missing --cov-report=xml",
]
test.default_item_type = "cmd"
test-grpc = "pytest -n 1 --cov=src --cov-report=term-missing --cov-report=xml --grpc"
test-windows = "pytest -n 1 --cov=src --cov-report=term-missing --cov-report=xml -m 'windows'"
mypy = "mypy --config-file ../../pyproject.toml --exclude src/autogen_ext/runtimes/grpc/protos --exclude tests/protos --exclude src/autogen_ext/agents/openai/_openai_assistant_agent.py --exclude tests/test_openai_assistant_agent.py  --ignore-missing-imports src tests"

[tool.mypy]
[[tool.mypy.overrides]]
module = "docker.*"
ignore_missing_imports = true
