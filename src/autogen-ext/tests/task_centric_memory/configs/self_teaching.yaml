
PageLogger:
  level: DEBUG  # DEBUG, INFO, WARNING, ERROR, CRITICAL, or NONE.
  path: ./tests/task_centric_memory/pagelogs/self_teaching

client:
  model: gpt-4o-2024-08-06
  temperature: 0.8
  max_completion_tokens: 4096
  presence_penalty: 0.0
  frequency_penalty: 0.0
  top_p: 1.0
  max_retries: 65535

Apprentice:
  name_of_agent_or_team: AssistantAgent  # AssistantAgent or MagenticOneGroupChat
  disable_prefix_caching: 0  # If true, prepends a small random string to the context, to decorrelate repeated runs.
  MemoryController:
    max_train_trials: 2
    max_test_trials: 1
    MemoryBank:
      path: ./tests/task_centric_memory/memory_bank/self_teaching
      relevance_conversion_threshold: 1.7
      n_results: 25
      distance_threshold: 100

test:
  task_file_1: tests/task_centric_memory/data_files/tasks/10_liars.yaml  # Train and test on this task.
  task_file_2: tests/task_centric_memory/data_files/tasks/100_vampires.yaml  # Test generalization on this different, similar task.
  num_loops: 1
  num_final_test_trials: 1
