# Demo created manually for solving the cell_towers_2 task.

insight: 'Sort the houses by location:  3, 6, 10, 11, 12, 17, 19, 20.
  Then start at one end and place the towers only where absolutely needed.
  The house at 3 could be served by a tower as far away as mile marker 7, because 3 + 4 = 7, so place a tower at 7.
  This obviously covers houses up to mile 7.
  But a coverage radius of 4 miles (in each direction) means a total coverage of 8 miles.
  So the tower at mile 7 would reach all the way to mile 11, covering the houses at 10 and 11.
  The next uncovered house would be at mile 12 (not 10), requiring a second tower.
  It could go at mile 16 (which is 12 + 4) and this tower would reach up to mile 20 (16 + 4),
  covering the remaining houses. So 2 towers would be enough.'
