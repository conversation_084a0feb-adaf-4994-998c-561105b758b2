#!/usr/bin/env python3
"""
Debug Log Utilities for Magnetic-One Test Script (M1)
====================================================

This module contains utility functions for DEBUG-level logging that captures
detailed internal execution traces and saves them to generated/debug/ directory.
"""

import logging
from pathlib import Path
from datetime import datetime, timezone


def save_raw_debug_log(scenario_id: str, endpoint_type: str, model: str, output_dir: Path, run_timestamp: str = None) -> Path:
    """
    设置DEBUG级别的内部流程日志记录，保存详细的执行轨迹
    
    与原有的INFO级别日志不同，这个函数专门用于捕获DEBUG级别的内部信息，
    保存到 generated/debug/ 目录下，文件后缀为 _debug.log
    
    Args:
        scenario_id: 场景ID (如 "1")
        endpoint_type: 端点类型 (如 "ollama", "openai", "cloudgpt")
        model: 模型名称 (如 "llama3.1", "gpt-4o-mini-20240718")
        output_dir: 输出目录路径
        run_timestamp: 运行时间戳
        
    Returns:
        Path: DEBUG日志文件的路径
    """
    # 创建debug专用目录
    debug_dir = output_dir / "debug"
    debug_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建DEBUG日志文件路径，后缀为 _debug.log
    if run_timestamp:
        debug_log_path = debug_dir / f"scenario_{scenario_id}_{endpoint_type}_{model}_{run_timestamp}_debug.log"
    else:
        debug_log_path = debug_dir / f"scenario_{scenario_id}_{endpoint_type}_{model}_debug.log"
    
    # 初始化DEBUG日志文件
    start_time = datetime.now(timezone.utc)
    start_msg = f"=== Started DEBUG logging for scenario {scenario_id} with {endpoint_type} at {start_time.strftime('%H:%M:%S')} UTC ===\n"
    start_msg += f"=== Model: {model} ===\n"
    start_msg += f"=== This log contains DEBUG-level internal execution traces ===\n"
    start_msg += f"=== Log level: DEBUG (more detailed than INFO) ===\n\n"
    
    # 写入初始化信息（覆盖模式，确保每次任务都是新的日志）
    debug_log_path.write_text(start_msg, encoding="utf-8")
    
    # 配置DEBUG级别的日志记录
    # 创建简单的格式化器，只保存消息内容
    class SimpleFormatter(logging.Formatter):
        def format(self, record):
            # 直接返回消息内容，不添加时间戳
            return str(record.msg)
    
    # 清除现有的handlers，避免重复配置
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 配置DEBUG级别的日志记录
    handler = logging.FileHandler(debug_log_path, mode='a', encoding='utf-8')
    handler.setLevel(logging.DEBUG)  # 设置为DEBUG级别
    handler.setFormatter(SimpleFormatter())
    
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)  # 设置为DEBUG级别
    logger.addHandler(handler)
    
    # 确保autogen事件被记录为DEBUG级别
    # 这会捕获所有内部调用，包括更详细的执行信息
    logging.getLogger("autogen_core.events").setLevel(logging.DEBUG)
    logging.getLogger("autogen_ext").setLevel(logging.DEBUG)
    logging.getLogger("autogen_core").setLevel(logging.DEBUG)
    
    print(f"✓ DEBUG级别日志已初始化，将保存到: {debug_log_path}")
    print(f"✓ 将记录DEBUG级别的内部执行轨迹和详细信息")
    print(f"✓ 日志级别从INFO提升到DEBUG，捕获更多内部流程")
    print(f"✓ 每次运行都会创建新的DEBUG日志文件")
    
    return debug_log_path


def finalize_debug_log(debug_log_path: Path, total_steps: int, duration: str) -> None:
    """
    在任务完成时添加结束标记到DEBUG日志文件
    
    Args:
        debug_log_path: DEBUG日志文件路径
        total_steps: 总步数
        duration: 执行时长
    """
    end_time = datetime.now(timezone.utc)
    end_msg = f"\n=== Completed DEBUG logging at {end_time.strftime('%H:%M:%S')} UTC ===\n"
    end_msg += f"=== Total steps: {total_steps} | Duration: {duration} ===\n"
    end_msg += f"=== End of DEBUG log ===\n"
    
    # 追加结束信息
    with debug_log_path.open("a", encoding="utf-8") as f:
        f.write(end_msg)
    
    print(f"✓ DEBUG日志记录完成，共记录 {total_steps} 个步骤，耗时 {duration}")
    print(f"✓ DEBUG日志保存在: {debug_log_path}")
