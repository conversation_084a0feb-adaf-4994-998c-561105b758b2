#!/usr/bin/env python3
"""
Utils Package for Magnetic-One Test Script (M1)
==============================================

This package contains utility functions for logging and data processing 
used by the Magnetic-One test script.

The package is organized as follows:
- log_format.py: Main utility functions for LLM logging and formatting
- internal_helpers.py: Internal helper functions (prefixed with underscore)
- utils.py: Backup of the original utils.py file

For backward compatibility, all main functions are exported at the package level.
"""

# Import main utility functions from log_format module
from .log_format import (
    setup_llm_logging,
    format_llm_calls_log,
    format_complete_log,
    finalize_llm_logging
)

# Import debug logging functions
from .debug_log import (
    save_raw_debug_log,
    finalize_debug_log
)

# Export all main functions for backward compatibility
__all__ = [
    'setup_llm_logging',
    'format_llm_calls_log',
    'format_complete_log',
    'finalize_llm_logging',
    'save_raw_debug_log',
    'finalize_debug_log'
]

# Version information
__version__ = '1.0.0'
__author__ = 'Magnetic-One Test Script Team'
