#!/usr/bin/env python3
"""
Internal Helper Functions for Log Format Utilities
=================================================

This module contains internal helper functions used by the log format utilities.
These functions are prefixed with underscore to indicate they are internal.
"""

from pathlib import Path


def _get_format_from_endpoint_type(endpoint_type: str) -> str:
    """
    根据endpoint_type确定LLM调用日志的格式类型

    Args:
        endpoint_type: 端点类型 ("ollama", "openai", "cloudgpt" 等)

    Returns:
        str: 格式类型 ("ollama" 或 "openai")
    """
    if endpoint_type.lower() in ['openai', 'cloudgpt']:
        return 'openai'
    else:
        return 'ollama'


def _format_ollama_messages(messages: list) -> list:
    """格式化Ollama格式的消息"""
    formatted_messages = []
    for i, msg in enumerate(messages, 1):
        formatted_messages.append({
            "message_index": i,
            "role": msg.get('role', 'unknown'),
            "content": msg.get('content', ''),
            "thinking": msg.get('thinking'),
            "images": msg.get('images'),
            "tool_calls": msg.get('tool_calls')
        })
    return formatted_messages


def _format_openai_messages(messages: list) -> list:
    """格式化OpenAI格式的消息"""
    formatted_messages = []
    for i, msg in enumerate(messages, 1):
        formatted_messages.append({
            "message_index": i,
            "role": msg.get('role', 'unknown'),
            "name": msg.get('name'),
            "content": msg.get('content', '')
        })
    return formatted_messages


def _format_ollama_response(response: dict) -> dict:
    """格式化Ollama格式的响应"""
    return {
        "model": response.get('model'),
        "created_at": response.get('created_at'),
        "done": response.get('done'),
        "done_reason": response.get('done_reason'),
        "total_duration": response.get('total_duration'),
        "load_duration": response.get('load_duration'),
        "prompt_eval_count": response.get('prompt_eval_count'),
        "prompt_eval_duration": response.get('prompt_eval_duration'),
        "eval_count": response.get('eval_count'),
        "eval_duration": response.get('eval_duration'),
        "message": response.get('message', {})
    }


def _format_openai_response(response: dict) -> dict:
    """格式化OpenAI格式的响应"""
    return {
        "id": response.get('id'),
        "choices": response.get('choices', []),
        "created": response.get('created'),
        "model": response.get('model'),
        "object": response.get('object'),
        "service_tier": response.get('service_tier'),
        "system_fingerprint": response.get('system_fingerprint'),
        "usage": response.get('usage', {})
    }


def _extract_timestamp_ollama(response: dict) -> str:
    """从Ollama响应中提取时间戳"""
    return response.get('created_at')


def _extract_timestamp_openai(response: dict) -> str:
    """从OpenAI响应中提取时间戳"""
    created = response.get('created')
    if created:
        # OpenAI的created是Unix时间戳，转换为ISO格式
        from datetime import datetime, timezone
        try:
            dt = datetime.fromtimestamp(created, tz=timezone.utc)
            return dt.isoformat()
        except:
            return str(created)
    return None


def _extract_endpoint_type_from_filename(llm_log_path: Path) -> str:
    """
    从日志文件名中提取endpoint_type

    Args:
        llm_log_path: 日志文件路径

    Returns:
        str: endpoint_type (如 "ollama", "openai", "cloudgpt")
    """
    # 文件名格式: scenario_{id}_{endpoint_type}_{model}_{timestamp}_llm_calls.log
    # 或: scenario_{id}_{endpoint_type}_{model}_llm_calls.log
    filename = llm_log_path.stem
    parts = filename.split('_')

    # 寻找endpoint_type，通常在第二个位置
    if len(parts) >= 3:
        # 检查常见的endpoint类型
        for part in parts[1:4]:  # 检查可能的位置
            if part.lower() in ['ollama', 'openai', 'cloudgpt']:
                return part.lower()

    # 默认返回ollama
    return 'ollama'
