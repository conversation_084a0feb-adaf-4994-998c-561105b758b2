# Title: Reproducing and Debugging Hand-Crafted Failures in Multi-Agent System

---

## Objective:

The goal of this 3-day offline task is to reproduce and understand hand-crafted failure traces from the [Who & When dataset](https://github.com/mingyin1/Agents_Failure_Attribution/tree/main/Who%26When/Hand-Crafted) using the original **Magnetic-One** multi-agent system. You will go through a structured process to run the system end-to-end, reproduce a specific failure snapshot, and attempt to manually fix the failure. This tests your ability to work with complex systems, analyze multi-agent failures, and apply manual debugging strategies.

---

## Milestones:

### 🔵 Milestone 1: Setup & End-to-End Query Execution (Day 1)

**Goal:** Set up the Magnetic-One system and run a full user query from the dataset, even if the generated trace differs from the original failure trace.

**Tasks:**

- Set up the Magnetic-One environment (use <PERSON><PERSON> or <PERSON><PERSON> as needed)
- Choose one scenario from the Hand-Crafted dataset
- Parse the user query from the dataset trace (JSON format)
- Run that query through Magnetic-One to generate a full end-to-end trace
- Save and organize the resulting trace for comparison

**Expected Outcome:**

- You can take a dataset query and run it through Magnetic-One successfully
- Logs are generated and stored, even if they don’t exactly match the dataset’s failure trace

**Deliverables:**

- A working setup (`setup/`) with instructions
- The selected query + your generated trace logs (`logs/generated/`)
- Brief notes (`milestone1_report.md`) on:
  - Which query you selected
  - Any differences noticed in the generated trace vs. dataset reference

---

### 🔹 Milestone 2: Reproducing a Failure Snapshot (Day 2)

**Goal:** Use a *snapshot* (partial trace) from the dataset to launch Magnetic-One into the failure state described in the dataset.

**Tasks:**

- Parse the provided snapshot from the hand-crafted failure trace (initial agent state, partial logs, etc.)
- Modify the Magnetic-One configuration to start the system from this snapshot or inject intermediate states as needed
- Run the system and confirm the failure occurs at the expected step
- Compare your logs to the dataset trace to validate structural match

**Expected Outcome:**

- You can successfully start Magnetic-One from a mid-trace failure state
- The resulting behavior closely replicates the failure as described

**Deliverables:**

- Scripts or configuration used to inject the snapshot (`scripts/restore_<scenario>.sh`)
- Logs of your reproduced run (`logs/reproduced/`)
- A comparison summary (`milestone2_report.md`) explaining:
  - How your run compares to the original failure trace
  - How close the reproduction is

---

### 🔶 Milestone 3: Manual Debugging & Fix (Day 3)

**Goal:** Identify the failing step in the trace and manually fix it (e.g., by modifying agent actions or timing) so that the failure no longer occurs when rerun.

**Tasks:**

- Identify the exact step that causes the failure. Feel free to leverage the ground-truth annotations.
- Modify the agent code/config/planner to avoid or resolve the failure
- Re-run the fixed scenario from the same snapshot
- Confirm the failure is resolved and the task completes successfully

**Expected Outcome:**

- You can isolate and fix the original problem in the trace
- The rerun produces a successful or altered outcome as expected

**Deliverables:**

- Updated scripts/config used for the fix (`scripts/fix_<scenario>.sh`)
- Logs of the fixed run (`logs/fixed/`)
- Debugging notes (`milestone3_report.md`) including:
  - What caused the failure
  - What fix you applied
  - Evidence that it resolved the issue

---

## Submission:
Submit a zip file containing all code, scripts, logs, and analysis notes used throughout the assignment.
