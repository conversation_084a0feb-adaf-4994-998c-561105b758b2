# Day 1: Environment Setup

This directory contains the setup instructions for Day 1 of the Multi-Agent Failure Attribution Task.

## Quick Setup

### Step 1: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Clone Dataset
```bash
git clone https://github.com/mingyin1/Agents_Failure_Attribution.git
```

### Step 3: Set Environment Variables
```bash
export OPENAI_API_KEY="your-openai-api-key-here"
```

### Step 4: Run Day 1
```bash
cd ../scripts
python day1_end_to_end_execution.py --scenario 1
```

## Requirements

- Python 3.10+
- OpenAI API key
- Git

## Files

- `requirements.txt` - Python dependencies for Day 1
- `README.md` - This setup guide 