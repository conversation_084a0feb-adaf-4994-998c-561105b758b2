#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
day3_failure_fix.py
~~~~~~~~~~~~~~~~~~
Fix failure scenarios based on Day 2 

Fix Strategy:
- Start from failure snapshot, but don't inject error actions
- Let system re-reason, should get correct answer

Usage:
    python day3_failure_fix.py --scenario 1 --model gpt-4o
"""

import os, re, json, argparse, asyncio
from pathlib import Path
from typing import Any, List, Dict
from datetime import datetime

# --------------------------- AutoGen -----------------------------------------
from autogen_agentchat.messages import TextMessage as Message
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne

# --------------------------- code-executor stub ------------------------------
class DummyCodeExecutor:          # Satisfy Magentic-One interface requirements
    async def start(self):        # Do nothing
        pass
    async def stop(self):
        pass
    async def execute_code_blocks(self, code_blocks, cancellation_token=None):
        """Simulate code execution, return empty results"""
        return []

# --------------------------- API Setup ----------------------------------------
API_KEY = os.getenv("OPENAI_API_KEY")
if not API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable must be set")
API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")

# --------------------------- Paths -------------------------------------------
DATA_DIR = Path("../Agents_Failure_Attribution/Who&When/Hand-Crafted")
OUT_DIR  = Path("../logs/fixed")

# --------------------------- Tools -------------------------------------------
ROLE_MAP = {"human": "User", "assistant": "Assistant"}
_drop = re.compile(r"<Image>|Automatic OCR|meta_tags", re.I)

def clean(t:str, k:int=3000)->str: return "" if _drop.search(t) else t[:k]

def is_error_action(content: str) -> bool:
    """Determine if it's an error action (clicking ads)"""
    error_indicators = [
        "click ad", "click advertisement", 
        "advertisement", "banner", "popup"
    ]
    content_lower = content.lower()
    return any(indicator in content_lower for indicator in error_indicators)

# --------------------------- Message Injection Tools -----------------------------------
async def feed(agent: MagenticOne, msg_dict: Dict[str, Any]) -> None:
    """Inject historical messages into runtime (adapted for TextMessage)"""
    role    = msg_dict.get("role", "assistant")     # Role from dataset
    content = msg_dict.get("content", "")
    
    # Skip error actions (click ads)
    if is_error_action(content):
        print(f"[SKIP_ERROR] Skipping error action: {content[:50]}...")
        return
    
    # TextMessage needs source field
    m = Message(source=role, content=content)       
    
    # Try different methods to inject messages
    try:
        if hasattr(agent, "_runtime") and hasattr(agent._runtime, "publish"):
            await agent._runtime.publish(m)
        elif hasattr(agent, "runtime") and hasattr(agent.runtime, "publish"):
            await agent.runtime.publish(m)
        elif hasattr(agent, "send"):
            await agent.send(m)
        else:
            # If all methods fail, skip injection and just log
            print(f"[SKIP] Unable to inject message: {role}")
    except Exception as e:
        print(f"[WARNING] Message injection failed: {e}")
        # Continue execution, don't interrupt program

# --------------------------- Fix Failure Scenario ----------------------------------
async def fix_failure_scenario(sid:str, model:str):
    """Fix failure scenario: Don't inject error actions, let system re-reason"""
    snap_path = DATA_DIR / f"{sid}.json"
    snap:dict[str,Any] = json.loads(snap_path.read_text(encoding="utf-8"))
    
    # Get failure step from snapshot
    mistake_step = int(snap.get("mistake_step", len(snap["history"])))
    
    print(f"\n[INFO] Starting failure scenario fix")
    print(f"[INFO] Failure step: {mistake_step} (total {len(snap['history'])} steps)")
    print(f"[INFO] Failure reason: {snap.get('mistake_reason', 'unknown')}")
    print(f"[INFO] Failed agent: {snap.get('mistake_agent', 'unknown')}")
    print(f"[INFO] Fix strategy: Skip error actions (click ads), let system re-reason")
    
    # Initialize client
    client = OpenAIChatCompletionClient(
        model=model,
        api_key=API_KEY,
        base_url=API_BASE or None,
        temperature=0,
        model_extras={"max_tokens":256},
    )
    
    # Initialize MagenticOne, ignore warnings
    import warnings
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        # Don't use config parameter, pass client directly
        m1 = MagenticOne(client=client, code_executor=DummyCodeExecutor())

    # Inject historical messages to failure point, but skip error actions
    print(f"\n[INFO] Injecting historical messages to step {mistake_step} (skipping error actions)")
    injected_count = 0
    skipped_count = 0
    
    for i, msg in enumerate(snap["history"][:mistake_step]):
        if is_error_action(msg.get("content", "")):
            print(f"[SKIP] Skipping error action [{msg['role']}]: {msg['content'][:50]}...")
            skipped_count += 1
            continue
            
        await feed(m1, msg)
        print(f"[INJECT] [{msg['role']}] {str(msg['content'])[:80].replace(chr(10),' ')} …")
        injected_count += 1
    
    print(f"\n[INFO] Injection complete: {injected_count} messages injected, {skipped_count} error actions skipped")
    
    # Continue execution from injection point
    print(f"\n[INFO] Now continuing execution, let system re-reason...")
    fixed_history = []
    
    # First add injected messages
    for msg in snap["history"][:mistake_step]:
        if not is_error_action(msg.get("content", "")):
            fixed_history.append(msg)
    
    try:
        # Continue execution, start reasoning from injection point
        async for msg in m1.run_stream(task=snap["question"]):
            role = getattr(msg, "role", getattr(msg, "source", "assistant"))
            content = getattr(msg, "content", "")
            print(f"[CONTINUE] [{role}] {str(content)[:110].replace(chr(10),' ')} …")
            fixed_history.append({"content": str(content), "role": role})
            
            # Stop execution if task result is encountered
            if getattr(msg, "type", "") == "task_result":
                print(f"\n[INFO] Task result encountered, stopping execution")
                break
                
    except Exception as e:
        print(f"\n[ERROR] Error during continued execution: {e}")
    
    return fixed_history, snap, mistake_step-1

def save_fixed_trace(sid, hist, snap, fail_step):
    """Save the fixed trace"""
    OUT_DIR.mkdir(parents=True, exist_ok=True)
    out = {
        "history"              : hist,
        "question"             : snap["question"],
        "ground_truth"         : snap.get("ground_truth",""),
        "original_failure_step": fail_step + 1,
        "fix_method"           : "skip_error_actions",
        "mistake_agent"        : snap.get("mistake_agent",""),
        "mistake_step"         : snap.get("mistake_step",""),
        "mistake_reason"       : snap.get("mistake_reason",""),
        "fixed_at"             : datetime.utcnow().isoformat(),
        "fix_description"      : "Skip error actions (click ads), let system re-reason to get correct answer"
    }
    p = OUT_DIR / f"scenario_{sid}_fixed.json"
    p.write_text(json.dumps(out, indent=2, ensure_ascii=False), encoding="utf-8")
    print(f"\n✓ Fixed trace saved → {p}")
    print(f"Trace length: {len(hist)} (original: {len(snap['history'])})")

def compare_with_original(sid):
    """Compare fixed trace with original trace"""
    try:
        # Read original trace
        orig_path = DATA_DIR / f"{sid}.json"
        orig_data = json.loads(orig_path.read_text(encoding="utf-8"))
        
        # Read fixed trace
        fixed_path = OUT_DIR / f"scenario_{sid}_fixed.json"
        fixed_data = json.loads(fixed_path.read_text(encoding="utf-8"))
        
        print(f"\n[COMPARISON] Fix effectiveness analysis:")
        print(f"Original trace length: {len(orig_data['history'])}")
        print(f"Fixed trace length: {len(fixed_data['history'])}")
        print(f"Fix method: {fixed_data['fix_description']}")
        
        # Check if contains error actions
        orig_has_error = any(is_error_action(msg.get("content", "")) 
                           for msg in orig_data["history"])
        fixed_has_error = any(is_error_action(msg.get("content", "")) 
                            for msg in fixed_data["history"])
        
        print(f"Original trace contains error actions: {orig_has_error}")
        print(f"Fixed trace contains error actions: {fixed_has_error}")
        
        if not fixed_has_error and orig_has_error:
            print("✓ Fix successful: Error actions have been removed")
        else:
            print("⚠ Fix effectiveness needs further verification")
            
    except Exception as e:
        print(f"[ERROR] Comparison analysis failed: {e}")

# --------------------------- CLI --------------------------------------------
async def main_async():
    pa = argparse.ArgumentParser(description="Tool for fixing failure scenarios")
    pa.add_argument("--scenario", required=True, help="Scenario ID, e.g. 1 or 6e3b...")
    pa.add_argument("--model", default="gpt-4o", help="Model to use")
    args = pa.parse_args()

    # Fix failure scenario
    hist, snap, fs = await fix_failure_scenario(args.scenario, args.model)
    
    # Save fixed trace
    save_fixed_trace(args.scenario, hist, snap, fs)
    
    # Compare analysis
    compare_with_original(args.scenario)

if __name__ == "__main__":
    asyncio.run(main_async()) 