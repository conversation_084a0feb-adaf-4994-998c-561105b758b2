#!/bin/bash

# Day 3: Failure Fix Script for Scenario 1
# This script fixes the failure scenario by skipping error actions

echo "=== Day 3: Failure Fix (Scenario 1) ==="
echo "Fixing failure scenario by skipping error actions..."

# Set scenario ID
SCENARIO_ID="1"
MODEL=${1:-"gpt-4o"}

echo "Scenario ID: $SCENARIO_ID"
echo "Model: $MODEL"
echo ""

# Check if the scenario file exists
SCENARIO_FILE="../Agents_Failure_Attribution/Who&When/Hand-Crafted/${SCENARIO_ID}.json"
if [ ! -f "$SCENARIO_FILE" ]; then
    echo "Error: Scenario file not found: $SCENARIO_FILE"
    exit 1
fi

echo "Found scenario file: $SCENARIO_FILE"
echo ""

# Create output directory
mkdir -p ../logs/fixed

# Run the failure fix script
echo "Running failure fix..."
python day3_failure_fix.py --scenario $SCENARIO_ID --model $MODEL

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Day 3 execution completed successfully!"
    echo "Output saved to: ../logs/fixed/scenario_${SCENARIO_ID}_fixed.json"
    
    # Show comparison summary
    echo ""
    echo "=== Fix Summary ==="
    echo "Original failure: ../Agents_Failure_Attribution/Who&When/Hand-Crafted/${SCENARIO_ID}.json"
    echo "Fixed execution: ../logs/fixed/scenario_${SCENARIO_ID}_fixed.json"
    echo ""
    echo "Check the milestone3_report.md for detailed analysis."
else
    echo ""
    echo "❌ Day 3 execution failed!"
    exit 1
fi

echo ""
echo "=== Day 3 Complete ===" 