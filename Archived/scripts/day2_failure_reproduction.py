#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
day2_failure_reproduction.py
~~~~~~~~~~~~~~~~~~~~~~~~~~
Restore from specific failure snapshot to exactly reproduce failure scenarios, including error operations

Usage:
    python day2_failure_reproduction.py --scenario 1 --model gpt-3.5-turbo
"""

import os, re, json, argparse, asyncio
from pathlib import Path
from typing import Any, List, Dict
from datetime import datetime

# --------------------------- AutoGen -----------------------------------------
from autogen_agentchat.messages import TextMessage as Message
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne

# --------------------------- code-executor stub ------------------------------
class DummyCodeExecutor:          
    async def start(self):        
        pass
    async def stop(self):
        pass
    async def execute_code_blocks(self, code_blocks, cancellation_token=None):
        """Simulate code execution, return empty results"""
        return []

# --------------------------- API Setup ----------------------------------------
API_KEY = os.getenv("OPENAI_API_KEY")
if not API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable must be set")
API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")

# --------------------------- Paths -------------------------------------------
DATA_DIR = Path("../Agents_Failure_Attribution/Who&When/Hand-Crafted")
OUT_DIR  = Path("../logs/reproduced")

# --------------------------- Tools -------------------------------------------
ROLE_MAP = {"human": "User", "assistant": "Assistant"}
_drop = re.compile(r"<Image>|Automatic OCR|meta_tags", re.I)

def clean(t:str, k:int=3000)->str: return "" if _drop.search(t) else t[:k]

# --------------------------- Message Injection Tools -----------------------------------
async def feed(agent: MagenticOne, msg_dict: Dict[str, Any]) -> None:
    """Inject historical messages into runtime (adapt to TextMessage)"""
    role    = msg_dict.get("role", "assistant")     # Role from dataset
    content = msg_dict.get("content", "")
    
    m = Message(source=role, content=content)       

    try:
        if hasattr(agent, "_runtime") and hasattr(agent._runtime, "publish"):
            await agent._runtime.publish(m)
        elif hasattr(agent, "runtime") and hasattr(agent.runtime, "publish"):
            await agent.runtime.publish(m)
        elif hasattr(agent, "send"):
            await agent.send(m)
        else:
            # If none work, skip injection and just print log
            print(f"[SKIP] Unable to inject message: {role}")
    except Exception as e:
        print(f"[WARNING] Message injection failed: {e}")
        # Continue execution, don't interrupt program

# --------------------------- Exact Recovery from Snapshot ----------------------------------
async def restore_from_snapshot_exact(sid:str, model:str):
    """Exact recovery from snapshot, reproduce failure scenarios"""
    snap_path = DATA_DIR / f"{sid}.json"
    snap:dict[str,Any] = json.loads(snap_path.read_text(encoding="utf-8"))
    
    # Get failure step from snapshot
    mistake_step = int(snap.get("mistake_step", len(snap["history"])))
    
    print(f"\n[INFO] Exact recovery from snapshot")
    print(f"[INFO] Failure step: {mistake_step} (total {len(snap['history'])} steps)")
    print(f"[INFO] Failure reason: {snap.get('mistake_reason', 'Unknown')}")
    print(f"[INFO] Failure agent: {snap.get('mistake_agent', 'Unknown')}")
    
    # Initialize client
    client = OpenAIChatCompletionClient(
        model=model,
        api_key=API_KEY,
        base_url=API_BASE or None,
        temperature=0,
        model_extras={"max_tokens":256},
    )
    
    # Initialize MagenticOne, ignore warnings
    import warnings
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        # Don't use config parameter, pass client directly
        m1 = MagenticOne(client=client, code_executor=DummyCodeExecutor())

    # Inject historical messages to failure point (including error operations)
    print(f"\n[INFO] Injecting historical messages to step {mistake_step} (including advertisement click operation)")
    for i, msg in enumerate(snap["history"][:mistake_step]):
        await feed(m1, msg)
        print(f"[INJECT] [{msg['role']}] {str(msg['content'])[:80].replace(chr(10),' ')} …")
    
    # Continue execution from injection point
    print(f"\n[INFO] Injected to advertisement click operation, now continuing execution...")
    full_history = snap["history"][:mistake_step].copy()
    
    try:
        # Continue execution, start reasoning from injection point
        async for msg in m1.run_stream(task=snap["question"]):
            role = getattr(msg, "role", getattr(msg, "source", "assistant"))
            content = getattr(msg, "content", "")
            print(f"[CONTINUE] [{role}] {str(content)[:110].replace(chr(10),' ')} …")
            full_history.append({"content": str(content), "role": role})
            
            # If task result is encountered, stop execution
            if getattr(msg, "type", "") == "task_result":
                print(f"\n[INFO] Task result encountered, stopping execution")
                break
                
    except Exception as e:
        print(f"\n[ERROR] Error during continued execution: {e}")
    
    return full_history, snap, mistake_step-1

def save_reproduction(sid, hist, snap, fail_step):
    """Save reproduced trajectory"""
    OUT_DIR.mkdir(parents=True, exist_ok=True)
    out = {
        "history"              : hist,
        "question"             : snap["question"],
        "ground_truth"         : snap.get("ground_truth",""),
        "original_failure_step": fail_step + 1,
        "reproduction_method"  : "exact_snapshot_restore",
        "mistake_agent"        : snap.get("mistake_agent",""),
        "mistake_step"         : snap.get("mistake_step",""),
        "mistake_reason"       : snap.get("mistake_reason",""),
        "reproduced_at"        : datetime.utcnow().isoformat()
    }
    p = OUT_DIR / f"scenario_{sid}_reproduced_exact.json"
    p.write_text(json.dumps(out, indent=2, ensure_ascii=False), encoding="utf-8")
    print(f"\n✓ Reproduced trajectory saved → {p}")
    print(f"Trajectory length: {len(hist)} (original: {len(snap['history'])})")

# --------------------------- CLI --------------------------------------------
async def main_async():
    pa = argparse.ArgumentParser(description="Tool for exact reproduction of failure scenarios")
    pa.add_argument("--scenario", required=True, help="Scenario ID, e.g. 1 or 6e3b...")
    pa.add_argument("--model", default="gpt-4o", help="Model to use")
    args = pa.parse_args()

    # Restore from snapshot
    hist, snap, fs = await restore_from_snapshot_exact(args.scenario, args.model)
    
    # Save reproduced trajectory
    save_reproduction(args.scenario, hist, snap, fs)

if __name__ == "__main__":
    asyncio.run(main_async()) 