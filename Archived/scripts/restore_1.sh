#!/bin/bash

# Day 2: Failure Reproduction Script for Scenario 1
# This script reproduces the failure scenario from the Hand-Crafted dataset

echo "=== Day 2: Failure Reproduction (Scenario 1) ==="
echo "Reproducing failure scenario from Hand-Crafted dataset..."

# Set scenario ID
SCENARIO_ID="1"
MODEL=${1:-"gpt-4o"}

echo "Scenario ID: $SCENARIO_ID"
echo "Model: $MODEL"
echo ""

# Check if the scenario file exists
SCENARIO_FILE="../Agents_Failure_Attribution/Who&When/Hand-Crafted/${SCENARIO_ID}.json"
if [ ! -f "$SCENARIO_FILE" ]; then
    echo "Error: Scenario file not found: $SCENARIO_FILE"
    exit 1
fi

echo "Found scenario file: $SCENARIO_FILE"
echo ""

# Create output directory
mkdir -p ../logs/reproduced

# Run the failure reproduction script
echo "Running failure reproduction..."
python day2_failure_reproduction.py --scenario $SCENARIO_ID --model $MODEL

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Day 2 execution completed successfully!"
    echo "Output saved to: ../logs/reproduced/scenario_${SCENARIO_ID}_reproduced_exact.json"
else
    echo ""
    echo "❌ Day 2 execution failed!"
    exit 1
fi

echo ""
echo "=== Day 2 Complete ===" 