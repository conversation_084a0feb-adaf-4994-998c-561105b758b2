#!/usr/bin/env python3
"""
Day 1: End-to-End Query Execution Script
========================================

This script implements Day 1 of the Multi-Agent Failure Attribution Task.
It sets up the Magnetic-One system and runs a complete end-to-end query
execution to generate trace logs for analysis.

Usage:
    python day1_end_to_end_execution.py --scenario 1
"""

import os
import json
import argparse
import asyncio
from pathlib import Path
from typing import Any

# -----------------------------------------------------------
# AutoGen / Magnetic-One
# -----------------------------------------------------------
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne

# -----------------------------------------------------------
# OpenAI Configuration
# -----------------------------------------------------------
API_KEY = os.getenv("OPENAI_API_KEY")
if not API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable must be set")

API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")

# -----------------------------------------------------------
# Tool Functions
# -----------------------------------------------------------
DATA_DIR = Path("../Agents_Failure_Attribution/Who&When/Hand-Crafted")
OUT_DIR = Path("../logs/generated")


def load_scenario(scenario_id: str) -> dict[str, Any]:
    """Load scenario JSON file and return as dictionary."""
    path = DATA_DIR / f"{scenario_id}.json"
    if not path.exists():
        raise FileNotFoundError(f"Scenario file not found: {path}")
    with path.open(encoding="utf-8") as f:
        return json.load(f)


def save_trace(scenario_id: str, trace: dict[str, Any]) -> None:
    """Save trace to ../logs/generated/scenario_{id}.json"""
    OUT_DIR.mkdir(parents=True, exist_ok=True)
    out_path = OUT_DIR / f"scenario_{scenario_id}.json"
    with out_path.open("w", encoding="utf-8") as f:
        json.dump(trace, f, indent=2, ensure_ascii=False)
    print(f"✓ Trace saved to {out_path}")


# -----------------------------------------------------------
# Core: Run Magnetic-One and Capture Full Process Messages
# -----------------------------------------------------------
async def run_m1(query: str) -> list[dict[str, str]]:
    """Run Magnetic-One system and capture complete message history."""
    client = OpenAIChatCompletionClient(
        model="gpt-4o",
        api_key=API_KEY,
        base_url=API_BASE,
    )
    m1 = MagenticOne(client=client)

    history: list[dict[str, str]] = [{"content": query, "role": "human"}]

    async for item in m1.run_stream(task=query):
        if not hasattr(item, "content"):
            continue  # Skip TaskResult and other non-message objects

        role = getattr(item, "role", None) or getattr(item, "source", "") or "assistant"
        content_raw = item.content
        content_str = content_raw if isinstance(content_raw, str) else str(content_raw)

        # Terminal preview
        preview = content_str[:120].replace('\n', ' ')
        print(f"[{role}] {preview}...")

        history.append({"content": content_str, "role": role})

    return history


# -----------------------------------------------------------
# CLI Main Entry Point
# -----------------------------------------------------------
async def main_async() -> None:
    """Main async function for CLI execution."""
    parser = argparse.ArgumentParser(description="Day 1: End-to-End Magnetic-One Trace Capture")
    parser.add_argument("--scenario", required=True, help="Scenario ID (e.g. 1)")
    args = parser.parse_args()

    # Load scenario and extract query
    scen = load_scenario(args.scenario)
    query = scen["question"]
    ground_truth = scen.get("ground_truth", "")

    print(f"\n▶ Day 1: End-to-End Execution")
    print(f"▶ Query: {query}\n")

    # Execute query through Magnetic-One
    history = await run_m1(query)

    # Create trace object
    trace = {
        "history": history,
        "question": query,
        "ground_truth": ground_truth,   
        "question_ID": args.scenario,
        "execution_type": "day1_end_to_end",
        "timestamp": str(asyncio.get_event_loop().time()),
    }

    # Save trace
    save_trace(args.scenario, trace)
    print(f"✓ Day 1 execution completed")
    print(f"✓ Trace length: {len(history)} steps\n")


if __name__ == "__main__":
    asyncio.run(main_async()) 