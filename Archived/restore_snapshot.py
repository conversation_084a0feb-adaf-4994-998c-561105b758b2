#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
restore_snapshot.py
~~~~~~~~~~~~~~~~~~
从特定的失败快照恢复运行，重现失败场景

用法：
    python restore_snapshot.py --scenario 1 --model gpt-3.5-turbo
"""

import os, re, json, argparse, asyncio
from pathlib import Path
from typing import Any, List

# --------------------------- AutoGen -----------------------------------------
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_agentchat.messages import TextMessage

# --------------------------- API 设置 ----------------------------------------
API_KEY = os.getenv("OPENAI_API_KEY")
if not API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable must be set")
API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")

# --------------------------- 路径 -------------------------------------------
DATA_DIR = Path("../Agents_Failure_Attribution/Who&When/Hand-Crafted")
OUT_DIR  = Path("../logs/reproduced")

# --------------------------- 工具 -------------------------------------------
ROLE_MAP = {"human": "User", "assistant": "Assistant"}
_drop = re.compile(r"<Image>|Automatic OCR|meta_tags", re.I)

def norm(r:str)->str: return ROLE_MAP.get(r.split("(")[0].strip(), r)
def clean(t:str, k:int=3000)->str: return "" if _drop.search(t) else t[:k]

def to_msgs(hist:List[dict])->List[TextMessage]:
    return [
        TextMessage(content=clean(h["content"]),
                    source=norm(h["role"]))
        for h in hist if clean(h["content"])
    ]

# --------------------------- 从快照恢复 ---------------------------------------
async def restore_from_snapshot(sid:str, model:str):
    """从快照恢复运行，重现失败场景"""
    snap_path = DATA_DIR / f"{sid}.json"
    snap:dict[str,Any] = json.loads(snap_path.read_text(encoding="utf-8"))
    
    # 从快照中获取失败步数
    fail_step = int(snap.get("mistake_step", len(snap["history"]))) - 1
    
    # 从历史记录中提取到失败点之前的消息
    init_hist = to_msgs(snap["history"][:fail_step])
    
    client = OpenAIChatCompletionClient(
        model=model,
        api_key=API_KEY,
        base_url=API_BASE or None,
        temperature=0,
        model_extras={"max_tokens":256},
    )
    
    # 初始化MagenticOne，忽略警告
    import warnings
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        m1 = MagenticOne(client=client)
    
    # 检查run_stream方法是否接受init_messages参数
    kwargs = ({"init_messages": init_hist}
              if "init_messages" in m1.run_stream.__code__.co_varnames else {})
    
    # 从失败点开始运行
    hist = snap["history"][:fail_step]
    print(f"\n[INFO] 从快照恢复运行，从第{fail_step}步开始（共{len(snap['history'])}步）")
    print(f"[INFO] 失败原因: {snap.get('mistake_reason', '未知')}")
    print(f"[INFO] 失败代理: {snap.get('mistake_agent', '未知')}")
    print(f"[INFO] 最后一条消息: [{hist[-1]['role']}] {hist[-1]['content'][:50]}...\n")
    
    try:
        async for msg in m1.run_stream(task=snap["question"], **kwargs):
            role = getattr(msg, "role", getattr(msg, "source", "assistant"))
            content = getattr(msg, "content", "")
            print(f"[{role}] {str(content)[:110].replace(chr(10),' ')} …")
            hist.append({"content": str(content), "role": role})
            if getattr(msg, "type", "") == "task_result":
                break
    except Exception as e:
        print(f"\n[ERROR] 运行过程中出错: {e}")
    
    return hist, snap, fail_step

def save_reproduction(sid, hist, snap, fail_step):
    """保存重现的轨迹"""
    OUT_DIR.mkdir(parents=True, exist_ok=True)
    out = {
        "history"              : hist,
        "question"             : snap["question"],
        "ground_truth"         : snap.get("ground_truth",""),
        "original_failure_step": fail_step + 1,
        "reproduction_method"  : "snapshot_restore",
        "mistake_agent"        : snap.get("mistake_agent",""),
        "mistake_step"         : snap.get("mistake_step",""),
        "mistake_reason"       : snap.get("mistake_reason","")
    }
    p = OUT_DIR / f"scenario_{sid}_reproduced.json"
    p.write_text(json.dumps(out, indent=2, ensure_ascii=False), encoding="utf-8")
    print(f"\n✓ 重现轨迹已保存 → {p}")
    print(f"轨迹长度: {len(hist)} (原始: {len(snap['history'])})")

# --------------------------- CLI --------------------------------------------
async def main_async():
    pa = argparse.ArgumentParser()
    pa.add_argument("--scenario", required=True, help="场景ID，如 1 或 6e3b...")
    pa.add_argument("--model", default="gpt-3.5-turbo", help="使用的模型")
    args = pa.parse_args()

    # 从快照恢复运行
    hist, snap, fs = await restore_from_snapshot(args.scenario, args.model)
    
    # 保存重现轨迹
    save_reproduction(args.scenario, hist, snap, fs)

if __name__ == "__main__":
    asyncio.run(main_async()) 