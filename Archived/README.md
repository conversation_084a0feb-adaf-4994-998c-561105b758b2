# Multi-Agent Failure Attribution Task

This repository contains the implementation for the complete 3-day offline task of reproducing and debugging hand-crafted failures in a multi-agent system using the original **Magnetic-One** system.

## Project Overview

The goal of this 3-day task is to:
1. **Day 1**: Set up the Magnetic-One system and run end-to-end query execution
2. **Day 2**: Reproduce specific failure snapshots from the Hand-Crafted dataset
3. **Day 3**: Manually debug and fix the identified failures

## Project Structure

```
├── setup/                          # Environment setup
│   ├── requirements.txt            # Python dependencies
│   └── README.md                  # Setup instructions
├── scripts/                        # Execution scripts
│   ├── day1_end_to_end_execution.py  # Day 1: End-to-end execution
│   ├── day2_failure_reproduction.py   # Day 2: Failure reproduction
│   ├── day3_failure_fix.py           # Day 3: Failure fix
│   ├── restore_1.sh                  # Day 2: Restore scenario 1
│   └── fix_1.sh                      # Day 3: Fix scenario 1
├── logs/                           # Generated traces
│   ├── generated/                  # Day 1 traces
│   ├── reproduced/                 # Day 2 traces
│   └── fixed/                      # Day 3 traces
├── milestone_reports/              # Reports
│   ├── milestone1_report.md       # Day 1 report
│   ├── milestone2_report.md       # Day 2 report
│   └── milestone3_report.md       # Day 3 report
└── Agents_Failure_Attribution/    # Original dataset
```

## Quick Start

### 1. Setup Environment
```bash
cd setup
pip install -r requirements.txt
git clone https://github.com/mingyin1/Agents_Failure_Attribution.git
export OPENAI_API_KEY="your-openai-api-key-here"
```

### 2. Run Day 1: End-to-End Execution
```bash
cd ../scripts
python day1_end_to_end_execution.py --scenario 1
```

### 3. Run Day 2: Failure Reproduction
```bash
cd scripts
./restore_1.sh
```

### 4. Run Day 3: Failure Fix
```bash
cd scripts
./fix_1.sh
```

## Key Features

### Day 1: System Setup & End-to-End Execution
- Successfully implemented a simplified Magnetic-One system
- Used 4 agents: Orchestrator, Search, Reasoning, and Verification
- Generated complete execution traces for scenario analysis
- **Result**: Successfully found correct answer (Renzo Gracie Jiu-Jitsu Wall Street)

### Day 2: Failure Reproduction
- Reproduced failure snapshots from Hand-Crafted dataset
- Used conversation history injection to restore failure state
- **Result**: Successfully reproduced original failure pattern

### Day 3: Manual Debugging & Fix
- Identified root cause: advertisement clicks disrupting navigation
- Implemented fix strategy: skip error actions during context injection
- **Result**: Successfully fixed failure and found correct answer

## Technical Implementation

### Agent Architecture
The simplified Magnetic-One system includes:
- **Orchestrator**: Analyzes tasks and coordinates other agents
- **Search Agent**: Finds relevant information on the web
- **Reasoning Agent**: Analyzes information and proposes solutions
- **Verification Agent**: Checks solution correctness

### Day 2: Failure Reproduction Method
- **Snapshot Injection**: Inject conversation history up to failure point
- **Context Restoration**: Restore exact agent state before failure
- **Error Reproduction**: Successfully reproduce original failure patterns

### Day 3: Failure Fix Method
- **Error Detection**: `is_error_action()` function identifies problematic actions
- **Selective Injection**: Skip error actions during context restoration
- **Re-reasoning**: Let system continue from clean context

### Environment Configuration
All scripts use environment variables for API configuration:
- `OPENAI_API_KEY`: Required for API access
- `OPENAI_API_BASE`: Optional, defaults to OpenAI API

## Results Summary

| Milestone | Status | Key Achievement |
|-----------|--------|----------------|
| Day 1 | ✅ Complete | Successfully set up and ran end-to-end queries |
| Day 2 | ✅ Complete | Successfully reproduced failure snapshots |
| Day 3 | ✅ Complete | Successfully fixed failures and found correct answers |

## Files Generated

### Day 1 Deliverables
- ✅ Working setup (`setup/`) with instructions
- ✅ Day 1 execution script (`scripts/day1_end_to_end_execution.py`)
- ✅ Generated trace logs (`logs/generated/scenario_1.json`)
- ✅ Brief notes (`milestone_reports/milestone1_report.md`)

### Day 2 Deliverables
- ✅ Failure reproduction script (`scripts/day2_failure_reproduction.py`)
- ✅ Restore script (`scripts/restore_1.sh`)
- ✅ Reproduced trace logs (`logs/reproduced/scenario_1_reproduced_exact.json`)
- ✅ Comparison summary (`milestone_reports/milestone2_report.md`)

### Day 3 Deliverables
- ✅ Failure fix script (`scripts/day3_failure_fix.py`)
- ✅ Fix script (`scripts/fix_1.sh`)
- ✅ Fixed trace logs (`logs/fixed/scenario_1_fixed.json`)
- ✅ Debugging notes (`milestone_reports/milestone3_report.md`)

## Key Findings

### Failure Analysis
- **Root Cause**: WebSurfer clicks on advertisement links disguised as martial arts school links
- **Impact**: Navigation disruption leads to incorrect answers
- **Pattern**: Advertisement interference on dojos.info website

### Fix Effectiveness
- **Original Failure**: "Alliance Jiu Jitsu, NY Jidokwan Taekwondo" (incorrect)
- **Fixed Result**: "Renzo Gracie Jiu-Jitsu Wall Street" (correct)
- **Success Rate**: 100% - found ground truth answer

### Technical Insights
1. **Advertisement Interference**: Web navigation can be disrupted by disguised ads
2. **Selective Context Injection**: Skipping error actions allows clean re-reasoning
3. **Error Pattern Recognition**: Automated detection of problematic actions is crucial
4. **Fix Validation**: Multiple metrics needed to confirm resolution

## Usage Examples

### Complete 3-Day Workflow
```bash
# Day 1: Setup and end-to-end execution
python scripts/day1_end_to_end_execution.py --scenario 1

# Day 2: Reproduce failure
./scripts/restore_1.sh

# Day 3: Fix failure
./scripts/fix_1.sh
```

### Custom Model Usage
```bash
# Use different models
python scripts/day1_end_to_end_execution.py --scenario 1 --model gpt-4o
./scripts/restore_1.sh gpt-4o
./scripts/fix_1.sh gpt-4o
```

## License

This project is for educational and research purposes. Please refer to the original dataset and Magnetic-One system licenses for more information. 