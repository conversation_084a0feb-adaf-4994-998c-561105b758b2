# 2025.07.25 工作记录

## 今日进展

1. autogen 集成了 tiktoken 计数功能。
2. tiktoken 支持的模型包括：gpt-4, gpt-3.5-turbo, gpt-4o 等 OpenAI 模型，以及一些老的 OpenAI 模型如 da<PERSON><PERSON>, curie, ada 等。
3. 但 tiktoken 不支持 llama3.1（Meta）、llama2（Meta）、claude-3（Anthropic）等非 OpenAI 的模型。
4. 只有 OpenAI 的模型不会报警告，其他模型会触发警告。

---

## 备注
- 日志和 trace 文件已自动保存到 `../logs/generated/` 目录。
- 运行命令示例：
  ```bash
  python scripts/run_m1_test.py --scenario 1 --endpoint_type ollama --model llama3.1
  ``` 