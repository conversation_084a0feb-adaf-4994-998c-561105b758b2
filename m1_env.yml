name: m1
channels:
  - defaults
  - conda-forge
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - asttokens=3.0.0=pyhd8ed1ab_1
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.7.14=hbd8a1cb_0
  - comm=0.2.3=pyhe01879c_0
  - debugpy=1.8.11=py311h6a678d5_0
  - decorator=5.2.1=pyhd8ed1ab_0
  - exceptiongroup=1.3.0=pyhd8ed1ab_0
  - executing=2.2.0=pyhd8ed1ab_0
  - expat=2.7.1=h6a678d5_0
  - importlib-metadata=8.7.0=pyhe01879c_1
  - ipykernel=6.30.0=pyh82676e8_0
  - ipython=9.4.0=pyhfa0c392_0
  - ipython_pygments_lexers=1.1.1=pyhd8ed1ab_0
  - jedi=0.19.2=pyhd8ed1ab_1
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.8.1=pyh31011fe_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libsodium=1.0.18=h36c2ea0_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - libxcb=1.17.0=h9b100fa_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - openssl=3.0.17=h5eee18b_0
  - packaging=25.0=pyh29332c3_1
  - parso=0.8.4=pyhd8ed1ab_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pip=25.1=pyhc872135_2
  - platformdirs=4.3.8=pyhe01879c_0
  - prompt-toolkit=3.0.51=pyha770c72_0
  - psutil=5.9.0=py311h5eee18b_1
  - pthread-stubs=0.3=h0ce48e5_1
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pygments=2.19.2=pyhd8ed1ab_0
  - python=3.11.13=h1a3bd86_0
  - python-dateutil=2.9.0.post0=pyhe01879c_2
  - pyzmq=26.2.0=py311h6a678d5_0
  - readline=8.2=h5eee18b_0
  - setuptools=78.1.1=py311h06a4308_0
  - six=1.17.0=pyhe01879c_1
  - sqlite=3.50.2=hb25bd0a_1
  - stack_data=0.6.3=pyhd8ed1ab_1
  - tk=8.6.14=h993c535_1
  - tornado=6.5.1=py311h5eee18b_0
  - traitlets=5.14.3=pyhd8ed1ab_1
  - typing_extensions=4.14.1=pyhe01879c_0
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.45.1=py311h06a4308_0
  - xorg-libx11=1.8.12=h9b100fa_1
  - xorg-libxau=1.0.12=h9b100fa_0
  - xorg-libxdmcp=1.1.5=h9b100fa_0
  - xorg-xorgproto=2024.1=h5eee18b_1
  - xz=5.6.4=h5eee18b_1
  - zeromq=4.3.5=h6a678d5_0
  - zipp=3.23.0=pyhd8ed1ab_0
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - aiofiles==24.1.0
      - altair==5.5.0
      - annotated-types==0.7.0
      - anyio==4.9.0
      - attrs==25.3.0
      - autogen-agentchat==0.7.1
      - autogen-core==0.7.1
      - autogen-ext==0.6.4
      - azure-core==1.35.0
      - azure-identity==1.23.1
      - azure-identity-broker==1.2.0
      - backoff==2.2.1
      - bcrypt==4.3.0
      - beautifulsoup4==4.13.4
      - blinker==1.9.0
      - build==1.3.0
      - cachetools==5.5.2
      - certifi==2025.7.14
      - cffi==1.17.1
      - charset-normalizer==3.4.2
      - chromadb==1.0.15
      - click==8.2.2
      - coloredlogs==15.0.1
      - cryptography==45.0.5
      - defusedxml==0.7.1
      - distro==1.9.0
      - durationpy==0.10
      - filelock==3.18.0
      - flatbuffers==25.2.10
      - fsspec==2025.7.0
      - gitdb==4.0.12
      - gitpython==3.1.45
      - google-auth==2.40.3
      - googleapis-common-protos==1.70.0
      - greenlet==3.2.3
      - grpcio==1.74.0
      - h11==0.16.0
      - hf-xet==1.1.5
      - httpcore==1.0.9
      - httptools==0.6.4
      - httpx==0.28.1
      - huggingface-hub==0.34.3
      - humanfriendly==10.0
      - idna==3.10
      - importlib-resources==6.5.2
      - jinja2==3.1.6
      - jiter==0.10.0
      - jsonpickle==4.1.1
      - jsonref==1.1.0
      - jsonschema==4.25.0
      - jsonschema-specifications==2025.4.1
      - kubernetes==33.1.0
      - magika==0.6.2
      - markdown-it-py==3.0.0
      - markdownify==1.1.0
      - markitdown==0.1.2
      - markupsafe==3.0.2
      - mdurl==0.1.2
      - mmh3==5.2.0
      - mpmath==1.3.0
      - msal==1.33.0
      - msal-extensions==1.3.1
      - narwhals==2.0.1
      - networkx==3.5
      - numpy==2.3.2
      - oauthlib==3.3.1
      - ollama==0.5.1
      - onnxruntime==1.22.1
      - openai==1.98.0
      - opentelemetry-api==1.36.0
      - opentelemetry-exporter-otlp-proto-common==1.36.0
      - opentelemetry-exporter-otlp-proto-grpc==1.36.0
      - opentelemetry-proto==1.36.0
      - opentelemetry-sdk==1.36.0
      - opentelemetry-semantic-conventions==0.57b0
      - orjson==3.11.1
      - overrides==7.7.0
      - pandas==2.3.1
      - pillow==11.3.0
      - playwright==1.54.0
      - plotly==6.2.0
      - posthog==5.4.0
      - protobuf==5.29.5
      - pyarrow==21.0.0
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.2
      - pybase64==1.4.2
      - pycparser==2.22
      - pydantic==2.11.7
      - pydantic-core==2.33.2
      - pydeck==0.9.1
      - pyee==13.0.0
      - pyjwt==2.10.1
      - pymsalruntime==0.18.1
      - pypika==0.48.9
      - pyproject-hooks==1.2.0
      - python-dotenv==1.1.1
      - pytz==2025.2
      - pyvis==0.3.2
      - pyyaml==6.0.2
      - referencing==0.36.2
      - regex==2025.7.34
      - requests==2.32.4
      - requests-oauthlib==2.0.0
      - rich==14.1.0
      - rpds-py==0.26.0
      - rsa==4.9.1
      - shellingham==1.5.4
      - smmap==5.0.2
      - sniffio==1.3.1
      - soupsieve==2.7
      - streamlit==1.47.1
      - sympy==1.14.0
      - tenacity==9.1.2
      - tiktoken==0.9.0
      - tokenizers==0.20.3
      - toml==0.10.2
      - tqdm==4.67.1
      - typer==0.16.0
      - typing-inspection==0.4.1
      - tzdata==2025.2
      - urllib3==2.5.0
      - uvicorn==0.35.0
      - uvloop==0.21.0
      - watchdog==6.0.0
      - watchfiles==1.1.0
      - websocket-client==1.8.0
      - websockets==15.0.1
prefix: /home/<USER>/.conda/envs/m1
